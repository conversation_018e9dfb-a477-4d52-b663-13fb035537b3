---
type: "always_apply"
---

# 项目规则与结构概述

## 1. 基本信息

*   **项目名称**: niushop
*   **框架**: ThinkPHP 6.0
*   **主要语言**: PHP (>= 7.1.0)
*   **版本控制**: Git

## 2. 项目结构

*   **`app/`**: 核心应用代码
    *   `api/`: API 模块
    *   `shop/`: 商城业务模块
    *   `pay/`: 支付模块
    *   `model/`: 数据模型
    *   `controller/`: (隐式，通常在各模块下) 控制器
    *   `common.php`: 全局公共函数
    *   ... (其他核心组件)
*   **`addon/`**: 插件扩展目录
*   **`config/`**: 配置文件目录
*   **`public/`**: Web 访问入口 (包含 `index.php`)
*   **`vendor/`**: Composer 依赖库
*   **`runtime/`**: 运行时文件 (缓存、日志)
*   **`extend/`**: 自定义扩展库
*   **`h5/`**: (推测) H5 前端资源
*   **`composer.json`**: PHP 依赖管理
*   **`.gitignore`**: Git 忽略配置

## 3. 主要技术栈与依赖

*   **后端框架**: ThinkPHP 6.0
*   **ORM**: `topthink/think-orm`
*   **多应用支持**: `topthink/think-multi-app`
*   **视图引擎**: `topthink/think-view`
*   **微信开发**: `overtrue/wechat`
*   **Excel 处理**: `phpoffice/phpexcel`
*   **短信服务**: `overtrue/easy-sms`
*   **邮件服务**: `phpmailer/phpmailer`
*   **图像处理**: `intervention/image`
*   **日期时间**: `nesbot/carbon`
*   **云存储**: `thans/thinkphp-filesystem-cloud`
*   **PHP 版本**: >= 7.1.0

## 4. 开发规范 (初步推测)

*   遵循 ThinkPHP 官方开发规范。
*   模块化开发，各功能模块应职责清晰、低耦合。
*   代码注释清晰，易于理解。
*   API 设计可能遵循 RESTful 风格。
*   使用 Composer 管理第三方库依赖。
*   前后端分离架构 (基于入口文件的跨域设置推测)。

## 5. 启动与配置

*   Web 服务器需配置 `public` 目录为根目录。
*   入口文件为 `public/index.php`。
*   配置文件位于 `config/` 目录下。
*   项目包含安装流程 (通过 `install.php` 和 `install.lock` 推测)。

## 6. 注意事项

*   入口文件 `index.php` 中包含一些基础的安全防护措施。
*   项目支持插件化扩展。

## 7. 数据库
部分的数据库表信息可以从根目录的update.sql文件中了解到，如果文件中没有需要的表结构信息，可以主动向用户询问。