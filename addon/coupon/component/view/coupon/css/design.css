@CHARSET "UTF-8";


/* 弹框样式 */
.coupon-wrap .coupon-list-style {
	display: none;
}

.style-list-con-coupon {
	display: flex;
	flex-wrap: wrap;
}
.style-list-con-coupon .style-li-coupon {
	width: 32%;
	height: 300px;
	line-height: 300px;
	margin-right: 2%;
	margin-bottom: 15px;
	cursor: pointer;
	border: 1px solid #ededed;
	background: #f7f8fa;
	box-sizing: border-box;
	text-align: center;
}

.style-list-con-coupon .style-li-coupon:nth-child(3n) {
	margin-right: 0;
}

.style-list-con-coupon .style-li-coupon img {
	max-width: 100%;
	max-height: 100%;
}

.layui-layer-page .layui-layer-content {
	overflow: auto !important;
}

.btn-box {
	margin-top: 30px;
	text-align: center;
}


/* 风格一 */
.coupon-wrap .coupon-box {
	width: 100%;
	overflow: hidden;
	display: flex;
	flex-wrap: nowrap;
}

.coupon-wrap .coupon-box .coupon {
	display: inline-block;
	width: 140px;
	margin-right: 10px;
	position: relative;
}

.coupon-wrap .coupon-box .coupon img {
	width: 100%;
}

.coupon-wrap .coupon-box .coupon .coupon-intro {
	position: absolute;
	top: 10px;
	width: 103px;
	text-align: center;
	font-size: 12px;
}

.coupon-wrap .coupon-box .coupon .coupon-intro .coupon-price {
	margin-bottom: 3px;
}

.coupon-wrap .coupon-box .coupon .coupon-intro .coupon-price span {
	font-size: 20px;
}

.coupon-wrap .coupon-box .coupon .coupon-btn {
	position: absolute;
	bottom: 12px;
	right: 6px;
	width: 20px;
	font-size: 14px;
	line-height: 18px;
	text-align: center;
}

/* 风格二 */
.coupon-box-2 {
	height: 150px;
	background: url(../img/style2-bg-1.png) no-repeat;
	background-size: 100% 100%;
	padding: 8px;
	box-sizing: border-box;
}

.coupon-block {
	width: 100%;
	display: flex;
	overflow: hidden;
}

.coupon-wrap .coupon-box-2 .coupon {
	width: 110px;
	height: 135px;
	margin-right: 10px;
	text-align: center;
	flex-shrink: 0;
}

.coupon-wrap .coupon-box-2 .coupon .coupon-intro {
	width: 100%;
	top: 15px;
}

.coupon-wrap .coupon-box-2 .coupon .coupon-intro .coupon-price span {
	font-size: 24px;
}

.coupon-box-2 .coupon .coupon-desc {
	margin: 5px 0;
}

.coupon-box-2 .coupon .coupon-info {
	color: #777777;
	margin-top: 5px;
}

.coupon-wrap .coupon-box-2 .coupon .coupon-btn {
	width: 66px;
	bottom: 10px;
	left: 50%;
	margin-left: -33px;
	font-size: 12px;
	color: #FFFFFF;
	line-height: 28px;
	background-color: #FF4544;
	border-radius: 30px;
}