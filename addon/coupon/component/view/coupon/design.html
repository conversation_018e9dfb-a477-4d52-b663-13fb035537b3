<nc-component v-bind:data="data[index]" class="coupon-wrap" v-bind:style="{ backgroundColor : nc.backgroundColor }">

	<!-- 预览 -->
	<template slot="preview">
		<div class="coupon-preview" v-bind:style="{ padding: (nc.padding + 'px 0') }">
			<div class="coupon-box" v-if="nc.style == 1">
				<div class="coupon">
					<img src="{$resource_path}/coupon/img/coupon_bg.png">
					<div class="coupon-intro">
						<p class="coupon-price ns-red-color">￥<span>500.00</span></p>
						<p class="coupon-desc ns-red-color">满3000元可用</p>
					</div>
					<div class="coupon-btn ns-red-color">领取</div>
				</div>
				
				<div class="coupon">
					<img src="{$resource_path}/coupon/img/coupon_bg.png">
					<div class="coupon-intro">
						<p class="coupon-price ns-red-color">￥<span>500.00</span></p>
						<p class="coupon-desc ns-red-color">满3000元可用</p>
					</div>
					<div class="coupon-btn ns-red-color">领取</div>
				</div>
			</div>
			
			<div class="coupon-box" v-bind:class="'coupon-box-'+ nc.style" v-if="nc.style == 2">
				<div class="coupon-block">
					<div class="coupon">
						<img src="{$resource_path}/coupon/img/style2-bg-2.png">
						<div class="coupon-intro">
							<p class="coupon-price ns-red-color">￥<span>500.00</span></p>
							<p class="coupon-desc ns-red-color">满3000元可用</p>
							<p class="coupon-info">指定商品可用</p>
						</div>
						<div class="coupon-btn">立即抢</div>
					</div>
					
					<div class="coupon">
						<img src="{$resource_path}/coupon/img/style2-bg-2.png">
						<div class="coupon-intro">
							<p class="coupon-price ns-red-color">￥<span>500.00</span></p>
							<p class="coupon-desc ns-red-color">满3000元可用</p>
							<p class="coupon-info">指定商品可用</p>
						</div>
						<div class="coupon-btn">立即抢</div>
					</div>
					
					<div class="coupon">
						<img src="{$resource_path}/coupon/img/style2-bg-2.png">
						<div class="coupon-intro">
							<p class="coupon-price ns-red-color">￥<span>500.00</span></p>
							<p class="coupon-desc ns-red-color">满3000元可用</p>
							<p class="coupon-info">指定商品可用</p>
						</div>
						<div class="coupon-btn">立即抢</div>
					</div>
				</div>
			</div>
		</div>
	</template>

	<!-- 编辑 -->
	<template slot="edit">
		
		<template v-if="nc.lazyLoad">
			<coupon-list></coupon-list>
			<coupon-style></coupon-style>
		</template>
		
<!--		<color v-bind:data="{ field : 'backgroundColor', 'label' : '背景颜色' }"></color>-->
		<slide v-bind:data="{ field : 'padding', label : '上下边距' }"></slide>
		
		<!-- <template v-if="nc.lazyLoad">
			<coupon-status></coupon-status>
		</template> -->

		<!-- 弹框 -->
		<div class="coupon-list-style">
			<div class="style-list-coupon layui-form">
				<div class="style-list-con-coupon">
					<div class="style-li-coupon" v-bind:class="{'selected ns-border-color': nc.style == 1}">
						<img src="{$resource_path}/coupon/img/style1.png" />
					</div>
					
					<div class="style-li-coupon" v-bind:class="{'selected ns-border-color': nc.style == 2}">
						<img src="{$resource_path}/coupon/img/style2.png" />
					</div>
				</div>

				<input type="hidden" name="style">
			</div>
		</div>
	
	</template>
	
	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/coupon/css/design.css"></css>
		<js src="{$resource_path}/coupon/js/design.js"></js>
		
	</template>

</nc-component>