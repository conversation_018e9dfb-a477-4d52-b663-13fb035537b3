{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.layui-form-item .layui-input-inline.end-time{
		float: none;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>管理员可以在此页添加限时折扣活动</li>
			<li>活动时间不可与列表中的折扣活动时间冲突</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="discount_name" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
		<div class="ns-word-aux">
			<p>活动名称将显示在限时折扣活动列表中，方便商家管理使用</p>
		</div>
	</div>

	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea ns-len-long"></textarea>
		</div>
		<div class="ns-word-aux">
			<p>备注是商家对限时折扣活动的补充说明文字，在商品详情页-优惠信息位置显示；非必填选项</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>开始时间：</label>
		<div class="layui-input-inline">
			<input type="text" id="start_time" name="start_time" lay-verify="required" class="layui-input ns-len-mid" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>结束时间：</label>
		<div class="layui-input-inline end-time">
			<input type="text" id="end_time" name="end_time" lay-verify="required|time" class="layui-input ns-len-mid" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>
		<div class="ns-word-aux">
			<p>结束时间不能小于开始时间，也不能小于当前时间</p>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>
{/block}
{block name="script" }
<script>
	layui.use(['form', 'laydate'], function() {
		var form = layui.form,
			laydate = layui.laydate,
			repeat_flag = false, //防重复标识
			currentDate = new Date(),
			minDate = "";
		form.render();

		currentDate.setDate(currentDate.getDate() + 30);

		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime',
			value: new Date(),
			done: function(value) {
				minDate = value;
				reRender();
			}
		});

		//结束时间
		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime',
			value: new Date(currentDate)
		});

		/**
		 * 重新渲染结束时间
		 * */
		function reRender() {
			$("#end_time").remove();
			$(".end-time").html('<input type="text" id="end_time" name="end_time" placeholder="请输入结束时间" lay-verify="required|time" class = "layui-input ns-len-mid" autocomplete="off"> ');
			laydate.render({
				elem: '#end_time',
				type: 'datetime',
				min: minDate
			});
		}

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				url: ns.url("discount://shop/discount/add"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('添加成功', {
							title: '操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function() {
								location.href = ns.url("discount://shop/discount/lists")
							},
							btn2: function() {
								location.href = ns.url("discount://shop/discount/add");
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			});
		});

		/**
		 * 表单验证
		 */
		form.verify({
			time: function(value) {
				var now_time = (new Date()).getTime();
				var start_time = (new Date($("#start_time").val())).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
				if (start_time > end_time) {
					return '结束时间不能小于开始时间!';
				}
			}
		});
	});

	function back() {
		location.href = ns.url("discount://shop/discount/lists");
	}
</script>
{/block}