{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.layui-form-item .layui-input-inline.end-time{
		float: none;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>管理员可以在此页添加限时折扣活动。</li>
			<li>活动时间不可与列表中的折扣活动时间冲突。</li>
			<li>当活动状态为进行中时，不可以对时间进行编辑。</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="discount_name" value="{$discount_info.discount_name}" autocomplete="off" class="layui-input ns-len-long">
		</div>
		<div class="ns-word-aux">
			<p>活动名称将显示在限时折扣活动列表中，方便商家管理使用</p>
		</div>
	</div>

	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea ns-len-long">{$discount_info.remark}</textarea>
		</div>
		<div class="ns-word-aux">
			<p>备注是商家对限时折扣活动的补充说明文字，在商品详情页-优惠信息位置显示；非必填选项</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>开始时间：</label>
		<div class="layui-input-inline ns-len-mid">
			<input {if condition="$discount_info.status == 1" }disabled {/if} type="text" id="start_time" name="start_time"
			 value="{:date('Y-m-d H:i:s', $discount_info.start_time)}" autocomplete="off" class="layui-input">
			<i class="ns-calendar"></i>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>结束时间：</label>
		<div class="layui-input-inline ns-len-mid end-time">
			<input {if condition="$discount_info.status == 1" }disabled {/if} type="text" id="end_time" name="end_time" value="{:date('Y-m-d H:i:s', $discount_info.end_time)}"
			 autocomplete="off" class="layui-input">
			<i class="ns-calendar"></i>
		</div>
		<div class="ns-word-aux">
			<p>结束时间不能小于开始时间，也不能小于当前时间</p>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div> 
	
	<input type="hidden" name="discount_id" value="{$discount_info.discount_id}" />
</div>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'laydate'], function() {

		var form = layui.form,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识

		form.render();

		// 时间模块
		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime'
		});

		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime'
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				url: ns.url("discount://shop/discount/edit"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title: '操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function() {
								location.href = ns.url("discount://shop/discount/lists")
							},
							btn2: function() {
								location.reload();
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			});
		});

	});
	
	function back() {
		location.href = ns.url("discount://shop/discount/lists");
	}
</script>
{/block}