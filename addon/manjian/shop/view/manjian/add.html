{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.ns-discount { display: flex; justify-content: space-between; height: 34px; line-height: 34px; padding: 5px 15px; background-color: #F6FBFD; border: 1px dashed #BCE8F1; }
	.manjian-rule {}
	.manjian-rule .level-head{display: flex;justify-content: space-between;background: #eee;padding: 0 10px;margin-bottom: 15px;}
	.manjian-rule .title { color: #454545;font-weight: 600; }
	.manjian-rule .wrap .layui-form-label { width: 140px; }
	.manjian-rule .wrap .layui-form-label + .layui-input-block { margin-left: 140px }
	.manjian-rule .wrap .layui-form-checkbox[lay-skin=primary] {margin-top: 0}
	.manjian-rule .wrap .discount-cont {padding-left: 28px;min-height: 36px}
	.manjian-rule .discount-item .ns-word-aux {margin-left: 0}
	.layui-form-item .layui-input-inline.end-time{
		float: none;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>满减活动包括店铺所有商品，活动时间不能和已有活动重叠</li>
			<li>每添加一个规则都需点击确定规则设置按钮，生成一条规则，提交之后才可成功添加</li>
			<li>点击确定规则设置按钮生成的规则都有一个删除按钮，如不需要该条规则点击删除按钮即可删除</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="manjian_name" lay-verify="required|len" class="layui-input ns-len-long" autocomplete="off">
		</div>
		<div class="ns-word-aux">
			<p>活动名称最多为25个字符</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>开始时间：</label>
		<div class="layui-input-inline ns-len-mid">
			<input type="text" class="layui-input" name="start_time" lay-verify="required" id="start_time" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>结束时间：</label>
		<div class="layui-input-inline ns-len-mid end-time">
			<input type="text" class="layui-input" name="end_time" lay-verify="required|time" id="end_time" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>
		<div class="ns-word-aux">
			<p>结束时间不能小于开始时间，也不能小于当前时间</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>条件类型：</label>
		<div class="layui-input-block">
			<input type="radio" name="type" lay-filter="type" value="0" title="满N元" checked>
			<input type="radio" name="type" lay-filter="type" value="1" title="满N件">
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-form manjian-rule">
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>优惠设置：</label>

				<div class="layui-input-block discount-level">
					<div class="level-item">
						<div class="level-head">
							<label class="title">活动层级1：</label>
						</div>
						<div class="wrap">
							<div class="condition">
								<label class="layui-form-label"><span class="required">*</span>优惠门槛：</label>
								<div class="layui-input-block">
									<div class="type-0">
										<div class="layui-form-mid">满</div>
										<div class="layui-input-inline ns-len-short">
											<input type="number" name="money" value="" lay-verify="manjian_money" placeholder="" autocomplete="off" class="layui-input ns-len-short">
										</div>
										<div class="layui-form-mid">元</div>
									</div>
									<div class="type-1 layui-hide">
										<div class="layui-form-mid">满</div>
										<div class="layui-input-inline ns-len-short">
											<input type="number" name="num" value="" lay-verify="manjian_num" placeholder="" autocomplete="off" class="layui-input ns-len-short">
										</div>
										<div class="layui-form-mid">件</div>
									</div>
								</div>
							</div>
							<div class="content">
								<label class="layui-form-label"><span class="required">*</span>优惠内容：</label>
								<div class="layui-input-block">
									<div class="discount-item discount-money">
										<div>
											<input type="checkbox" name="discount_type" value="discount_money" class="ns-input-checkbox" lay-skin="primary"><span>订单金额优惠</span>
										</div>
										<div class="discount-cont layui-hide">
											<div class="layui-form-mid">减</div>
											<div class="layui-input-inline ns-len-short">
												<input type="number" value="" placeholder="" autocomplete="off" class="layui-input ns-len-short">
											</div>
											<div class="layui-form-mid">元</div>
										</div>
									</div>
									<div class="discount-item">
										<div>
											<input type="checkbox" name="" value="free_shipping" class="ns-input-checkbox" lay-skin="primary"><span>包邮</span>
										</div>
										<div class="ns-word-aux" style="margin-left: 28px;margin-top: 0">
											<p>仅参与该活动的商品包邮，非整单包邮</p>
										</div>
									</div>
									<div class="discount-item point">
										<div>
											<input type="checkbox" name="discount_type" value="point" class="ns-input-checkbox" lay-skin="primary"><span>送积分</span>
										</div>
										<div class="discount-cont layui-hide">
											<div class="layui-form-mid">送</div>
											<div class="layui-input-inline ns-len-short">
												<input type="number" name="" value="" placeholder="" autocomplete="off" class="layui-input ns-len-short">
											</div>
											<div class="layui-form-mid">积分</div>
										</div>
									</div>
									<div class="discount-item coupon">
										<div>
											<input type="checkbox" name="discount_type" value="coupon" class="ns-input-checkbox" lay-skin="primary"><span>送优惠券</span>
										</div>
										<div class="discount-cont layui-hide">
											<div><a href="javascript:;" class="ns-text-color select-coupon">选择优惠券</a></div>
											<div class="ns-word-aux">
												<p>请确认优惠券数量是否充足，优惠券数量不足将导致赠送失败</p>
											</div>
											<div>
												<table class="layui-table" lay-skin="nob">
												  	<colgroup>
													    <col width="30%">
													    <col width="50%">
													    <col width="20%">
												  	</colgroup>
											  		<thead>
													    <tr>
													      	<th>优惠券</th>
													      	<th>优惠内容</th>
													      	<th style="text-align:center;">操作</th>
													    </tr>
												  	</thead>
												  	<tbody>

												  	</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<label class="layui-form-label"></label>
				<div class="layui-input-block">
					<button class="layui-btn ns-bg-color" onclick="addDiscountLevel()">添加活动层级</button>
				</div>

			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动商品：</label>
		<div class="layui-input-block">
			<input type="radio" name="manjian_type" lay-filter="manjian_type" value="1" title="全部商品参与" checked>
			<input type="radio" name="manjian_type" lay-filter="manjian_type" value="2" title="指定商品参与">
		</div>
	</div>

	<div class="layui-form-item goods_list" style="display:none">
		<label class="layui-form-label"></label>
		<div class="layui-input-block">
			<table id="selected_goods_list"></table>
			<button class="layui-btn ns-bg-color" onclick="addGoods()">选择商品</button>
		</div>
	</div>
	<input type="hidden" name="goods_ids">

	<div class="layui-form-item">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea ns-len-long"></textarea>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">提交</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" onclick="delGoods({{d.goods_id}})">删除</a>
	</div>
</script>
{/block}
{block name="script"}
<script type="text/javascript" src="MANJIAN_JS/add.js"></script>

<!-- 优惠券 -->
<script type="text/html" id="couponList">
	<div class="gift-box">
		<div class="ns-single-filter-box">
			<div class="layui-form">
				<div class="layui-input-inline">
					<input type="text" name="coupon_name" placeholder="请输入优惠券名称" class="layui-input ns-len-mid">
					<button type="button" class="layui-btn layui-btn-primary" lay-filter="coupon-search" lay-submit>
						<i class="layui-icon">&#xe615;</i>
					</button>
				</div>
			</div>
		</div>
		<table id="coupon_list" lay-filter="coupon_list"></table>
	</div>
</script>

<!-- 优惠券-名称 -->
<script type="text/html" id="couponName">
	<div class="ns-table-tuwen-box">
		<div class="ns-font-box">
			<p class="ns-multi-line-hiding">{{d.coupon_name}}</p>
		</div>
	</div>
</script>

<!-- 优惠券-操作 -->
<script type="text/html" id="couponOperation">
	{{# if($.inArray(d.coupon_type_id, coupon_list) != -1){ }}
	<p title="该优惠券已参加积分兑换活动">已添加</p>
	{{# }else{ }}
	<a class="layui-btn" lay-event="add">添加</a>
	{{# } }}
</script>
{/block}