{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.ns-discount { display: flex; justify-content: space-between; height: 34px; line-height: 34px; padding: 5px 15px; background-color: #F6FBFD; border: 1px dashed #BCE8F1; }
</style>
{/block}
{block name="main"}
<div class="layui-form">
	<div class="layui-form-item">
		<label class="layui-form-label">活动名称：</label>
		<div class="layui-input-inline">{$manjian_info.manjian_name}</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">开始时间：</label>
		<div class="layui-input-inline">
			{:date('Y-m-d H:i:s',$manjian_info.start_time)}
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">结束时间：</label>
		<div class="layui-input-inline">
			{:date('Y-m-d H:i:s',$manjian_info.end_time)}
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-form">
			<div class="layui-form-item">
				<label class="layui-form-label">满减规则：</label>
				<div class="ns-discount-box">
					{foreach name="$manjian_info.rule" item="vo"}
					<div class="ns-discount ns-form-row">
						<div class="ns-discount-con">
							<p>单笔订单满 
								{if $manjian_info.type == 0}
									<span class="ns-red-color money-num">{:sprintf('%.2f', $vo.limit)}</span>元
								{else/}
									<span class="ns-red-color money-num">{:number_format($vo.limit)}</span>件
								{/if}
								{if isset($vo.discount_money)}，减{$vo.discount_money}元 {/if}
								{if isset($vo.free_shipping)}，包邮 {/if}
								{if isset($vo.point)}，送{$vo.point}积分 {/if}
								{if isset($vo.coupon)}，送优惠券<a href="{:addon_url('coupon://shop/coupon/detail', ['coupon_type_id' => $vo.coupon ])}" target="_blank">【{$vo.coupon_data.coupon_name}】</a> {/if}
						</div>
					</div>
					{/foreach}
				</div>
			</div>
		</div>
	</div>

	{if $manjian_info.manjian_type == 1}
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动商品：</label>
		<div class="layui-input-inline">
			 全部商品参与
		</div>
	</div>
	{else /}
	<div class="layui-form-item goods_list">
		<label class="layui-form-label">活动商品：</label>
		<div class="layui-input-block">
			<!-- <table class="layui-table" id="goods" lay-skin="line" lay-size="lg">

				<thead>
				<tr>
					<th>商品名称</th>
					<th>商品价格(元)</th>
					<th>库存</th>
				</tr>
				</thead>
				<tbody>
				{if empty($manjian_info.goods_list)}
				<tr>
					<td class="ns-goods-null" colspan="3">
						<div class="goods-null">该活动还未选择商品</div>
					</td>
				</tr>
				{else /}
				{foreach $manjian_info.goods_list as $v}
				<tr data-sku_id="{$v.goods_id}">
					<td>{$v.goods_name}</td>
					<td>{$v.price}</td>
					<td>{$v.goods_stock}</td>
				</tr>
				{/foreach}
				{/if}
				</tbody>
			</table> -->
			<table id="selected_sku_list"></table>
		</div>
	</div>
	{/if}

	<div class="layui-form-item">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block ns-input-text">{$manjian_info.remark}</div>
	</div>
	
	<div class="ns-form-row">
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>

	<input type="hidden" name="rule_json" value='{$manjian_info.rule_json}' id="rule_json" />
</div>
{/block}
{block name="script"}
<script>

	var rule = JSON.parse($("#rule_json").val());
	var goods_list = {:json_encode($manjian_info.goods_list, JSON_UNESCAPED_UNICODE)};
	
	for (var key in rule) {
		var html = '<div class="ns-discount ns-form-row">'+
						'<div class="ns-discount-con">'+
							'<p>单笔订单满<span class="ns-red-color money-num">' + rule[key].money + '</span>元，立减现金<span class="ns-red-color discount_money-num">' + rule[key].discount_money + '</span>元</p>'+
						'</div>'+
					'</div>';
		// $(".ns-discount-box").append(html);
	}
	
	layui.use('form', function() {
		var table,
			form = layui.form;
		
		table = new Table({
			elem: '#selected_sku_list',
			cols: [
				[{
					field: 'goods_name',
					title: '商品名称',
					unresize: 'false',
					width: '60%'
				}, {
					field: 'price',
					title: '商品价格(元)', 
					unresize: 'false',
					align: 'right',
					width: '20%',
					templet: function(data) {
						return '￥' + data.price;
					}
				}, {
					field: 'goods_stock', 
					title: '库存', 
					unresize: 'false',
					align: 'center',
					width: '20%'
				}],
			],
			data: goods_list,
		});
	});
	
	function back() {
		location.href = ns.url("manjian://shop/manjian/lists");
	}
</script>
{/block}