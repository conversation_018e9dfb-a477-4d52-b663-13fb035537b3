{extend name="app/shop/view/base.html" /}
{block name="resources"}
<link rel="stylesheet" href="WECHAT_CSS/wx_replay.css">
{/block}
{block name="main"}
<div class="layui-tab-brief fourstage-nav clearfix">
    <ul class="layui-tab-title">
        <li class='layui-this'><a href='{:addon_url("wechat://shop/replay/replay")}'>关键词自动回复</a></li>
        <li><a href='{:addon_url("wechat://shop/replay/follow")}'>关注后自动回复</a></li>
    </ul>
    <div class="ns-single-filter-box">
        <div class='info'>
            <button class='add-replay js-replay layui-btn ns-bg-color' type="button" nc-event="click" nc-action="addRule">新建自动回复</button>
        </div>
        <div class="layui-form">

            <div class="layui-input-inline">
                <input type="text" name="rule_name" id="search_text" placeholder="请输入规则" class="layui-input">
                <button type="button" class="layui-btn layui-btn-primary" onclick="search()">
                    <i class="layui-icon"></i>
                </button>

            </div>
        </div>
    </div>

    <div class="weixin-normal rule-autoreplay-page">
        <div id="load_rule_list" class="rule-group-container"></div>
        <div id="list_page" style="text-align: right;"></div>

        <!--添加回复-->
        <div class="layui-form rule-container" id="add_reply" style="display: none">
            <input type="hidden" name="layer_index" value=""/>
            <input type="hidden" name="rule_id" value=""/>
            <input type="hidden" name="key_id" value="-1"/>
            <input type="hidden" value="" id="hidden_reply_type" />
            <input type="hidden" value="" id="hidden_media_id" />
            <div class="arrow">
                <i class="layui-icon">&#xe603;</i>
            </div>
            <a href="javascript:;" class="close--circle js-close">×</a>
            <div>
                <div class="misc">
                    <!-- 		<a href="javascript:;" class="js-replay" nc-event="click" nc-action="emotion">表情</a> -->
                    <a href="javascript:;" class="js-replay" nc-event="click" nc-action="hyperlink">插入链接</a>
                    <!--<a href="javascript:;" class="image" onclick="material(2);">文本消息</a>-->
                    <!-- <a href="javascript:;" class="voice" onclick="chooseMaterial(3);">音频</a> -->
                    <!-- <a href="javascript:;" class="js-replay" nc-event="click" nc-action="music">音乐</a> -->
                    <a href="javascript:;" class="js-replay" onclick="material(1);">选择图文</a>
                    <!--<div class="others">-->
                        <!--<a href="javascript:;">其他<i class="caret"></i></a>-->
                        <!--<ul class="dropdown-menu">-->
                            <!--{volist name="link_list" id="vo"}-->
                            <!--<li>-->
                                <!--<a class="js-open-goods" data-action-type="{$vo.name}" data-complex-mode="true" href="javascript:;">{$vo.title}</a>-->
                            <!--</li>-->
                            <!--{/volist}-->
                        <!--</ul>-->
                    <!--</div>-->
                </div>

                <div class="layui-form-item">
                    <textarea placeholder="请输入内容" class="layui-textarea" name="reply_content" maxlength="300" lay-verify="required|content"></textarea>
                    <div class="complex-backdrop">
                        <div class="complex-content"></div>
                    </div>
                    <div class="layui-input-block" style="margin-top: 10px; margin-left: 0;">
                        <button class="layui-btn ns-bg-color" type="button" lay-submit lay-filter="add_reply">确定</button>
                        <span class="pull-right">还能输入 <i>300</i> 个字 </span>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!--添加一条规则-->
<div class="layui-form" id="add_auto_replay" style="display:none">
    <input type="hidden" name="layer_index" value="">
    <input type="hidden" name="rule_id" value="">
    <div class="layui-form-item">
        <label class="layui-form-label sm"><span class="required">*</span>规则名称</label>
        <div class="layui-input-inline ">
            <input type="text" name="key_rule_name" placeholder="关键字回复规格名称" autocomplete="off" class="layui-input" required lay-verify="required"/>
        </div>
    </div>
    <div class="ns-form-row sm">
        <button class="layui-btn ns-bg-color" type="button" lay-submit lay-filter="add_rule_replay">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>
</div>

<!--添加关键字-->
<div class="layui-form" id="add_keywords" style="display:none">
    <input type="hidden" name="layer_index" value=""/>
    <input type="hidden" name="rule_id" value=""/>
    <input type="hidden" name="key_id" value="-1"/>
    <div class="layui-form-item ">
        <label class="layui-form-label sm">关键字</label>
        <div class="layui-input-inline ">
            <input type="text" name="keywords_name" placeholder="关键词最多支持15个字" autocomplete="off" class="layui-input" required lay-verify="required|length"/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label sm">规则</label>
        <div class="layui-input-block">
            <input type="radio" name="keywords_type" value="0" title="全匹配" checked/>
            <input type="radio" name="keywords_type" value="1" title="模糊"/>
        </div>
    </div>
    
    <div class="ns-form-row sm">
        <button class="layui-btn ns-bg-color" type="button" lay-submit lay-filter="add_keywords">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>

</div>

<!-- 插入链接 -->
<div class="layui-form hyperlink" id="hyperlink" style="display:none">
    <div class="layui-form-item">
        <div class="layui-input-inline">
            <input type="text" name="title" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')"  lay-verify="required|title" autocomplete="off" placeholder="http://" class="layui-input"/>
        </div>
        <button class="layui-btn layui-btn-primary" lay-submit lay-filter="hyperlink">确定</button>
    </div>
</div>
<!-- 音乐素材 -->
<div class="layui-form" id="music" style="display:none">
    <div class="layui-form-item ">
        <label class="layui-form-label msg-music-thumb">
            <a href="javascript:chooseMaterial(2);" class="js-replay" nc-event="click" nc-action="thumbnail">
                <i class="layui-icon">&#xe654;</i>
            </a>
        </label>
        <div class="layui-input-inline ">
            <input type="text" name="title" placeholder="音乐标题" autocomplete="off" class="layui-input" lay-verify="required|title" style="margin-bottom: 10px;"/>
            <textarea placeholder="音乐描述" class="layui-textarea" name="description" maxlength="300" lay-verify="required|description"></textarea>
        </div>
    </div>
    <div class="layui-form-item ">
        <label class="layui-form-label">普通音质</label>
        <div class="layui-input-inline ">
            <input type="text" name="music_url" placeholder="填写音乐地址" autocomplete="off" class="layui-input" lay-verify="required|url"/>
        </div>
    </div>
    <div class="layui-form-item ">
        <label class="layui-form-label">高清音质</label>
        <div class="layui-input-inline ">
            <input type="text" name="hq_music_url" placeholder="填写音乐地址" autocomplete="off" class="layui-input" lay-verify="required|url"/>
        </div>
    </div>
    <div class="layui-form-item">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="music" style="float: right; margin-right: 10px;">确定</button>
    </div>
    <input type="hidden" name="thumb_attachment_id" value=""/>
</div>
{/block}
{block name="script"}
<script type='text/javascript' src='WECHAT_JS/wx_replay.js'></script>
<script src="WECHAT_JS/common.js"></script>
<script type="text/javascript">
    $(function() {
        var replay = new WxReplay(3, [ 3, 6, 9 ]);
        replay.getData({
            "_this" : replay,
            "rule_type" : 'KEYWORDS'
        });//数据初始化
        replay.pageInit({
            "_this" : replay
        });//分页初始化
        // $(".js-replay").bind("click", {"_this": replay}, replay.e); //元素事件
        $(".rule-autoreplay-page,.info").delegate(".js-replay", "click", {
            "_this" : replay
        }, replay.e);

        layui .use([ 'form' ], function() {
            var form = layui.form;

            //添加关键字回复
            form.on('submit(add_rule_replay)', function(data) {
                var d = data.field;
                var dom = data.form;
                var rule_id = d.rule_id;
                var rule_name = $.trim(d.key_rule_name); //关键词名称
                var layer_index = d.layer_index;
                var param = {
                    url : ns.url('wechat://shop/replay/addOrEditRule'),
                    data : {
                        "rule_name" : rule_name,
                        "rule_id" : rule_id
                    },
                    success : function(res) {
                        layer.msg(res.message);
                        if (res.code >= 0) {
                            //关闭弹出层
                            $(dom).find('button[type="reset"]').click();

                            replay.getData({
                                "_this" : replay,
                                "rule_type" : 'KEYWORDS'
                            });//数据初始化
                            replay.pageInit({
                                "_this" : replay
                            });//分页初始化
                            //   $(".layui-laypage-btn").click();

                            layer.close(layer_index);
                        }
                    }
                };
                replay.sendAjax(param);
            });

            //添加关键字
            form.on('submit(add_keywords)', function(data) {
                var d = data.field;
                var dom = data.form;
                var rule_id = d.rule_id;
                var key_id = d.key_id;
                var keywords_name = $.trim(d.keywords_name);
                var keywords_type = d.keywords_type;
                var layer_index = d.layer_index;
                form.verify({
                    length : function(val, dom) {
                        if (val.length > 15) {
                            layer.msg("关键词最多支持15个字");
                        }
                    }
                });
                var param = {
                    url : ns.url('wechat://shop/replay/editKeywords'),
                    data : {
                        "rule_id" : rule_id,
                        "keywords_name" : keywords_name,
                        "keywords_type" : keywords_type,
                        "key_id" : key_id
                    },
                    success : function(res) {
                        layer.msg(res.message);
                        if (res.code >= 0) {
                            $(".layui-laypage-btn").click();
                            $(dom).find('button[type="reset"]').click();
                            layer.close(layer_index);
                            location.reload();
                        }
                    }
                };
                replay.sendAjax(param);
            });

            //添加和修改回复
            form.on('submit(add_reply)', function(data) {
                var d = data.field;
                var dom = data.form;
                var rule_id = d.rule_id;
                var key_id = d.key_id;
                var reply_content = $.trim(d.reply_content);
                var layer_index = d.layer_index;
                var type = $("#hidden_reply_type").val() ? $("#hidden_reply_type").val() : "text";
                var media_id = $('#hidden_media_id').val();

                var param = {
                    url : ns.url('wechat://shop/replay/editReplays'),
                    data : {
                        "rule_id" : rule_id,
                        "reply_content" : reply_content,
                        "key_id" : key_id,
                        "type" : type,
                        'media_id' : media_id
                    },
                    success : function(res) {
                        layer.msg(res.message);
                        if (res.code >= 0) {
                            $(".layui-laypage-btn").click();
                            $('#hidden_media_id').val('');
                            layer.close(layer_index);
                            $('#add_reply').css("display", "none");
                            location.reload();
                        }
                    }
                };
                replay.sendAjax(param);
            });

            //插入链接 确定
            form.on('submit(hyperlink)', function(data, index) {
                var d = data.field;
                var url = d.title;
                if (url.indexOf('http://') == -1 & url.indexOf('https://') == -1) {
                    url = 'http://' + url;
                }
                var content = $("#add_reply").find("textarea[name='reply_content']").val();
                $("#add_reply").find("textarea[name='reply_content']").val(content + url);
                var text_leng = (content + url).length;
                var left_leng = 300 - text_leng;
                $('.pull-right').find('i').text(left_leng);
                layer.close(layer.index);
            });

            //音乐 确定
            form.on('submit(music)', function(data, index) {
                var d = data.field;
                var thumb_attachment_id = d.thumb_attachment_id;
                var title = d.title;
                var description = d.description;
                var music_url = d.music_url;
                var hq_music_url = d.hq_music_url;

                var active_pic = '';
                active_pic += '<div class="voice-wrapper" data-voice-src="'+music_url+'">';
                active_pic += '<span class="voice-player">';
                active_pic += '<a href="javascript:;" class="close--circle js-delete-complex">×</a>';
                active_pic += '<span class="stop">点击播放</span>';
                active_pic += '<span class="second"></span>';
                active_pic += '<i class="play" style="display:none;"></i>';
                active_pic += '</span>';
                active_pic += '</div>';
                $("#add_reply").find(".complex-content").html(active_pic);
                $('.complex-backdrop').css("display", "block");
                $("#add_reply").find("textarea[name='reply_content']").val(music_url);
                $("#hidden_reply_type").val('music');
                layer.close(layer.index);
            });
        });

        //关闭  清除
        $(".js-close").click(function() {
            $("#add_reply").find("textarea[name='reply_content']").val('');
            $('.complex-backdrop').css("display", "none");
            $('#add_reply').css("display", "none");
            $(".layui-layer-shade").remove();
        });

        //清除
        $("body").on('click', ".js-delete-complex", function() {
            $("#add_reply").find("textarea[name='reply_content']").val('');
            $("#add_reply").find("span.pull-right").show();
            $('.complex-backdrop').css("display", "none");
        });

    });

    //图片回调
    function materialPicCallBack(data) {
        var active_pic = '';
        active_pic += '<div class="ng ng-single ng-image">';
        active_pic += '<a class="picture" target="_blank" href="'+data['url']+'"><img src="' + ns.img(data['path']) + '" alt=""/></a>';
        active_pic += '</div>';

        $("#add_reply").find(".complex-content").html(active_pic);
        $('.complex-backdrop').css("display", "block");
        $("#add_reply").find("span.pull-right").hide();
        $("#add_reply").find("textarea[name='reply_content']").val(data['path']);
        $("#hidden_reply_type").val('image');
        $("#hidden_media_id").val(data.media_id);
    }

    //图文回调
    function chooseGraphicMessage(data) {
        var active_pic = '';
        active_pic += '<div class="ng ng-single">';
        active_pic += '<a href="javascript:;" class="close--circle js-delete-complex">×</a>';
        active_pic += '<div class="ng-item">';
        active_pic += '<span class="label label-success">图 文</span>';
        active_pic += '<div class="ng-title">';
        //active_pic += '<a href="'+data.value[0].url+'" target="_blank" class="new-window" title="'+data.value[0].title+'">' + data.value[0].title + '</a>';
        active_pic += '<a href="javascript:;" title="'+data.value[0].title+'">' + data.value[0].title + '</a>';
        active_pic += '</div>';
        active_pic += '</div>';
        active_pic += '<div class="ng-item view-more">';
        // active_pic += '<a href="'+data.value[0].url+'" target="_blank" class="clearfix new-window">';
        active_pic += '<a href="javascript:;" class="clearfix new-window">';
        active_pic += '<span class="pull-left">阅读全文</span>';
        active_pic += '<span class="pull-right">&gt;</span>';
        active_pic += '</a>';
        active_pic += '</div>';
        active_pic += '</div>';

        $("#add_reply").find(".complex-content").html(active_pic);
        $('.complex-backdrop').css("display", "block");
        $("#add_reply").find("textarea[name='reply_content']").val(data.value[0].title);
        $("#add_reply").find("span.pull-right").hide();
        $("#hidden_reply_type").val('articles');
        $("#hidden_media_id").val(data.media_id);
    }

    //弹出框的位置
    $("body").on('click', ".add-reply-menu", function() {
        var x = $(this).position().top;
        var y = $(this).position().left;
        var real_x = x - 8;
        var real_y = y + 120;
        $('.rule-container').css('top', real_x);
        $('.rule-container').css('left', real_y);

        var m = '<i class="layui-icon">&#xe603;</i>';
        $('.rule-container .arrow').html(m);
        $('.rule-container .arrow').css('right', 'auto');
        $('.rule-container .arrow').css('left', '-13px');

        $('.pull-right').find('i').text(300);
    });

    //编辑弹出框的位置
    $("body").on('click',".js-edit-it", function() {
        var x = $(this).offset().top;
        var y = $(this).offset().left;
        var real_x = x - 220;
        var real_y = y - 730;
        $('.rule-container').css('top', real_x);
        $('.rule-container').css('left', real_y);

        var s = '<i class="layui-icon">&#xe602;</i>';
        $('.rule-container .arrow').html(s);
        $('.rule-container .arrow').css('left', 'auto');
        $('.rule-container .arrow').css('right', 3);

        var text_leng = $('.rule-container').find(".layui-textarea").val().length;
        var left_leng = 300 - text_leng;
        $('.pull-right').find('i').text(left_leng);
    });

    $("body").on('keydown', ".layui-textarea", function() {
        var text_leng = $(this).val().length;
        var left_leng = 300 - text_leng;
        $('.pull-right').find('i').text(left_leng);
    });

    function search(){
        var replay = new WxReplay(3, [ 3, 6, 9 ]);
        var search_text = $("#search_text").val();
        replay.getData({
            "_this" : replay,
            "rule_type" : 'KEYWORDS',
            "search_text" : search_text
        });//数据初始化
        replay.pageInit({
            "_this" : replay
        });
    }
</script>
{/block}