{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.ns-form-row{margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form">
	<div class="layui-card ns-card-common ns-card-brief">
		<div class="layui-card-header">
			<span class="ns-card-title">微信公众号设置</span>
		</div>

		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">公众号名称：</label>
				<div class="layui-input-block">
					<input type="text" name="wechat_name" autocomplete="off" value="{$config.value.wechat_name ?? ''}" class="layui-input ns-len-long">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">原始ID：</label>
				<div class="layui-input-block">
					<input type="text" name="wechat_original" autocomplete="off" value="{$config.value.wechat_original ?? ''}" class="layui-input ns-len-long">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">公众号二维码：</label>
				<div class="layui-input-block">
					<input type="hidden" class="layui-input" name="qrcode" value="{$config.value.qrcode ?? ''}"/>
					<div class="upload-img-block square">
						<div class="upload-img-box" id="img">
						{if condition="$config.value && $config.value.qrcode"}
						<img src="{:img($config.value.qrcode)}"/>
						{else/}
						<div class="ns-upload-default">
							<img src="SHOP_IMG/upload_img.png" />
							<p>点击上传</p>
						</div>
						{/if}
						</div>
				   </div>
				</div>
			</div>

			<!-- <div class="layui-form-item">
				<label class="layui-form-label">重新授权：</label>
				<div class="layui-input-block">
					<a class="ns-text-color " onclick="auth();">去授权</a>
				</div>
			</div> -->
		</div>
	</div>

	<div class="layui-card ns-card-common ns-card-brief">
		<div class="layui-card-header">
			<span class="ns-card-title">开发设置</span>
		</div>

		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">APPID：</label>
				<div class="layui-input-block">
					<input type="text" name="appid" autocomplete="off" value="{$config.value.appid ?? ''}" class="layui-input ns-len-long">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">APP密钥：</label>
				<div class="layui-input-block">
					<input type="text" name="appsecret" autocomplete="off" value="{$config.value.appsecret ?? ''}" class="layui-input ns-len-long">
				</div>
				<div class="ns-word-aux">AppID和Appsecret来自于您申请开发接口时提供的账号和密码，且公众号为已认证服务号</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">业务域名校验文件：</label>
				<div class="layui-input-block">
					<button class="layui-btn layui-btn-primary" id="checkFile">上传文件</button>
				</div>
				<div class="ns-word-aux">仅支持上传TXT格式的文件</div>
			</div>
		</div>
	</div>

	<div class="layui-card ns-card-common ns-card-brief">
		<div class="layui-card-header">
			<span class="ns-card-title">公众平台通信</span>
		</div>

		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">Token：</label>
				<div class="layui-input-inline">
					<input type="text" name="token" autocomplete="off" id="empowerToken" class="layui-input ns-len-long" value="{$config.value.token ?? ''}">
				</div>
				<button class="layui-btn layui-btn-primary" onclick="ns.copy('empowerToken')">复制</button>
				<div class="ns-word-aux">Token必须为英文或数字，长度为3-32字符。如不填写则默认为“TOKEN”。</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">URL：</label>
				<div class="layui-input-inline">
					<input type="text" readonly autocomplete="off" id="call_back_url" class="layui-input ns-len-long" value="{$call_back_url}">
				</div>
				<button class="layui-btn layui-btn-primary" onclick="ns.copy('call_back_url')">复制</button>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">EncodingAESKey：</label>
				<div class="layui-input-inline">
					<input type="text" autocomplete="off" name="encodingaeskey" id="EncodingAESKey" class="layui-input ns-len-long" value="{$config.value.encodingaeskey ?? ''}">
				</div>
				<button class="layui-btn layui-btn-primary" onclick="ns.copy('EncodingAESKey')">复制</button>
			</div>
		</div>
	</div>

	<div class="layui-card ns-card-common ns-card-brief">
		<div class="layui-card-header">
			<span class="ns-card-title">配置说明</span>
		</div>

		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">业务域名：</label>
				<div class="layui-input-inline">
					<input type="text" readonly autocomplete="off" id="public_url" class="layui-input ns-len-long" value="{$url}">
				</div>
				<button class="layui-btn layui-btn-primary" onclick="ns.copy('public_url')">复制</button>
				<div class="ns-word-aux">
					设置业务域名（设置业务域名，用户在相应域名上进行输入时不再出现防欺诈盗号等安全提示）<br/>
					<a href="https://mp.weixin.qq.com/" target="_blank">登录微信公众平台</a>点击公众号设置&gt;功能设置&gt;业务域名设置，一次填写：<br/>
					域名：{$url}<br/>
					<a href="https://mp.weixin.qq.com/" target="_blank">登录微信公众平台</a>点击公众号设置&gt;开发者中心&gt;网页授权获取用户基本信息&gt;修改
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">授权域名：</label>
				<div class="layui-input-inline">
					<input type="text" readonly autocomplete="off" id="auth_url" class="layui-input ns-len-long" value="{$url}">
				</div>
				<button class="layui-btn layui-btn-primary" onclick="ns.copy('auth_url')">复制</button>
				<div class="ns-word-aux">
					填写授权回调页面域名业务域名设置完毕！
				</div>
			</div>

		</div>
	</div>
	
	<div class="layui-card ns-card-common">
		<div class="layui-card-body">
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
				<button type="reset" class="layui-btn layui-btn-primary" onclick="back()">返回</button>
			</div>
		</div>
	</div>
</div>
{/block}
{block name="script"}
<script type="text/javascript">
    layui.use(['form', 'upload'], function () {
        var form = layui.form,
            upload = layui.upload,
            repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: "post",
				url: "{:addon_url('wechat://shop/wechat/config')}",
				dataType: "JSON",
				data: data.field,
				success: function(data) {
					repeat_flag = false;
					layer.msg(data.message);
				}
			});
		});

        // 图片上传
        var uploadInst = upload.render({
            elem: '#img',
            url: ns.url("shop/upload/image"),
            done: function (res) {
                if (res.code >= 0) {
                    $("input[name='qrcode']").val(res.data.pic_path);
                    $("#img").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });

        
        var uploadFile = upload.render({
			elem: '#checkFile',
			accept: 'file',
			acceptMime: 'text/plain',
			exts: 'txt',
			url: ns.url("shop/upload/checkfile"),
			done: function(res) {
				if (res.code >= 0) {
					layer.msg('文件上传成功');
				}
			}
		});

	});
 
	function back() {
		location.href = "{:addon_url('wechat://shop/wechat/setting')}";
	}


	/**
	 * 去授权
	 */
	function auth(){

		window.open("{:addon_url('wxoplatform://shop/oplatform/auth', ['auth_type' => 'wechat'])}");
		//询问框
		layer.confirm('请在新窗口中继续授权', {
			title:'提示',
			btn: ['重新授权','已完成授权'] //按钮
		}, function(){
			layer.closeAll();
			auth();
		}, function(){
			layer.closeAll();
			//验证是否授权
			$.ajax({
				type: "post",
				url: "{:addon_url('wxoplatform://shop/oplatform/wechatsettled')}",
				dataType: "JSON",
				success: function(data) {
					if(data.code >= 0){
						location.href = "{:addon_url('wechat://shop/wechat/setting')}";
					}

				}
			});
		});
	}
</script>
{/block}