<?php

namespace app\api\controller;

use app\model\goods\Cart as CartModel;
use app\model\goods\Goods;
use app\model\goods\GoodsStock;
use app\model\system\User as UserModel;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\order\Order as OrderModel;
use think\facade\Db;

class Cart extends BaseApi
{
    public function order_devery(){
        $order_common_model = new OrderCommonModel();
        $condition          = array(
            ["order_status", '=', 1]
        );
        $res        = $order_common_model->getOrderList($condition, '', "order_id desc",1);
        if (isset($res['data'][0])){
            $res = $res['data'][0];
        }else{
            $res = [];
        }
        return $this->response($res);
    }
    /**
     * 添加信息
     */
    public function add()
    {
        //$token = $this->checkToken();
        //if ($token['code'] < 0) return $this->response($token);

        $express_no = isset($this->params['express_no']) ? $this->params['express_no'] : 0;
        $content    = isset($this->params['image']) ? $this->params['image'] : 0;
        if (empty($express_no)) {
            return $this->response($this->error('', 'REQUEST_EXPRESS_NO'));
        }
        if (empty($content)) {
            return $this->response($this->error('', 'REQUEST_IMAGES'));
        }

        $reg = '/data:image\/(\w+?);base64,(.+)$/si';
        preg_match($reg,$content,$match_result);
        $file_name = time().'.'.$match_result[1];
        $logo_path = 'upload/devery/'.$file_name;
        //file_put_contents($logo_path,base64_decode($match_result[2]));
        $cart = new CartModel();
        $data = [
            'express_no' => $express_no,
            'content'    => '/'.$logo_path,
            'create_time'       => time()
        ];
        //$res  = $cart->addCart($data);

        $post_url = 'http://im.fmsupplement.com/api/order_devery.php';
        $post_data = ['basefile'=>$match_result[2]];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $post_url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        if ($post_data != '' && !empty($post_data)) {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $result = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($result,true);
        if (isset($result['path'])){
            $data = [
                'express_no' => $express_no,
                'content'    => 'http://mianyangkeji.oss-cn-shanghai.aliyuncs.com/'.$result['path'],
                'create_time'       => time()
            ];
            $res  = $cart->addCart($data);
        }
        return $this->response($res);
    }

    public function file_upload_base64_image($post) {
        $base64=base64_decode($post);

        $file_tmp_name = 'upload/devery/'.time().'.jpeg';

        if (file_put_contents($file_tmp_name, $base64) == false) {

            return false;
        }

        return $file_tmp_name;

    }

    public function salesLists()
    {
        $goodsb = isset($this->params['goodsb']) ? $this->params['goodsb'] : '';
        $saleb  = isset($this->params['saleb']) ? $this->params['saleb'] : '';
        if ($goodsb){
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
            $condition[] = ['o.goods_name|o.goods_chi|o.keywords|o.category_names|og.sku_no', 'like', '%' . $goodsb . '%'];
        }else{
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
        }
        $alias       = 'og';
        $join        = [
            [
                'goods o',
                'o.sku_id = og.sku_id',
                'left'
            ]
        ];
        $goodslist = model('goods_sku')->getList($condition, 'o.goods_chi,og.sku_no,o.goods_name,og.sku_id,og.stock', 'og.stock asc', $alias, $join,'',50);


        $alias       = 'og';
        $join        = [
            [
                'order_goods o',
                'o.order_id = og.order_id',
                'left'
            ]
        ];
        $salelist = model('order')->getList([['og.order_status','>',0],['og.create_time','>',(time()-60*60*24*30)]], 'o.sku_id,o.num', '', $alias, $join);
        $salelistresult = [];
        foreach($salelist as $k=>$vo){
            if (!isset($salelistresult[$vo['sku_id']])){
                $salelistresult[$vo['sku_id']] = 0;
            }

            $salelistresult[$vo['sku_id']] = $salelistresult[$vo['sku_id']] + $vo['num'];

        }
        if ($saleb){
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
            $condition[] = ['o.goods_name|o.goods_chi|o.keywords|o.category_names|og.sku_no', 'like', '%' . $saleb . '%'];
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.sku_id = og.sku_id',
                    'left'
                ]
            ];
            $goodlist = model('goods_sku')->getList($condition, 'o.goods_chi,og.sku_no,o.goods_name,og.sku_id,og.sku_name,og.stock', 'og.stock asc', $alias, $join,'');
            $list = model('goods_sku')->getList([['sku_id','in',array_column($goodlist, 'sku_id')]], 'sku_no,sku_name,sku_id,stock');
        }else{
            $list = model('goods_sku')->getList([['sku_id','in',array_keys($salelistresult)]], 'sku_no,sku_name,sku_id,stock');

        }
        foreach($list as $key=>$vo){
            $list[$key]['sales'] = isset($salelistresult[$vo['sku_id']])?$salelistresult[$vo['sku_id']]:0;
        }
        $user_model = new UserModel();
        $sorted = $user_model->array_orderby($list, 'sales', SORT_DESC);
        $first50Elements = array_slice($sorted, 0, 50);
        echo json_encode(['goodslist'=>$goodslist,'salelist'=>$first50Elements]);
    }

    /**
     * 商品购物车列表
     */
    public function goodsLists()
    {
        $goodsb = isset($this->params['goodsb']) ? $this->params['goodsb'] : '';
        $saleb  = isset($this->params['saleb']) ? $this->params['saleb'] : '';
        if ($goodsb){
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
            $condition[] = ['o.goods_name|o.goods_chi|o.keywords|o.category_names|og.sku_no', 'like', '%' . $goodsb . '%'];
        }else{
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
        }
        $alias       = 'og';
        $join        = [
            [
                'goods o',
                'o.sku_id = og.sku_id',
                'left'
            ]
        ];
        $goodslist = model('goods_sku')->getList($condition, 'o.goods_chi,og.sku_no,o.goods_name,og.sku_id,og.stock', 'og.stock asc', $alias, $join,'',50);


        $alias       = 'og';
        $join        = [
            [
                'order_goods o',
                'o.order_id = og.order_id',
                'left'
            ]
        ];
        $salelist = model('order')->getList([['og.order_status','>',0],['og.create_time','>',(time()-60*60*24*7)]], 'o.sku_id,o.num', '', $alias, $join);
        $salelistresult = [];
        foreach($salelist as $k=>$vo){
            if (!isset($salelistresult[$vo['sku_id']])){
                $salelistresult[$vo['sku_id']] = 0;
            }

            $salelistresult[$vo['sku_id']] = $salelistresult[$vo['sku_id']] + $vo['num'];

        }
        if ($saleb){
            $condition = [];
            $condition[] = ['og.is_delete','=',0];
            $condition[] = ['o.goods_name|o.goods_chi|o.keywords|o.category_names|og.sku_no', 'like', '%' . $saleb . '%'];
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.sku_id = og.sku_id',
                    'left'
                ]
            ];
            $goodlist = model('goods_sku')->getList($condition, 'o.goods_chi,og.sku_no,o.goods_name,og.sku_id,og.sku_name,og.stock', 'og.stock asc', $alias, $join,'');
            $list = model('goods_sku')->getList([['sku_id','in',array_column($goodlist, 'sku_id')]], 'sku_no,sku_name,sku_id,stock');
        }else{
            $list = model('goods_sku')->getList([['sku_id','in',array_keys($salelistresult)]], 'sku_no,sku_name,sku_id,stock');

        }
        foreach($list as $key=>$vo){
            $list[$key]['sales'] = isset($salelistresult[$vo['sku_id']])?$salelistresult[$vo['sku_id']]:0;
            $list[$key]['salestotal'] = $vo['stock']==0 ?$list[$key]['sales']/1:round($list[$key]['sales']/$vo['stock'],2);
        }
        $user_model = new UserModel();
        $sorted = $user_model->array_orderby($list, 'salestotal', SORT_DESC);
        $first50Elements = array_slice($sorted, 0, 50);
        echo json_encode(['goodslist'=>$goodslist,'salelist'=>$first50Elements]);
    }

    /**
     * 获取购物车数量
     * @return string
     */
    public function count()
    {
        $saleb  = isset($this->params['saleb']) ? $this->params['saleb'] : '';
        $result = Db::connect('v3')
            ->table('baijiacms_sor_info')
            ->alias('og')
            ->join('baijiacms_goods o', 'og.goods = o.id')
            ->where('og.pid','=',$saleb)
            ->field('o.name,o.number,og.nums,og.handle')
            ->select()
            ->toArray();
        $nums = $handle=0;
        foreach($result as $key=>$vo){
            $nums = $nums + $vo['nums'];
            $handle = $handle + $vo['handle'];
        }
        echo json_encode(['salelist'=>$result,'nums'=>$nums,'handle'=>$handle]);
    }

    /**
     * 购物车关联列表
     * @return false|string
     */
    public function lists()
    {
        $saleb  = isset($this->params['saleb']) ? $this->params['saleb'] : '';
        $result = Db::connect('v3')
            ->table('baijiacms_sell_info')
            ->alias('og')
            ->join('baijiacms_goods o', 'og.goods = o.id')
            ->where('og.pid','=',$saleb)
            ->field('o.name,o.number,og.nums')
            ->select()
            ->toArray();
        $nresult = [];
        $nums = 0;
        foreach ($result as $key=>$vo){
            for ($i = 0; $i < $vo['nums']; $i++) {
                $nresult[]=$vo;
            }
            $nums = $nums + $vo['nums'];
        }
        echo json_encode(['salelist'=>$nresult,'nums'=>$nums]);
    }

    public function countt()
    {
        $condition = [
            ['time', '>', strtotime('-1 week')]
        ];
        $goodlist = model('goods_stock_log')->getList($condition, '*');
        $sku_ids = array_column($goodlist, 'sku_id');
        $sku_ids = array_unique($sku_ids);
        $errors = [];
        foreach ($sku_ids as $key=>$vo){
            $condition = [
                ['time', '>', strtotime('-1 week')],
                ['sku_id', '=', $vo]
            ];
            $goodlist = model('goods_stock_log')->getList($condition, '*','time asc,id asc');
            for ($i = 0; $i < count($goodlist) - 1; $i++) {
                $current = $goodlist[$i];
                $next = $goodlist[$i + 1];

                if ($current['new_stock'] != $next['old_stock']) {
                    $errors[] = [
                        'goods_id' => $current['goods_id'],
                        'sku_id' => $current['sku_id'],
                        'sku_name' => $current['sku_name'],
                        'error_type' => '库存不连续',
                        'current_record' => $current,
                        'next_record' => $next,
                        'discrepancy' => $next['old_stock'] - $current['new_stock']
                    ];
                }
            }
        }
        // 生成报告
        $types = [
            1 => '订单扣减',
            2 => '订单返回',
            3 => '人工入库',
            4 => '人工报损'
        ];
        if (empty($errors)) {
            $report = [
                'title' => '✅ 库存核验通过',
                'content' => '所有商品库存变化连续，没有发现问题。'
            ];
        }else{
            $errorCount = count($errors);
            $content = "发现 {$errorCount} 处库存不连续问题:\n\n";

            // 只展示前5个错误详情，避免消息过长
            $maxDisplay = 5;
            $displayed = min($maxDisplay, $errorCount);

            for ($i = 0; $i < $displayed; $i++) {
                $error = $errors[$i];
                $content .= "🔴 **商品**: {$error['sku_name']} (ID:{$error['goods_id']})\n";
                $content .= "📉 **差异数量**: {$error['discrepancy']}\n";
                $content .= "⏰ **时间**: " . date('Y-m-d H:i:s', $error['current_record']['time']) . "\n";
                $content .= "📝 **操作类型**: " . (isset($types[$error['current_record']['type']])?$types[$error['current_record']['type']]:'未知类型') . "\n";
                $content .= "🛒 **订单号**: {$error['current_record']['order_no']}\n";
                $content .= "➡️ **库存变化**: {$error['current_record']['old_stock']} → {$error['current_record']['new_stock']}\n";
                $content .= "---\n";
            }

            if ($errorCount > $maxDisplay) {
                $remaining = $errorCount - $maxDisplay;
                $content .= "\n...还有 {$remaining} 个问题未显示...\n";
            }

            $report = [
                'title' => "❌ 发现 {$errorCount} 处库存问题",
                'content' => $content
            ];
        }

        $data = [
            'msg_type' => 'interactive',
            'card' => [
                'header' => [
                    'title' => [
                        'tag' => 'plain_text',
                        'content' => $report['title']
                    ],
                    'template' => empty($report['errors']) ? 'green' : 'red'
                ],
                'elements' => [
                    [
                        'tag' => 'div',
                        'text' => [
                            'tag' => 'plain_text',
                            'content' => $report['content']
                        ]
                    ],
                    [
                        'tag' => 'hr'
                    ],
                    [
                        'tag' => 'note',
                        'elements' => [
                            [
                                'tag' => 'plain_text',
                                'content' => '📅 报告时间: ' . date('Y-m-d H:i:s')
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://open.feishu.cn/open-apis/bot/v2/hook/48c00646-2a97-418d-b8ce-1b173d59c46c');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        curl_close($ch);

    }

    // 获取采购单列表
    public function list()
    {
        $search  = isset($this->params['search']) ? $this->params['search'] : '';
        $condition = [];
        if ($search){
            $condition[] = ['order_no|remark', 'like', '%' . $search . '%'];
        }
        $orders = model('purchase_order')->getList($condition, '*', 'create_time desc');

        return json(['code' => 0, 'data' => $orders]);
    }

    // 获取采购单详情
    public function detail()
    {
        $orderNo  = isset($this->params['order_no']) ? $this->params['order_no'] : '';
        $order = model('purchase_order')->getInfo([['order_no','=',$orderNo]], '*');

        if (!$order) {
            return json(['code' => -1, 'message' => '采购单不存在']);
        }
        $goodsList = model('purchase_order_goods')->getList([['order_id','=',$order['id']]], '*');
        $order['goods_list'] = $goodsList;

        return json(['code' => 0, 'data' => $order]);
    }
    //核查多规格商品
    public function checkMultiSpec()
    {
        $goodsList = json_decode($this->params['goods_list'], true);
        $result = [
            'has_multi_spec' => false,
            'multi_spec_goods' => []
        ];

        foreach ($goodsList as $goods) {
            // 查询数据库检查该商品是否有多种规格
            $specs = model('goods_sku')->getInfo([['sku_id','=',$goods['sku_id']]], '*');

            if ($specs['weight']) {
                $daizhuang = Db::connect('v3')
                    ->table('baijiacms_goods')
                    ->where('code','=',$specs['sku_no'])
                    ->find();
                $daizhuan = Db::connect('v3')
                    ->table('baijiacms_goods')
                    ->where('code','=',$specs['weight'])
                    ->find();
                $specs_detail = [];
                $specs_detail[] = ['spec_id'=>$goods['sku_no'],'spec_name'=>$daizhuang['spec']];
                $specs_detail[] = ['spec_id'=>$specs['weight'],'spec_name'=>$daizhuan['spec']];
                $result['has_multi_spec'] = true;
                $result['multi_spec_goods'][] = [
                    'goods_name' => $goods['sku_name'],
                    'sku_id' => $goods['sku_id'],
                    'specs' => $specs_detail
                ];
            }
        }

        return json([
            'code' => 0,
            'data' => $result
        ]);
    }

    // 创建采购单
    public function create()
    {
        $goodsList = json_decode($this->params['goods_list'], true);
        $remark = $this->params['remark'];

        if (empty($goodsList)) {
            return json(['code' => -1, 'message' => '请选择采购商品']);
        }

        // 生成采购单号
        $orderNo = 'XSDD' . date('YmdHis') . rand(100, 999);
        $data = [
            'order_no' => $orderNo,
            'remark' => $remark,
            'status' => 'pending',
            'create_time' => date('Y-m-d H:i:s'),
            'creator_id' => 1
        ];

        $orderId = model('purchase_order')->add($data);


        // 添加采购商品
        $orderGoods = [];
        foreach ($goodsList as $goods) {
            $orderGoods = [
                'order_id' => $orderId,
                'sku_id' => $goods['sku_id'],
                'sku_no' => $goods['sku_no'],
                'sku_name' => $goods['sku_name'],
                'quantity' => $goods['quantity']
            ];
            model('purchase_order_goods')->add($orderGoods);
        }
        //通过库存系统
        $result = Db::connect('v3')
            ->table('baijiacms_goods')
            ->whereIn('code',array_column($goodsList, 'sku_no'))
            ->field('*')
            ->select()
            ->toArray();
        $res = [];
        foreach ($result as $key=>$vo){
            $res[$vo['code']] = $vo;
        }
        $total = 0;
        foreach ($goodsList as $goods) {
            $total = $total + (isset($res[$goods['sku_no']])?$res[$goods['sku_no']]['sell']*$goods['quantity']:0);
        }
        $data = [
            'frame'=>0,
            'customer'=>2,
            'time'=>time(),
            'number'=>$orderNo,
            'total'=>$total,
            'actual'=>0,
            'people'=>0,
            'arrival'=>0,
            'logistics'=>'{"key":"auto","name":"自动识别","number":""}',
            'file'=>'[]',
            'more'=>'{}',
            'examine'=>1,
            'state'=>0,
            'user'=>1
        ];
        $insertId = Db::connect('v3')->table('baijiacms_sor')->insertGetId($data);
        foreach ($goodsList as $goods) {
            if (isset($res[$goods['sku_no']])){
                $data = [
                    'pid'=>$insertId,
                    'goods'=>$res[$goods['sku_no']]['id'],
                    'attr'=>'',
                    'unit'=>'瓶',
                    'warehouse'=>1,
                    'price'=>$res[$goods['sku_no']]['sell'],
                    'nums'=>$goods['quantity'],
                    'discount'=>0,
                    'dsc'=>0,
                    'total'=>$res[$goods['sku_no']]['sell']*$goods['quantity'],
                    'tax'=>0,
                    'tat'=>0,
                    'tpt'=>$res[$goods['sku_no']]['sell']*$goods['quantity'],
                    'data'=>'',
                    'handle'=>0
                ];
                Db::connect('v3')->table('baijiacms_sor_info')->insert($data);
            }
        }
        return json(['code' => 0, 'message' => '采购单创建成功', 'data' => ['order_no' => $orderNo]]);
    }

    // 获取报损单列表
    public function dlist()
    {
        $search  = isset($this->params['search']) ? $this->params['search'] : '';
        $condition = [];
        if ($search){
            $condition[] = ['damage_no|remark', 'like', '%' . $search . '%'];
        }
        $orders = model('damage_order')->getList($condition, '*', 'create_time desc');

        return json(['code' => 0, 'data' => $orders]);

    }

    // 获取报损单详情
    public function ddetail()
    {
        $damageNo  = isset($this->params['damage_no']) ? $this->params['damage_no'] : '';
        $order = model('damage_order')->getInfo([['damage_no','=',$damageNo]], '*');

        if (!$order) {
            return json(['code' => -1, 'message' => '报损单不存在']);
        }
        $goodsList = model('damage_order_goods')->getList([['order_id','=',$order['id']]], '*');
        $order['goods_list'] = $goodsList;

        return json(['code' => 0, 'data' => $order]);
    }

    // 创建报损单
    public function dcreate()
    {
        $goodsList = json_decode($this->params['goods_list'], true);
        $remark = $this->params['remark'];
        $unit = $this->params['unit'];
        $reason = $this->params['reason'];

        if (empty($goodsList)) {
            return json(['code' => -1, 'message' => '请选择报损商品']);
        }

        if (empty($unit)) {
            return json(['code' => -1, 'message' => '请选择报损单位']);
        }

        if (empty($reason)) {
            return json(['code' => -1, 'message' => '请选择报损原因']);
        }

        // 生成采购单号
        $damageNo = 'BSD' . date('YmdHis') . rand(100, 999);
        $data = [
            'damage_no' => $damageNo,
            'unit' => $unit,
            'reason' => $reason,
            'remark' => $remark,
            'status' => 'completed',
            'create_time' => date('Y-m-d H:i:s'),
            'creator_id' => 1
        ];

        $orderId = model('damage_order')->add($data);



        // 添加采购商品
        $orderGoods = [];
        foreach ($goodsList as $goods) {
            $orderGoods = [
                'order_id' => $orderId,
                'sku_id' => $goods['sku_id'],
                'sku_no' => $goods['sku_no'],
                'sku_name' => $goods['sku_name'],
                'quantity' => $goods['quantity']
            ];
            model('damage_order_goods')->add($orderGoods);

            //报损
            $sku_stock_data = [
                'sku_id' => $goods['sku_id'],
                'num'    => $goods['quantity']
            ];
            $remark=[
                'type'=>4,
                'remark'=>'人工报损，原因：'.$reason
            ];
            $goods_sku_model = new GoodsStock();
            $goods_sku_model->decStock($sku_stock_data,$remark);
        }

        return json(['code' => 0, 'message' => '报损单创建成功', 'data' => ['order_no' => $damageNo]]);

    }

    // 创建入库单
    public function createStockIn()
    {
        $packageId = $this->params['package_id'];
        $scannedItems = json_decode($this->params['scanned_items'], true);
        $actualCount = $this->params['actual_count'];
        $jihua = $this->params['jihua'];
        $remark = isset($this->params['remark'])?$this->params['remark']:'';

        // 验证数据
        if (empty($packageId) || empty($scannedItems)) {
            return json(['code' => 1, 'message' => '参数不完整']);
        }

        // 判断编码是否存在
        foreach ($scannedItems as $itemNo) {
            $product =  model('goods_sku')->getInfo([['sku_no','=',$itemNo]], '*');
            if (!$product){
                $product =  model('goods_sku')->getInfo([['weight','=',$itemNo]], '*');
                if (!$product){
                    return json(['code' => 1, 'message' => $itemNo.'编码不存在，请及时录入！']);
                }
            }
        }


        $package = model('stock_in')->getInfo([['package_no','=',$packageId]], '*');
        if ($package) {
            return json(['code' => 1, 'message' => '该包裹已接收']);
        }

        $sell = Db::connect('v3')
            ->table('baijiacms_sell')
            ->where('number','=',$packageId)
            ->find();
        $result = Db::connect('v3')
            ->table('baijiacms_sell_info')
            ->alias('og')
            ->join('baijiacms_goods o', 'og.goods = o.id')
            ->where('og.pid','=',$sell['id'])
            ->field('o.name,o.code,og.nums,o.spec,o.fenzhuang')
            ->select()
            ->toArray();

        // 创建入库单主记录
        $stockInId = model('stock_in')->add([
            'package_no' => $packageId,
            'expected_quantity' => $jihua,
            'actual_quantity' => $actualCount,
            'difference' => $actualCount - $jihua,
            'remark' => $remark,
            'create_time' => time(),
            'status' => 1 // 1表示已完成
        ]);

        // 创建入库单明细
        $items = [];
        $detail =[];
        foreach ($scannedItems as $itemNo) {
            if (isset($detail[$itemNo])){
                $detail[$itemNo] = $detail[$itemNo] +1;
            }else{
                $detail[$itemNo] = 1;
            }
        }
        foreach ($result as $key=>$vo){
            $product =  model('goods_sku')->getInfo([['sku_no|weight','=',$itemNo]], '*');
            preg_match('/\d+/', $vo['spec'], $matches);
            if ($vo['fenzhuang']==1){
                $fenzhuang = 1;
            }else{
                $fenzhuang = isset($matches[0])?$matches[0]:0;
            }
            $items = [
                'stock_in_id' => $stockInId,
                'product_id' => $product['sku_id'],
                'product_no' => $vo['code'],
                'product_name' => $vo['name'],
                'fenzhuang' => $fenzhuang,
                'expected_quantity' => $vo['nums'], // 假设每个扫码对应1个商品
                'actual_quantity' => isset($detail[$vo['code']])?$detail[$vo['code']]:0,
                'create_time' => time()
            ];
            model('stock_in_item')->add($items);
            //入库
            $sku_stock_data = [
                'sku_id' => $product['sku_id'],
                'num'    => (isset($detail[$vo['code']])?$detail[$vo['code']]:0)*$fenzhuang
            ];
            $remark=[
                'type'=>3,
                'remark'=>$packageId.'包裹入库'
            ];
            $goods_sku_model = new GoodsStock();
            $goods_sku_model->incStock($sku_stock_data,$remark);
        }


        return json([
            'code' => 0,
            'message' => '入库成功',
            'data' => [
                'stock_in_no' => $packageId,
                'package_no' => $packageId,
                'expected_quantity' => $jihua,
                'actual_quantity' => $actualCount,
                'items' => $items
            ]
        ]);
    }

    // 获取入库单详情
    public function getStockInDetail()
    {
        $order = new OrderCommonModel();
        $res   = $order->orderPayPay();//订单支付完成
        exit;
        //判断商品是否还有库存，是否已下架
        $alias       = 'og';
        $join        = [
            [
                'goods o',
                'o.goods_id = og.goods_id',
                'left'
            ]
        ];
        $goodslist = model('goods_sku')->getList([['o.goods_id','=',442]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
        //商品属性成分
        $goods_attr_formats = [];
        foreach ($goodslist as $key =>$vo) {
            //统计商品属性成分
            if ($vo['goods_attr_format']) {
                print_r($vo['goods_attr_format']);
                $goods_attr_format = json_decode($vo['goods_attr_format'], true);
                print_r($goods_attr_format);
                foreach ($goods_attr_format as $kkk => $vvv) {
                    //$goods_attribute = model('goods_attribute')->getInfo([['attr_id', "=", $vvv['attr_id']]]);
                    //$vvv['attr_name_max'] = isset($goods_attribute)?$goods_attribute['attr_name_max']:'';
                    if ((strpos($vvv['attr_name'], '钒') !== false) || (strpos($vvv['attr_name'], '硼') !== false) || (strpos($vvv['attr_name'], '氯') !== false) || (strpos($vvv['attr_name'], '钠') !== false) || (strpos($vvv['attr_name'], '碘') !== false) || (strpos($vvv['attr_name'], '钼') !== false) || (strpos($vvv['attr_name'], '锰') !== false) || (strpos($vvv['attr_name'], '铬') !== false) || (strpos($vvv['attr_name'], '铜') !== false) || (strpos($vvv['attr_name'], '硒') !== false) || (strpos($vvv['attr_name'], '磷') !== false) || (strpos($vvv['attr_name'], '钾') !== false) || (strpos($vvv['attr_name'], '铁') !== false) || (strpos($vvv['attr_name'], '锌') !== false) || (strpos($vvv['attr_name'], '镁') !== false) || (strpos($vvv['attr_name'], '钙') !== false) || (strpos($vvv['attr_name'], '维生素') !== false)) {
                        if (isset($goods_attr_formats[$vvv['attr_name']])) {
                            $goods_attr_formats[$vvv['attr_name']]['attr_value_name'] = $goods_attr_formats[$vvv['attr_name']]['attr_value_name'] + $vvv['attr_value_name'];
                        } else {
                            $goods_attr_formats[$vvv['attr_name']] = $vvv;
                        }
                    }
                }
                print_r($goods_attr_format);
            }
        }
        exit;
        $stockInNo = $this->params['stock_in_no'];

        // 获取入库单主信息
        $stockIn = model('stock_in')->getInfo([['package_no','=',$stockInNo]], '*');

        if (!$stockIn) {
            return json(['code' => 1, 'message' => '入库单不存在']);
        }

        // 获取入库单明细
        $items = model('stock_in_item')->getList([['stock_in_id','=',$stockIn['id']]], '*');

        return json([
            'code' => 0,
            'message' => '成功',
            'data' => [
                'stock_in_no' => $stockIn['package_no'],
                'package_no' => $stockIn['package_no'],
                'expected_quantity' => $stockIn['expected_quantity'],
                'actual_quantity' => $stockIn['actual_quantity'],
                'remark' => $stockIn['remark'],
                'create_time' => date('Y-m-d H:i:s', $stockIn['create_time']),
                'items' => $items
            ]
        ]);
    }

    public function devery(){
        $order_model = new OrderModel();
        $data        = array(
            "type"               => input('type', 'manual'),//发货方式（手动发货、电子面单）
            "order_goods_ids"    => input("order_goods_ids", ''),//商品id
            "express_company_id" => input("express_company_id", 10),//物流公司
            "delivery_no"        => input("delivery_no", ''),//快递单号
            "order_id"           => input("order_id", 0),//订单id
            "delivery_type"      => input("delivery_type", 1),//是否需要物流
            "site_id"            => 1,
            "template_id"        => input('template_id', 0)//电子面单模板id
        );
        $ordersn = input("ordersn",'xxx');
        $order_goods_sku_no =  input("order_goods_sku_no",'xxx');
        $order_extend = model('order')->getInfo([['extend_id|invoice_full_address', "=",$ordersn]]);
        $data['order_id'] = $order_extend['order_id'];
        $order_goods_id_array = model("order_goods")->getColumn(
            [
                ['order_id', '=', $data['order_id']],
                ['sku_no', "in", $order_goods_sku_no]
            ],
            'order_goods_id'
        );
        $data['order_goods_ids'] = implode(',',$order_goods_id_array);
        $result = $order_model->orderGoodsDelivery($data);
        return $result;
    }

}