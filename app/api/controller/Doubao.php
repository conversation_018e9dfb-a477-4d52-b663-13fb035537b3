<?php
namespace app\api\controller;

use app\model\ai\AiModel;
use app\api\controller\BaseApi;
use think\File;
use Smalot\PdfParser\Parser;
use app\model\upload\Upload as UploadModel;

class Doubao extends BaseApi
{

    private $apiKey;
    private $model;
    private $apiUrl;
    
    public function __construct()
    {
        parent::__construct();
        $this->apiKey = config('ai_config.doubao.api_key');
        $this->model = config('ai_config.doubao.model');
        $this->apiUrl = config('ai_config.doubao.api_url');
    }

    /**
     * PDF文件分析接口
     * @return \think\response\Json
     */
    public function analyzePdf()
    {
        $prompt = isset($this->params['prompt']) ? $this->params['prompt'] : '请分析这份检测报告';
        
        if (empty($prompt)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        try {
            // 使用UploadModel处理文件上传
            $upload_model = new UploadModel($this->site_id);
            $param = array(
                "thumb_type" => "",
                "name" => "file"
            );
            $result = $upload_model->setPath("temp/pdf/" . date('Ymd') . '/')->image($param);
            
            if (!isset($result['code']) || $result['code'] < 0) {
                return json(['code' => 400, 'msg' => isset($result['message']) ? $result['message'] : '文件上传失败']);
            }
            
            $file = new \think\File($result['data']['path']);
            
            // 创建AI模型实例
            $ai = new AiModel($this->apiKey, $this->model, $this->apiUrl);
            
            // 使用新的sendPdfRequest方法
            $result = $ai->sendPdfRequest($file->getPathname(), $prompt);
            
            return json($result);
        } catch (\Exception $e) {
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    

    /**
     * AI聊天接口
     * @return \think\response\Json
     */
    public function chat()
    {
        $apiKey = isset($this->params['api_key']) ? $this->params['api_key'] : $this->apiKey;
        $messages = isset($this->params['messages']) ? $this->params['messages'] : [
            ['role' => 'user', 'content' => '你好'],
            ['role' => 'assistant', 'content' => '你好！有什么我可以帮助你的吗？'],
            ['role' => 'user', 'content' => '介绍一下你们的产品']
        ];
        $model = isset($this->params['model']) ? $this->params['model'] : $this->model;
        $apiUrl = isset($this->params['api_url']) ? $this->params['api_url'] : $this->apiUrl;
        
        if (empty($apiKey) || empty($messages)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        try {
            $ai = new AiModel($apiKey, $model, $apiUrl);
            $result = $ai->sendRequest($messages);
            
            return json($result);
        } catch (\Exception $e) {
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    
    /**
     * 流式聊天接口
     * @return \think\response\Json
     */
    public function streamChat()
    {
        $apiKey = isset($this->params['api_key']) ? $this->params['api_key'] : '';
        $messages = isset($this->params['messages']) ? $this->params['messages'] : [];
        $model = isset($this->params['model']) ? $this->params['model'] : '';
        $apiUrl = isset($this->params['api_url']) ? $this->params['api_url'] : $this->apiUrl;
        
        if (empty($apiKey) || empty($messages)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        try {
            $ai = new AiModel($apiKey, $model, $apiUrl);
            
            $response = new \think\Response();
            $response->header('Content-Type', 'text/event-stream');
            $response->header('Cache-Control', 'no-cache');
            
            $ai->streamChat($messages, function($content, $done) {
                if ($done) {
                    echo "data: [DONE]\n\n";
                } else {
                    echo "data: " . json_encode(['content' => $content]) . "\n\n";
                }
                ob_flush();
                flush();
            });
            
            return $response;
        } catch (\Exception $e) {
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }

    /**
     * 图片分析接口
     * @return \think\response\Json
     */
    public function analyzeImage()
    {
        $prompt = isset($this->params['prompt']) ? $this->params['prompt'] : '请分析这张图片';
        
        if (empty($prompt)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        try {
            // 使用UploadModel处理文件上传
            $upload_model = new UploadModel($this->site_id);
            $param = array(
                "thumb_type" => "",
                "name" => "file"
            );
            $result = $upload_model->setPath("temp/image/" . date('Ymd') . '/')->image($param);
            
            if (!isset($result['code']) || $result['code'] < 0) {
                return json(['code' => 400, 'msg' => isset($result['message']) ? $result['message'] : '文件上传失败']);
            }
            
            $file = new \think\File($result['data']['path']);
            
            // 创建AI模型实例
            $ai = new AiModel($this->apiKey, $this->model, $this->apiUrl);
            
            // 使用新的sendImageRequest方法
            $result = $ai->sendImageRequest($file->getPathname(), $prompt);
            
            return json($result);
        } catch (\Exception $e) {
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    
    /**
     * 获取可用模型列表
     * @return \think\response\Json
     */
    public function getModels()
    {
        try {
            $ai = new AiModel($this->apiKey, $this->model, $this->apiUrl);
            $models = $ai->getAvailableModels();
            
            return json([
                'code' => 0,
                'data' => $models,
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
}