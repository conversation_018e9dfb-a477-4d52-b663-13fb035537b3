<?php
/**
 * Goodssku.php
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2015-2025 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: http://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 * <AUTHOR> niuteam
 * @date : 2015.1.17
 * @version : v1.0.0.0
 */

namespace app\api\controller;

use app\model\goods\Goods;
use app\model\goods\GoodsService;
use app\model\order\OrderCommon as OrderCommonModel;
use addon\coupon\model\CouponType;

/**
 * 商品sku
 * <AUTHOR>
 *
 */
class Goodssku extends BaseApi
{

    /**
     * 基础信息
     */
    public function info()
    {
        $sku_id = isset($this->params['sku_id']) ? $this->params['sku_id'] : 0;
        if (empty($sku_id)) {
            return $this->response($this->error('', 'REQUEST_SKU_ID'));
        }
        $goods = new Goods();
        $field = 'goods_id,sku_id,goods_name,sku_name,sku_spec_format,price,market_price,discount_price,promotion_type,start_time,end_time,stock,sku_image,sku_images,goods_spec_format';
        $info  = $goods->getGoodsSkuInfo([['sku_id', '=', $sku_id], ['site_id', '=', $this->site_id]], $field);

        $token = $this->checkToken();
        if ($token['code'] >= 0) {
            // 是否参与会员等级折扣
            $goods_member_price = $goods->getGoodsPrice($sku_id, $this->member_id);
            $goods_member_price = $goods_member_price['data'];
            if (!empty($goods_member_price['member_price'])) {
                $info['data']['member_price'] = $goods_member_price['member_price'];
            }
        }
        return $this->response($info);
    }

    /**
     * 详情信息
     */
    public function detail()
    {
        $sku_id = isset($this->params['sku_id']) ? $this->params['sku_id'] : 0;
        if (empty($sku_id)) {
            return $this->response($this->error('', 'REQUEST_SKU_ID'));
        }

        $res = [];

        $goods                   = new Goods();
        $goods_sku_detail        = $goods->getGoodsSkuDetail($sku_id, $this->site_id);
        $goods_sku_detail        = $goods_sku_detail['data'];
        $res['goods_sku_detail'] = $goods_sku_detail;

        if (empty($goods_sku_detail)) return $this->response($this->error($res));

        $res['goods_sku_detail']['purchased_num'] = 0; // 该商品已购数量

        $token = $this->checkToken();
        if ($token['code'] >= 0) {
            // 是否参与会员等级折扣
            $goods_member_price = $goods->getGoodsPrice($sku_id, $this->member_id);
            $goods_member_price = $goods_member_price['data'];
            if (!empty($goods_member_price['member_price'])) {
                $res['goods_sku_detail']['member_price'] = $goods_member_price['member_price'];
            }
            if ($goods_sku_detail['max_buy'] > 0) $res['goods_sku_detail']['purchased_num'] = $goods->getGoodsPurchasedNum($goods_sku_detail['goods_id'], $this->member_id);
        }

        // 商品服务
        $goods_service                            = new GoodsService();
        $goods_service_list                       = $goods_service->getServiceList([['site_id', '=', $this->site_id], ['id', 'in', $res['goods_sku_detail']['goods_service_ids']]], 'service_name,desc');
        $res['goods_sku_detail']['goods_service'] = $goods_service_list['data'];

        return $this->response($this->success($res));
    }

    /**
     * 列表信息
     */
    public function lists()
    {

        $condition   = [];
        $condition[] = ['gs.site_id', '=', $this->site_id];

        $condition[] = ['gs.sku_no', '>', 0];
        $condition[] = ['gs.is_delete', '=', 0];
        $field       = 'g.goods_name,g.goods_chi,gs.sku_no,gs.sku_id';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];

        $goods = new Goods();
        $list  = $goods->getGoodsSkuList($condition,  $field, 'gs.sku_id desc', null,$alias, $join);
        $result = [];
        $result['MedTable']=[];
        if (!empty($list['data'])) {
            foreach ($list['data'] as $k => $v) {
                $res = [
                    'medicine_HisID'=>$v['sku_no'],
                    'medicine_ProductName'=>$v['goods_name'],
                    'medicine_GenericName'=>$v['goods_chi'],
                    'medicine_Specs'=>'',
                    'medicine_FactoryName'=>'',
                    'medicine_FactoryID'=>'',
                    'medicine_PinYinCode'=>'',
                    'medicine_BarCode'=>'',
                    'medicine_Hint'=>''
                ];
                $result['MedTable'][]=$res;
            }
        }
        return json_encode($result);
        exit;
    }

    public function orders()
    {

        $condition  = [
            ["site_id", "=", $this->site_id],
            ['is_delete', '=', 0],
            ["order_status", "=", 1]
        ];

        $order_by = 'pay_time asc';

        $order_list = model('order')->getList($condition, '*', $order_by);
        $result = [];
        $result['PreInfoTable']=[];
        if (!empty($order_list)) {
            $alias = 'og';
            $join = [
                [
                    'goods o',
                    'o.goods_id = og.goods_id',
                    'left'
                ]
            ];
            $order_goods_lists = model("order_goods")->getList([['order_id', 'in', array_column($order_list, 'order_id')]], 'og.*', 'og.order_goods_id desc', $alias, $join);
            $goods_skus = model('goods_sku')->getList([['sku_id', 'in', array_column($order_goods_lists, 'sku_id')]], '*');
            $goods_skuss = [];
            foreach ($goods_skus as $key => $vo) {
                $goods_skuss[$vo['sku_id']] = $vo['sku_no'];
            }
            foreach ($order_list as $k => $v) {
                //订单处方
                $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $v['out_trade_no']]], '*');

                $goods_name = json_decode($attr_class_info['goods_name'], true);
                $goods_num = json_decode($attr_class_info['goods_num'], true);
                $goods_sku = [];
                $time_name = ['earlymoring' => '晨起', 'moning' => '早餐', 'aftnoon' => '午餐', 'canjian' => '餐间', 'night' => '晚餐', 'evening' => '睡前', 'teshu' => '出差周转'];
                foreach ($goods_name as $key => $vo) {
                    $goods = explode('_', $vo);
                    if (!isset($goods_sku[$time_name[$goods[0]]])) {
                        $goods_sku[$time_name[$goods[0]]]['list'] = [];
                    }
                    $goods_sku[$time_name[$goods[0]]]['list'][] = [
                        'medicine_HisID' => $goods_skuss[$goods[1]],
                        'medicine_Number' => $goods_num[$key]
                    ];
                }

                $order_list[$k]['order_goods'] = $goods_sku;


                //天数
                $order_attr = model('order_attr')->getInfo([['order_id', '=', $v['out_trade_no']]], '*');
                $order_list[$k]['day'] = $order_attr['day'];


            }

            foreach ($order_list as $k => $v) {
                $member_info = model('member')->getInfo([['member_id', "=", $v['member_id']]], 'username');
                foreach ($v['order_goods'] as $kk => $vv) {
                    $res = [
                        'prescription_HisID'=>$v['order_no'],
                        'bag_Number'=>$v['day'],
                        'patient_Name'=>$member_info['username'],
                        'prescription_Taketime'=>$kk,
                        'MedList'=>$vv['list']
                    ];
                    $result['PreInfoTable'][]=$res;
                }

            }


        }
        return json_encode($result);
        exit;
    }

    public function cancel()
    {

        $condition  = [
            ["site_id", "=", $this->site_id],
            ['is_invoice', '=', 1],
            ["order_status", "=", 1]
        ];

        $order_by = 'pay_time asc';

        $order_list = model('order')->getList($condition, '*', $order_by);
        $result = [];
        $result['PreInfoTable']=[];
        if (!empty($order_list)) {
            foreach ($order_list as $k => $v) {
                $result['PreInfoTable'][]=['prescription_HisID'=>$v['order_no']];
            }

        }
        return json_encode($result);
        exit;
    }

    public function do()
    {
        $goodsmodel = new Goods();
        $prescription_HisID      = isset($this->params['prescription_HisID']) ? $this->params['prescription_HisID'] : '';
        $status      = isset($this->params['status']) ? $this->params['status'] : 0;
        $result = [];
        $result['StateTable']=[];
        if (!$prescription_HisID){
            $result['StateTable']=[
                'Result'=>1,
                'Msg'=>'医嘱订单号不能为空'
            ];
            return json_encode($result);
            exit;
        }
        if (!$status){
            $result['StateTable']=[
                'Result'=>1,
                'Msg'=>$prescription_HisID.'操作状态不能为空'
            ];
            return json_encode($result);
            exit;
        }
        $order_info = model('order')->getInfo([['order_no', "=", $prescription_HisID]], '*');
        if (!$order_info){
            $result['StateTable']=[
                'Result'=>1,
                'Msg'=>$prescription_HisID.'找不到该医嘱'
            ];
            return json_encode($result);
            exit;
        }

        if ($status==1){
            //分包成功
        }
        if ($status==3){
            //取消成功
        }
        $result['StateTable']=[
            'Result'=>0,
            'Msg'=>''
        ];
        return json_encode($result);
        exit;
    }

    /**
     * 商品推荐
     * @return string
     */
    public function recommend()
    {
        $page      = isset($this->params['page']) ? $this->params['page'] : 1;
        $page_size = isset($this->params['page_size']) ? $this->params['page_size'] : PAGE_LIST_ROWS;
        $condition = [
            ['gs.goods_state', '=', 1],
            ['gs.is_delete', '=', 0],
            ['gs.site_id', '=', $this->site_id]
        ];
        $goods     = new Goods();
        $field     = 'gs.goods_id,gs.sku_id,gs.sku_name,gs.price,gs.market_price,gs.discount_price,gs.stock,(gs.sale_num + gs.virtual_sale) as sale_num,gs.sku_image,gs.goods_name,gs.site_id,gs.is_free_shipping,gs.introduction,gs.promotion_type,g.goods_image';
        $alias     = 'gs';
        $join      = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $order_by  = 'gs.sort desc,gs.create_time desc';
        $list      = $goods->getGoodsSkuPageList($condition, $page, $page_size, $order_by, $field, $alias, $join);

        $token = $this->checkToken();
        if (!empty($list['data']['list'])) {
            foreach ($list['data']['list'] as $k => $v) {

                if ($token['code'] >= 0) {
                    // 是否参与会员等级折扣
                    $goods_member_price = $goods->getGoodsPrice($v['sku_id'], $this->member_id);
                    $goods_member_price = $goods_member_price['data'];
                    if (!empty($goods_member_price['member_price'])) {
                        $list['data']['list'][$k]['member_price'] = $goods_member_price['member_price'];
                    }
                }
            }
        }
        return $this->response($list);
    }
}