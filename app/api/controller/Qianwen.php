<?php
namespace app\api\controller;

use app\model\ai\AiModel;
use app\api\controller\BaseApi;
use think\File;
use Smalot\PdfParser\Parser;
use app\model\upload\Upload as UploadModel;
use app\model\ai\QwenModel;

class <PERSON>anwen extends BaseApi
{

    private $apiKey = '';
    private $model = ''; // 千问默认模型
    private $apiUrl = ''; // 千问API地址
    
    public function __construct()
    {
        parent::__construct();
        $this->apiKey = config('ai_config.qianwen.api_key');
        $this->model = config('ai_config.qianwen.model');
        $this->apiUrl = config('ai_config.qianwen.api_url');
    }

    /**
     * PDF文件分析接口
     * @return \think\response\Json
     */
    public function analyzePdf()
    {
        $prompt = isset($this->params['prompt']) ? $this->params['prompt'] : config('prompt_config.analysis_prompt');
        $stream = isset($this->params['stream']) ? (bool)$this->params['stream'] : true; // 默认使用流式请求
        $member_id = $this->params['member_id'] ?? 0;
        $user_id = $this->params['user_id'] ?? 0;
        
        if (empty($prompt)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        try {
            $pdfLocalPath = null;
            
            // 创建模型实例
            $reportModel = new \app\model\ai\UserReport();
            $logModel = new \app\model\ai\AiRequestLog();
            // 使用UploadModel处理文件上传
            $upload_model = new UploadModel($this->site_id);
            $param = array(
                "thumb_type" => "",
                "name" => "file",
                "extend_type" => ["pdf"] // 限制只允许上传PDF文件
            );
            
            // 检查并创建上传目录
            $upload_path = "pdf/" . date('Ymd') . '/';
            if (!is_dir($upload_path) && !mkdir($upload_path, 0755, true)) {
                return json(['error' => ['message' => '无法创建上传目录: ' . $upload_path, 'type' => 'directory_error', 'code' => 'directory_create_failed']], 500);
            }
            
            $pdfUploadResult = $upload_model->setPath($upload_path)->ossUpload($param, false);
            if ($pdfUploadResult['code'] < 0) {
                throw new \Exception($pdfUploadResult['message']);
            }
            
            $pdfLocalPath = $pdfUploadResult['data']['local_path'];
            $pdfOssPath = $pdfUploadResult['data']['path'];
            
            $file = new \think\File($pdfLocalPath);
            
            // 创建报告记录
            $reportData = [
                'user_id' => $user_id,
                'member_id' => $member_id,
                'file_path' => $pdfOssPath,
                'file_name' => $file->getFilename(),
                'file_size' => $file->getSize(),
                'file_type' => $file->getMime(),
                'analysis_status' => 1, // 分析中
                'site_id' => $this->site_id
            ];
            $reportId = $reportModel->createReport($reportData);
            if ($reportId === false) {
                throw new \Exception("用户报告记录创建失败.");
            }
            
            // 创建QwenModel实例
            $qwen = new QwenModel($this->apiKey, $this->model, $this->apiUrl);

            // 将PDF转换为图片并上传到OSS
            $imageConversionResult = $this->convertPdfToImagesAndUpload($pdfLocalPath, $this->site_id, $file->getFilename());
            if ($imageConversionResult['code'] < 0) {
                throw new \Exception("PDF图片转换或上传失败: " . ($imageConversionResult['message'] ?? '未知错误'));
            }
            
            $imageUrlList = $imageConversionResult['data'];
            
            // 更新报告记录，保存图片URL列表
            $reportModel->where('id', $reportId)->update([
                'file_list' => json_encode($imageUrlList),
                'analysis_status' => 1
            ]);
            
            // 准备请求参数
            $messages = $qwen->buildMultiModalMessage($prompt, $imageUrlList);
            
            // 创建请求日志
            $logData = [
                'user_id' => $user_id,
                'member_id' => $member_id,
                'report_id' => $reportId,
                'prompt' => $prompt,
                'messages' => json_encode($messages),
                'status' => 1, // 处理中
                'model_name' => $this->model,
                'api_url' => $this->apiUrl,
                'request_params' => json_encode($this->params),
                'site_id' => $this->site_id
            ];
            $logId = $logModel->createLog($logData);
            
            // 根据stream参数决定是否使用流式请求
            if ($stream) {
                // 设置全局变量以传递ID给streamRequest方法
                $GLOBALS['logId'] = $logId;
                $GLOBALS['reportId'] = $reportId;
                
                // 使用流式请求，直接输出结果
                $response = $qwen->sendRequest($messages, 0.7, 0.9, 1500, true);
                
                // 更新日志状态
                $logModel->updateResponse($logId, [], null);
                
                return $response;
            } else {
                // 使用普通请求，返回JSON
                $result = $qwen->sendRequest($messages);
                
                // 更新报告和日志
                $reportModel->updateAnalysisResult($reportId, json_encode($result), '');
                $logModel->updateResponse($logId, $result, null);
                
                return json($result);
            }
        } catch (\Exception $e) {
            // 清理原始PDF文件
            if ($pdfLocalPath && file_exists($pdfLocalPath)) {
                @unlink($pdfLocalPath);
            }
            
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }

    // TODO 测试代码 要在前端测试返回的内容,使用content3的内容,去调试,不请求模型了,浪费token
    /**
     * 测试流式接口
     * @return \think\response\Json
     */
    public function testStream()
    {
        // 测试代码
        $content_str = 'data: {"content":" 贵州省人民医院"}';
        $split_str = explode("\n", $content_str);
        $str_arr = [];
        foreach ($split_str as $key => $value) {
            // 替换data:
            $value = str_replace('data: ', '', $value);
            $value = json_decode($value, true);
            if (empty($value['content'])) continue;
            $str_arr[] = $value['content'];
        }

        // 模拟流式响应数据
        $contents = $str_arr;

        // 设置响应头，支持流式输出
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // 禁用Nginx缓冲
        
        // 关闭输出缓冲
        if (ob_get_level()) ob_end_clean();
        
        foreach ($contents as $content) {
            echo "data: " . json_encode(['content' => $content], JSON_UNESCAPED_UNICODE) . "\n\n";
            flush();
            usleep(10000); // 100ms间隔
        }
        
        echo "data: [DONE]\n\n";
        flush();
        
        exit; // 终止后续执行
    }
    
    /**
     * 图片分析接口
     * @return \think\response\Json
     */
    public function analyzeImages()
    {
        $prompt = isset($this->params['prompt']) ? $this->params['prompt'] : config('prompt_config.analysis_prompt');
        $stream = isset($this->params['stream']) ? (bool)$this->params['stream'] : true;
        $images = isset($this->params['images']) ? $this->params['images'] : [];
        $member_id = $this->params['member_id'] ?? 0;
        $user_id = $this->params['user_id'] ?? 0;

        //测试代码
        // $images = [
        //     'https://fenzhuang.oss-cn-shenzhen.aliyuncs.com/upload/1/analyse_image/20250504/20250504022245174633976529593.jpg',
        //     'https://fenzhuang.oss-cn-shenzhen.aliyuncs.com/upload/1/analyse_image/20250504/20250504022245174633976576710.jpg'
        // ];

        
        
        if (empty($prompt) || empty($images)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        
        // 校验图片URL格式
        foreach ($images as $image) {
            if (!preg_match('/^https?:\/\//i', $image)) {
                return json(['error' => ['message' => '图片URL格式不正确，必须以http或https开头', 'type' => 'invalid_request_error', 'code' => 'invalid_image_url']], 400);
            }
        }
        
        // 初始化模型
        $logModel = new \app\model\ai\AiRequestLog();
        $reportModel = new \app\model\ai\UserReport();
        
        // 创建请求日志
        $logData = [
            'user_id' => $user_id,
            'member_id' => $member_id,
            'prompt' => $prompt,
            'messages' => json_encode($images),
            'model_name' => $this->model,
            'api_url' => $this->apiUrl,
            'site_id' => $this->site_id
        ];
        $logId = $logModel->createLog($logData);
        
        // 创建用户报告记录
        $reportData = [
            'user_id' => $user_id,
            'member_id' => $member_id,
            'file_path' => implode(',', $images),
            'file_name' => '报告截图',
            'file_size' => 0,
            'file_type' => 'image/*',
            'file_count' => count($images),
            'file_list' => json_encode(array_map(function($img) {
                return [
                    'path' => $img,
                    'name' => basename($img),
                    'type' => 'image/*'
                ];
            }, $images)),
            'site_id' => $this->site_id
        ];
        $reportId = $reportModel->createReport($reportData);
        
        try {
            // 创建QwenModel实例
            $qwen = new QwenModel($this->apiKey, $this->model, $this->apiUrl);
            
            // 使用buildMultiModalMessage方法构建消息格式
            $messages = $qwen->buildMultiModalMessage($prompt, $images);
            
            // 根据stream参数决定是否使用流式请求
            if ($stream) {
                // 设置全局变量以传递ID给streamRequest方法
                $GLOBALS['logId'] = $logId;
                $GLOBALS['reportId'] = $reportId;
                $response = $qwen->sendRequest($messages, null, null, null, true);
                // 在响应中添加reportId
                if (is_array($response)) {
                    $response['reportId'] = $reportId;
                }
                return $response;
            } else {
                $result = $qwen->sendRequest($messages);
                
                // 更新日志状态
                $logModel->updateResponse($logId, $result);
                
                // 更新报告分析结果
                $reportModel->updateAnalysisResult($reportId, 
                    json_encode($result, JSON_UNESCAPED_UNICODE), 
                    $result['choices'][0]['message']['content'] ?? '');
                
                return json($result);
            }
        } catch (\Exception $e) {
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    

    /**
     * AI聊天接口
     * @return \think\response\Json
     */
    public function chat()
    {
        $apiKey = isset($this->params['api_key']) ? $this->params['api_key'] : $this->apiKey;
        // $messages = isset($this->params['messages']) ? $this->params['messages'] : [];
        $messages = [
            [
              "role" => "system",
              "content" =>"你是一个有帮助的AI助手"
            ],
            [
              "role" => "user",
              "content" => "你好，请介绍一下你自己"
            ]
        ];
        $model = isset($this->params['model']) ? $this->params['model'] : $this->model;
        $apiUrl = isset($this->params['api_url']) ? $this->params['api_url'] : $this->apiUrl;
        
        if (empty($apiKey) || empty($messages)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        // 初始化模型
        $logModel = new \app\model\ai\AiRequestLog();
        $reportModel = new \app\model\ai\UserReport();
        
        try {
            $qwen = new QwenModel($apiKey, $model, $apiUrl);
            $result = $qwen->sendRequest($messages);
            
            return json($result);
        } catch (\Exception $e) {
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    
    /**
     * 流式聊天接口
     * @return \think\response\Json
     */
    public function streamChat()
    {
        $apiKey = isset($this->params['api_key']) ? $this->params['api_key'] : '';
        $messages = isset($this->params['messages']) ? $this->params['messages'] : [];
        $model = isset($this->params['model']) ? $this->params['model'] : '';
        $apiUrl = isset($this->params['api_url']) ? $this->params['api_url'] : $this->apiUrl;
        
        if (empty($apiKey) || empty($messages)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        // 初始化模型
        $logModel = new \app\model\ai\AiRequestLog();
        $reportModel = new \app\model\ai\UserReport();
        
        try {
            $ai = new AiModel($apiKey, $model, $apiUrl);
            
            $response = new \think\response\Json([], 200);
            $response->header('Content-Type', 'text/event-stream');
            $response->header('Cache-Control', 'no-cache');
            
            $ai->streamChat($messages, function($content, $done) {
                if ($done) {
                    echo "data: [DONE]\n\n";
                } else {
                    echo "data: " . json_encode(['content' => $content]) . "\n\n";
                }
                ob_flush();
                flush();
            });
            
            return $response;
        } catch (\Exception $e) {
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }

    /**
     * 图片分析接口
     * @return \think\response\Json
     */
    public function analyzeImage()
    {
        $prompt = isset($this->params['prompt']) ? $this->params['prompt'] : '请分析这张图片';
        
        if (empty($prompt)) {
            return json(['error' => ['message' => '缺少必要参数', 'type' => 'invalid_request_error', 'code' => 'missing_parameters']], 400);
        }
        // 初始化模型
        $logModel = new \app\model\ai\AiRequestLog();
        $reportModel = new \app\model\ai\UserReport();
        
        try {
            // 使用UploadModel处理文件上传
            $upload_model = new UploadModel($this->site_id);
            $param = array(
                "thumb_type" => "",
                "name" => "file"
            );
            $result = $upload_model->setPath("temp/image/" . date('Ymd') . '/')->image($param);
            
            if (!isset($result['code']) || $result['code'] < 0) {
                return json(['code' => 400, 'msg' => isset($result['message']) ? $result['message'] : '文件上传失败']);
            }
            
            $file = new \think\File($result['data']['path']);
            
            // 创建AI模型实例
            $ai = new AiModel($this->apiKey, $this->model, $this->apiUrl);
            
            // 使用sendImageRequest方法
            $result = $ai->sendImageRequest($file->getPathname(), $prompt);
            
            return json($result);
        } catch (\Exception $e) {
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }
    
    /**
     * 获取可用模型列表
     * @return \think\response\Json
     */
    public function getModels()
    {
        // 初始化模型
        $logModel = new \app\model\ai\AiRequestLog();
        $reportModel = new \app\model\ai\UserReport();
        try {
            $ai = new AiModel($this->apiKey, $this->model, $this->apiUrl);
            $models = [
                // 千问模型列表
                'qwen-turbo',
                'qwen-plus',
                'qwen-max',
                'qwen-max-longcontext',
                'qwen-vl-plus', // 视觉语言模型
                'qwen-vl-max'   // 高级视觉语言模型
            ];
            
            return json([
                'code' => 0,
                'data' => $models,
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            // 更新日志状态为失败
            if (isset($logId)) {
                $logModel->updateResponse($logId, [], $e->getMessage());
            }
            
            // 更新报告状态为失败
            if (isset($reportId)) {
                $reportModel->where('id', $reportId)->update(['analysis_status' => 3]);
            }
            
            return json(['error' => ['message' => $e->getMessage(), 'type' => 'api_error', 'code' => 'internal_error']], 500);
        }
    }

    /**
     * 将PDF文件转换为图片并上传到OSS
     * @param string $pdfLocalPath PDF文件的本地完整路径
     * @param int $siteId 站点ID
     * @param string $pdfFileName PDF文件名（不含路径）
     * @return array
     */
    private function convertPdfToImagesAndUpload($pdfLocalPath, $siteId, $pdfFileName)
    {
        // 创建临时目录
        $tempDir = sys_get_temp_dir() . '/pdf_images/' . $siteId . '/' . uniqid() . '/';
        if (!is_dir($tempDir) && !mkdir($tempDir, 0755, true)) {
            return [
                'code' => -1,
                'message' => '无法创建临时目录',
                'data' => [],
                'temp_dir' => null
            ];
        }

        $uploadedImages = [];
        $tempFiles = [];

        try {
            // 构建转换命令
            $outputPattern = $tempDir . 'page_%d.jpg';
            $command = sprintf(
                'convert -density 200 -quality 90 %s %s 2>&1',
                escapeshellarg(public_path() . '/' . $pdfLocalPath),
                escapeshellarg($outputPattern)
            );

            // 执行转换命令
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                throw new \Exception('PDF转换失败: ' . implode("\n", $output));
            }

            // 获取生成的图片文件
            $imageFiles = glob($tempDir . 'page_*.jpg');
            if (empty($imageFiles)) {
                throw new \Exception('未生成任何图片文件');
            }

            // 实例化UploadModel
            $uploadModel = new UploadModel($siteId);

            // 上传每个图片到OSS
            foreach ($imageFiles as $index => $imageFile) {
                $pageNumber = $index + 1;
                $ossKey = sprintf(
                    '%d/pdf_images/%s/%s_page_%d.jpg',
                    $siteId,
                    date('Ymd'),
                    pathinfo($pdfFileName, PATHINFO_FILENAME),
                    $pageNumber
                );

                $result = $uploadModel->uploadToOssAndReturnPath($imageFile, $ossKey);
                
                if ($result['code'] >= 0) {
                    $uploadedImages[] = $result['data'];
                } else {
                    throw new \Exception('图片上传失败: ' . ($result['message'] ?? '未知错误'));
                }

                $tempFiles[] = $imageFile;
            }

            return [
                'code' => 0,
                'message' => '转换和上传成功',
                'data' => $uploadedImages,
                'temp_dir' => $tempDir
            ];

        } catch (\Exception $e) {
            return [
                'code' => -1,
                'message' => $e->getMessage(),
                'data' => [],
                'temp_dir' => $tempDir
            ];
        } finally {
            // 清理临时文件
            foreach ($tempFiles as $file) {
                if (file_exists($file)) {
                    @unlink($file);
                }
            }
            // 清理临时目录
            if (is_dir($tempDir)) {
                @rmdir($tempDir);
            }
        }
    }
}