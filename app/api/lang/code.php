<?php

return [
	'SUCCESS' => 0,
	'ERROR' => -1,
    'SITE_NOT_EXIST' => -2,
    'SITE_CLOSE' => -3,
	'FAIL' => -10001,
	'SAVE_SUCCESS' => 10002,
	'SAVE_FAIL' => -10002,
	'REQUEST_SUCCESS' => 10003,
	'REQUEST_FAIL' => -10003,
	'DELETE_SUCCESS' => 10004,
	'DELETE_FAIL' => -10004,
	'UNKNOW_ERROR' => -10005,
	'PARAMETER_ERROR' => -10006,
	'REQUEST_SITE_ID' => -10007,
	'REQUEST_APP_MODULE' => -10008,
	'TOKEN_NOT_EXIST' => -1,
	'TOKEN_ERROR' => -1,
	'TOKEN_EXPIRE' => -1,
	'CAPTCHA_FAILURE' => -1,
	'CAPTCHA_ERROR' => -1,
	'REQUEST_COUPON_TYPE_ID' => -1,
	'REQUEST_CAPTCHA_ID' => -1,
	'REQUEST_CAPTCHA_CODE' => -1,
	'REQUEST_SKU_ID' => -1,
	'REQUEST_NUM' => -1,
	'REQUEST_CART_ID' => -1,
	'REQUEST_CATEGORY_ID' => -1,
	'REQUEST_ID' => -1,
	'REQUEST_ORDER_ID' => -1,
	'REQUEST_GOODS_EVALUATE' => -1,
	'REQUEST_ORDER_STATUS' => -1,
	'REQUEST_DIY_ID_NAME' => -1,
	'REQUEST_TOPIC_ID' => -1,
	'REQUEST_SECKILL_ID' => -1,
	'REQUEST_KEYWORD' => -1,
	'REQUEST_GOODS_ID' => -1,
	'REQUEST_PINTUAN_ID' => -1,
	'REQUEST_EMAIL' => -1,
	'REQUEST_MOBILE' => -1,
	'REQUEST_GROUPBUY_ID' => -1,
	'REQUEST_RECHARGE_ID' => -1,
	'REQUEST_BL_ID' => -1,
	'REQUEST_NAME' => -1,
	'REQUEST_STORE_ID' => -1,
	'REQUEST_REAL_NAME' => -1,
	'REQUEST_WITHDRAW_TYPE' => -1,
	'REQUEST_BRANCH_BANK_NAME' => -1,
	'REQUEST_BRANCH_BANK_ACCOUNT' => -1,
    ''
];