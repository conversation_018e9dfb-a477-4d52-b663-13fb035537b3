<nc-component v-bind:data="data[index]" v-bind:class="['graphic-navigation']">

	<!-- 预览 -->
	<template slot="preview">
		<div class="preview-box" v-bind:style="{ paddingLeft : nc.paddingLeftRight + 'px', paddingRight : nc.paddingLeftRight + 'px', paddingTop : nc.paddingTopBottom + 'px', paddingBottom : nc.paddingTopBottom + 'px' }">
			<template v-if="nc.lazyLoad">
				<graphic-nav></graphic-nav>
			</template>
		</div>
	</template>
	
	<!-- 编辑 -->
	<template slot="edit">
	
		<template v-if="nc.lazyLoad">
			<graphic-nav-list></graphic-nav-list>
		</template>
		
	</template>
	
	<!-- 资源 -->
	<template slot="resource">
		
		<js>
			var RESOURCEPATH = "{$resource_path}";
			var STATICEXT_IMG ="STATIC_EXT/diyview/img";
		</js>
		<css src="{$resource_path}/graphic_nav/css/design.css"></css>
		<js src="{$resource_path}/graphic_nav/js/design.js"></js>
		
	</template>

</nc-component>