@CHARSET "UTF-8";
/*图片广告组件*/
.image-ads .selected-template-list ul{overflow: hidden;}
.image-ads .selected-template-list ul li{float:left;width:26%;text-align: center;border:1px solid #e5e5e5;padding:5px;margin: 0 10px 10px 0;cursor:pointer;background: #ffffff;}
.image-ads .selected-template-list ul li:nth-child(3){margin:0;}
.image-ads .selected-template-list ul li img{width:100%;}
.image-ads .selected-template-list ul li div{font-size:12px;}

.image-ads .add-item{border:1px dashed #e5e5e5;text-align: center;cursor:pointer;background: #ffffff;padding:10px;margin:10px 0;}
.image-ads .add-item p i{display: inline-block;height: 24px; line-height: 24px;font-size: 18px;font-style: normal;}
.image-ads .add-item p span{display: inline-block;height: 24px; line-height: 24px;}
.image-ads .add-item>span{vertical-align: middle;color:#999;font-size:12px;}
.image-ads p.hint{font-size:12px;color:#999;margin:10px 0;}

.image-ads .preview-draggable{padding:0;}
.image-ads .preview-draggable>ul li{text-align: center;height: 136px;background-color: #ebf8fd;color: #88c4dc;background-repeat: no-repeat;background-position: 50%;background-size:100%;position:relative;}
.image-ads .preview-draggable>ul li p{padding-top:45px;position:absolute;width:100%;text-align:center;}
.image-ads .preview-draggable>ul li p span:last-child{font-size:12px;}
.image-ads .preview-draggable>ul li>div{height:136px;}
.image-ads .preview-draggable>ul li>div img{width:100%;height:100%;}
.image-ads .preview-draggable>ul li>span{background: rgba(0,0,0,.6);position: absolute;bottom: 0;color: #ffffff;font-size: 12px;width: 100%;left: 0;}

.image-ads .preview-draggable .image-ads-item{text-align: center;position:relative;}
.image-ads .preview-draggable .image-ads-item p{text-align:center;height: 130px;background-color: #ebf8fd;color: #88c4dc;line-height: 130px;}
.image-ads .preview-draggable .image-ads-item>div img{width: auto;height: auto;max-width: 100%;max-height: 100%;}
.image-ads .preview-draggable .image-ads-item>span{background: rgba(0,0,0,.6);position: absolute;bottom: 0;color: #ffffff;font-size: 12px;width: 100%;left: 0;line-height: 30px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;padding: 0 5px;}

/*横向滑动*/
.image-ads .preview-draggable{overflow: hidden;white-space: nowrap;}
.image-ads .preview-draggable>ul.horizontal-sliding{overflow-x: scroll;white-space: nowrap;}
.image-ads .preview-draggable>ul.horizontal-sliding::-webkit-scrollbar{display: none;}
.image-ads .preview-draggable .image-ads-item.horizontal-sliding{display: inline-block;width: 100px;vertical-align: middle;}
.image-ads .preview-draggable .image-ads-item.horizontal-sliding p{height: 100px;line-height: 100px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;}

/*轮播海报*/
.image-ads .preview-draggable .image-ads-item.carousel-posters{}
.image-ads .preview-draggable .image-ads-item.carousel-posters p{height: 150px;line-height: 150px;}
.image-ads .preview-draggable .image-ads-item.carousel-posters>div{height: 150px;line-height: 150px;}
.image-ads .preview-draggable .image-ads-item.carousel-posters>div.fill img{width:100%;}

.draggable-element .image-ads .preview-draggable{padding: 0}
.image-ads .image-ad-list{margin-left:10px;}
.image-ads .image-ad-list>ul>li{padding:10px;background: #ffffff;border:1px dashed #e5e5e5;position:relative;margin-top:10px;}
.image-ads .image-ad-list>ul>li:first-child{margin-top:0;}
.image-ads .image-ad-list>ul>li .content-block{display:inline-block;width:71%;}
.image-ads .image-ad-list>ul>li .content-block.textNavigation{width:100%;}
.image-ads .image-ad-list>ul>li .content-block .layui-form-item{margin:0;}
.image-ads .image-ad-list>ul>li .content-block .layui-form-label{width:60px;padding:9px 0;line-height: 1;}
.image-ads .image-ad-list>ul>li .content-block .layui-input-block{margin-left:80px;}
.image-ads .image-ad-list>ul>li .content-block div{margin-top:10px;}
.image-ads .image-ad-list>ul>li .content-block div:last-child{margin-top:0;}
.image-ads .image-ad-list>ul>li:hover .del{display:block;}

/*图片间隙*/
.image-ads .preview-draggable>ul.vertically li{margin-left:0 !important;margin-right:0 !important;margin-top:0 !important;}
.image-ads .preview-draggable>ul.vertically li:last-child{margin:0 !important;}
.image-ads .preview-draggable>ul[class*='horizontal-sliding'] li{margin-top:0 !important;margin-bottom:0 !important;margin-left:0 !important;}
.image-ads .preview-draggable>ul[class*='horizontal-sliding'] li:last-child{margin:0 !important;}

.image-ads .error-msg {margin-top: 5px;color: #f44;display: none;}