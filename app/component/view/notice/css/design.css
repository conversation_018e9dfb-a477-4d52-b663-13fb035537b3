@CHARSET "UTF-8";
/*公告组件*/
.notice-view{height:50px;overflow: hidden;}
.notice-view .tips{width:50px;margin-right: 10px;overflow: initial;text-align: center;border: 1px solid;padding: 1px 4px;border-radius: 10px;float: left;}
.notice-view .scroll-list{height: 25px;margin: 0;width: 70%;float: left;position: relative;}
.notice-view .scroll-list .item{height: 25px;line-height: 25px;width: 100%;overflow: hidden;}

.notice .notice-config>ul>li{padding:10px;background: #ffffff;border:1px dashed #e5e5e5;position:relative;margin-top:10px;}
.notice .notice-config>ul>li:first-child{margin-top:0;}
.notice .notice-config>ul>li .content-block.textNavigation{width:100%;}
.notice .notice-config>ul>li .content-block .layui-form-item{margin:0;}
.notice .notice-config>ul>li .content-block .layui-form-label{width:60px;padding:9px 0;line-height: 1;}
.notice .notice-config>ul>li .content-block .layui-input-block{margin-left:80px;}
.notice .notice-config>ul>li .content-block div{margin-top:10px;}
.notice .notice-config>ul>li .content-block div:last-child{margin-top:0;}
.notice .notice-config>ul>li:hover .del{display:block;}

.notice .add-item{padding: 10px;border: 1px dashed #e5e5e5;margin: 10px 0;cursor: pointer;}
.notice .add-item i{display: inline-block;height: 24px; line-height: 24px; font-size: 18px;font-style: normal;margin-right: 10px;}
.notice .add-item span{display: inline-block;height: 24px; line-height: 24px;}
.notice p.hint{font-size: 12px;color: #999;margin: 10px;}

.notice .error-msg {margin-top: 5px;color: #f44;display: none;}

/* 风格弹框 */
.notice .style-list-box {
	display: none;
}
.style-list-con {
	display: flex;
	flex-wrap: wrap;
}
.style-list-con .style-li {
	margin-right: 13px;
	margin-bottom: 15px;
	cursor: pointer;
	border: 1px solid #ededed;
	background: #f7f8fa;
}

.style-list-con .style-li:nth-child(3n) {
	margin-right: 0;
}

.layui-layer-page .layui-layer-content {
	overflow: auto !important;
}
.btn-box {
	margin-top: 30px;
	text-align: center;
}

.notice .text-title-img {
	text-align: center;
}


/* 公告列表 */
.notice-list {
	display: none;
}

.notice-box {
	border-radius: 4px;
	padding: 0 10px;
	line-height: 20px;
}

.laytable-cell-2-0-0 {
	width: 350px;
}

.laytable-cell-2-0-1 {
	width: 250px;
}

.laytable-cell-2-0-2 {
	width: 150px;
}

/* 图一 */
.notice-box-1 {
	padding: 0;
}

.notice .notice-con {
	display: flex;
	align-items: center;
	position: relative;
	padding-left: 58px;
}

.notice .notice-con-icon {
	flex-shrink: 0;
	height: 10px;
	text-align: center;
	line-height: 18px;
	margin-right: 5px;
	position: absolute;
	left: 0;
}

.notice .notice-con-icon img {
	max-width: 100%;
	max-height: 100%;
	vertical-align: top;
}

.notice .notice-con-font {
	font-size: 12px;
	color: #666666;
	flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/* 图二 */
.notice-box-2 {
	background: #f2f2f2;
}

.notice .notice-box-2 .notice-con-icon {
	flex-shrink: 0;
	width: 18px;
	height: 18px;
	text-align: center;
	line-height: 18px;
	margin-right: 5px;
}

/* 图三 */
.notice-box-3 {
	-webkit-box-shadow: 4px 1px 20px #f2f2f2;
	box-shadow: 4px 1px 20px #f2f2f2;
	background: #fff;
}

.notice-box-3 .notice-con-icon {
	background: #ffe1e1;
	color: #ff4f4f;
	border-radius: 9px;
	display: inline-block;
	width: 32px;
	height: 18px;
	line-height: 18px;
	text-align: center;
	margin-right: 6px;
	font-size: 12px;
}