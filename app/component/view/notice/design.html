<nc-component v-bind:data="data[index]" v-bind:style="{ backgroundColor : nc.backgroundColor }" class="notice">

	<!-- 预览 -->
	<template slot="preview">
		<div class="preview-box" v-bind:style="{ paddingLeft : nc.paddingLeftRight + 'px', paddingRight : nc.paddingLeftRight + 'px', paddingTop : nc.paddingTopBottom + 'px', paddingBottom : nc.paddingTopBottom + 'px' }">

			<!-- 图一 -->
			<div class="notice-box" v-bind:class="'notice-box-'+ nc.style" v-if="nc.style == 1">
				<div class="notice-con" v-for="(item, index) in nc.list" v-if="index < 2">
					<div class="notice-con-icon" v-if="index == 0"><img src="{$resource_path}/notice/img/ns-notice.png" /></div>
					<span class="notice-con-font" v-bind:style="{color: nc.textColor}">{{ item.title }}</span>
				</div>
			</div>

			<!-- 图二 -->
			<div class="notice-box" v-bind:class="'notice-box-'+ nc.style" v-if="nc.style == 2">
				<div class="notice-con" v-show="nc.title">
					<div class="notice-con-icon"><img src="{$resource_path}/notice/img/laba.png" /></div>
					<span class="notice-con-font" v-bind:style="">{{ nc.title }}</span>
				</div>
			</div>
			
			<!-- 图三 -->
			<div class="notice-box" v-bind:class="'notice-box-'+ nc.style" v-if="nc.style == 3">
				<div class="notice-con" v-show="nc.title">
					<div class="notice-con-icon">公告</div>
					<span class="notice-con-font" v-bind:style="">{{ nc.title }}</span>
				</div>
			</div>
		</div>
	</template>

	<!-- 编辑 -->
	<template slot="edit">
		<!-- 所选风格字体颜色不可编辑 -->
		<template v-if="nc.lazyLoad">
			<notice-con></notice-con>
		</template>

		<!-- 所选风格字体大小、颜色可编辑 -->
		<font-size v-bind:data="{ value : nc.fontSize }" v-show="nc.isEdit == 2"></font-size>
		<color v-show="nc.isEdit == 1" v-bind:data="{ defaultcolor: 'defaultTextColor' }"></color>

		<color v-bind:data="{ field : 'backgroundColor', label : '背景颜色' }"></color>

		<slide v-bind:data="{ field : 'paddingTopBottom', label : '上下边距' }"></slide>
		<slide v-bind:data="{ field : 'paddingLeftRight', label : '左右边距' }"></slide>
		

		<template v-if="nc.lazyLoad">
			<notice-edit></notice-edit>
		</template>

		<!-- 弹框 -->
		<div class="style-list-box">
			<div class="style-list layui-form">
				<div class="style-list-con">
					<div class="style-li" v-bind:class="{'selected ns-border-color': nc.style == 1}">
						<input type="hidden" v-bind:class="'style-li-'+ nc.style" value="1" />
						<img src="{$resource_path}/notice/img/style1.png" />
					</div>
					
					<!-- <div class="style-li" v-bind:class="{'selected ns-border-color': nc.style == 2}">
						<input type="hidden" v-bind:class="'style-li-'+ nc.style" value="0" />
						<img src="{$resource_path}/notice/img/style2.png" />
					</div>

					<div class="style-li" v-bind:class="{'selected ns-border-color': nc.style == 3}">
						<input type="hidden" v-bind:class="'style-li-'+ nc.style" value="0" />
						<img src="{$resource_path}/notice/img/style3.png" />
					</div> -->
				</div>

				<input type="hidden" name="style">
				<input type="hidden" name="isEdit">
			</div>
		</div>

		<!-- 网站公告弹框 -->
		<div class="notice-list">
			<table class="layui-table" id="notice_list"></table>
			<div class="layui-form layui-border-box layui-table-view">
				<div class="layui-table-box">
					<div class="layui-table-header">
						<table cellspacing="0" cellpadding="0" border="0" class="layui-table" lay-skin="line" lay-size="lg">
							<thead>
								<tr>
									<th data-field="0" data-key="2-0-0" data-unresize="true" class=" layui-table-col-special">
										<div class="layui-table-cell laytable-cell-2-0-0">
											<span>公告标题</span>
										</div>
									</th>
									<th data-field="1" data-key="2-0-1" data-unresize="true" class=" layui-table-col-special">
										<div class="layui-table-cell laytable-cell-2-0-1">
											<span>创建时间</span>
										</div>
									</th>
									<th data-field="2" data-key="2-0-2" data-unresize="true" class=" layui-table-col-special">
										<div class="layui-table-cell laytable-cell-2-0-2">
											<span>操作</span>
										</div>
									</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="layui-table-body layui-table-main">
						<table cellspacing="0" cellpadding="0" border="0" class="layui-table" lay-skin="line" lay-size="lg">
							<tbody>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>

	</template>

	<!-- 资源 -->
	<template slot="resource">

		<js>
			var RESOURCEPATH = "{$resource_path}";
		</js>
		<css src="{$resource_path}/notice/css/design.css"></css>
		<js src="{$resource_path}/notice/js/design.js"></js>

	</template>

</nc-component>
