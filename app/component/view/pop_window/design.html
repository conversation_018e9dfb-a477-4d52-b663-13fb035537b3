<nc-component v-bind:data="data[index]" class="pop-window">

	<!-- 预览 -->
	<template slot="preview">

		<div class="pop-window-box">

			<img v-bind:src="nc.imageUrl? changeImgUrl(nc.imageUrl) : 'STATIC_EXT/diyview/img/crack_figure.png'" class="pop-window-image"/>
		
		</div>
		
	</template>
	
	<!-- 编辑 -->
	<template slot="edit">
		
		<template v-if="nc.lazyLoad">
			<pop-window></pop-window>
		</template>
	</template>
	
	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/pop_window/css/design.css"></css>
		<js src="{$resource_path}/pop_window/js/design.js"></js>
		
	</template>
	
</nc-component>