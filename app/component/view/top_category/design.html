<nc-component v-bind:data="data[index]" class="top-category">

	<!-- 预览 -->
	<template slot="preview">
		<div class="topCategorty" v-bind:style="{ backgroundColor : nc.backgroundColor}">
			<div class="top-categorty-item active" v-bind:style="{ color : nc.selectColor}">
				{{nc.title}}
			</div>
			<div class="top-categorty-item" v-bind:style="{ color : nc.nsSelectColor}">
				珠宝饰品
			</div>
			<div class="top-categorty-item" v-bind:style="{ color : nc.nsSelectColor}">
				美妆护肤
			</div>
			<div class="top-categorty-item" v-bind:style="{ color : nc.nsSelectColor}">
				手机数码
			</div>
		</div>
	</template>

	<!-- 编辑 -->
	<template slot="edit">
		
		<template v-if="nc.lazyLoad">
			<!-- <top-category></top-category> -->
			<color v-bind:data="{ field : 'backgroundColor', 'label' : '背景颜色' }"></color>
			<color v-bind:data="{ field : 'selectColor', 'label' : '选中颜色' }"></color>
			<color v-bind:data="{ field : 'nsSelectColor', 'label' : '未选中颜色' }"></color>
			<div class="layui-form-item">
				<label class="layui-form-label sm">主页名称</label>
				<div class="layui-input-block">
					<input type="text" v-model="nc.title" v-bind:id="'title_'+index" placeholder="请输入文本" class="layui-input">
				</div>
			</div>
		</template>
		
	</template>

	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/top_category/css/design.css"></css>
		<js src="{$resource_path}/top_category/js/design.js"></js>

	</template>

</nc-component>
