<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\event;

use app\model\message\Message;

/**
 * 登录成功发送通知
 */
class MemberLogin
{

    public function handle($param)
    {
        // 发送通知
        $message_model = new Message();
        $message_model->sendMessage(["keywords" => "LOGIN", "member_id" => $param["member_id"], "site_id" => $param["site_id"]]);
    }

}