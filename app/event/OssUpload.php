<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\event;

use app\model\upload\Upload as UploadModel;
use OSS\OssClient;
use OSS\Core\OssException;

/**
 * 阿里云OSS上传事件
 */
class OssUpload
{
    /**
     * 处理Put事件
     * @param array $params
     * @return array
     */
    public function handle($params)
    {
        try {
            // 获取阿里云OSS配置
            $accessKeyId = config('filesystem.disks.aliyun.access_key_id');
            $accessKeySecret = config('filesystem.disks.aliyun.access_key_secret');
            $endpoint = config('filesystem.disks.aliyun.endpoint');
            $bucket = config('filesystem.disks.aliyun.bucket');
            
            // 初始化OSS客户端
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            
            // 上传文件到OSS
            $object = ltrim($params['file_path'], '/');
            $filePath = $params['file_path'];
            
            $result = $ossClient->uploadFile($bucket, $object, $filePath);
            return [
                'code' => 1,
                'message' => '上传成功',
                'data' => [
                    'path' => $result['info']['url'],
                    'url' => $result['info']['url'],
                    'object' => $object
                ]
            ];
        } catch (OssException $e) {
            return [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'data' => []
            ];
        }
    }
}