<?php

return [
	'SUCCESS' => 'success',
    'ERROR' => 'error',
	'FAIL' => 'fail',
	'SAVE_SUCCESS' => 'save success',
	'SAVE_FAIL' => 'save fail',
	'REQUEST_SUCCESS' => 'request success',
	'REQUEST_FAIL' => 'request error',
	'DELETE_SUCCESS' => 'delete success',
	'DELETE_FAIL' => 'delete fail',
	'UNKNOW_ERROR' => 'unknow error',
	'PARAMETER_ERROR' => 'parameter error',
    'REQUEST_SITE_ID' => 'request site id',
    'REQUEST_APP_MODULE' => 'request app module',
    'TEMPLATE_TO_LONG' => 'template to long',
    'REQUEST_GOODS_ATTRIBUTE_ID' => 'request goods attribute id',
    'MEMBER_NOT_EXIST' => 'member not exist',
    'MEMBER_IS_LOCKED' => 'member is locked',
    'USERNAME_EXISTED' => 'username existed',
    'MOBILE_EXISTED' => 'mobile existed',
    'EMAIL_EXISTED' => 'email existed',
    'REGISTER_REFUND' => 'register refund',
    'REQUEST_KEYWORDS' => 'request keywords',
    "COUPON_ERROR" => 'coupon error',
    "ORDER_DELIVERY_CODE_ERROR" => 'order delivery code error',
    "MEMBER_SHOP_BIND_EXISTED" => 'shop bind exist',
    "APPLY_EXISTED" => 'apply existed',
    "SHOP_EXISTED" => 'shop existed',
    //基础系统
    'REQUEST_CONFIG_KEY' =>'request config key',
    'REQUEST_DOCUMENT_KEY' =>'request document key',
    'CONFIG_NOT_EXIST' =>'config is not exist',
    //插件安装与卸载
    'ADDON_NOT_EXIST' => 'addon is not exist',
    'ADDON_IS_EXIST' => 'addon is exist',
    'ADDON_INFO_ERROR' => 'addon info error',
    'ADDON_INSTALL_MENU_FAIL_EXISTED' => 'menu install fail, menu is exist',
    'ADDON_INSTALL_MENU_FAIL' => 'menu install fail',
    'ADDON_INSTALL_FAIL' => 'addon install fail',
    'ADDON_ADD_FAIL' => 'addon add fail',
    'ADDON_UNINSTALL_FAIL' => 'addon uninstall fail',
    'ADDON_UNINSTALL_MENU_FAIL' => 'addon menu uninstall fail',
    //数据库
    'DABASE_REPAIR_FAIL' => 'database repair fail',
    'DATABASE_OPTIMIZE_FAIL' => 'databasr optimize fail',
    'REQUEST_DATABASE_TABLE' =>'request database table',
    //用户
    'USER_EXISTED' => 'user is exist',
    'USER_NOT_EXIST' => 'user is not exist',
    'USER_IS_LOCKED' => 'user is locked',
    'PASSWORD_ERROR' =>'password error',
    'PERMISSION_DENIED' => 'permission denied',
    'USER_GROUP_NOT_ALL_DELETE' => 'user group is not all deleted',
    'USER_GROUP_USED' => 'user group is used',
    'CAPTCHA_FAILURE' => 'captcha fail',
    'CAPTCHA_ERROR' => 'captcha error',
	'RESULT_ERROR' => 'result error'
];