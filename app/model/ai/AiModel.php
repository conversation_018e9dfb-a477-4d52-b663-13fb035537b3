<?php
namespace app\model\ai;
use app\model\BaseModel;

class AiModel extends BaseModel
{
    /**
     * API密钥
     * @var string
     */
    public $apiKey;

    /**
     * API接口地址
     * @var string
     */
    protected $apiUrl;
    
    /**
     * 默认模型名称
     * @var string
     */
    protected $defaultModel;

    /**
     * 当前使用的模型名称
     * @var string
     */
    protected $model;
    
    /**
     * 构造函数
     * @param string $apiKey API密钥
     * @param string $model 模型名称，可选
     * @param string $apiUrl API接口地址，可选，用于支持不同的API提供商
     */
    public function __construct($apiKey, $model = null, $apiUrl = null)
    {
        if (empty($apiKey)) {
            throw new \Exception("API密钥不能为空");
        }
        $this->apiKey = $apiKey;
        parent::__construct();
        $this->apiUrl = config('ai_config.openai.api_url');
        $this->defaultModel = config('ai_config.openai.default_model');
        $this->model = $model ?: $this->defaultModel;
        
        // 如果提供了自定义API地址，则使用自定义地址
        if (!empty($apiUrl)) {
            $this->apiUrl = $apiUrl;
        }
    }
    
    /**
     * 准备请求参数
     * @param array $messages 消息数组
     * @param array $options 附加选项
     * @return array
     */
    function prepareRequestParams($messages, $options = [])
    {
        if (empty($this->model)) {
            throw new \Exception("模型名称不能为空");
        }
        // 合并默认参数和用户自定义参数
        $params = array_merge([
            'model' => $this->model,
            'messages' => $this->formatMessages($messages),
            'temperature' => 0.7,
            'top_p' => 0.9,
            'max_tokens' => 1500,
            'stream' => false, // 默认非流式
        ], $options);
        
        return $params;
    }
    
    /**
     * 格式化消息为OpenAI兼容格式
     * @param array $messages
     * @return array
     */
    function formatMessages($messages)
    {
        // 如果已经是数组形式的消息列表，直接返回
        if (isset($messages[0]) && is_array($messages[0])) {
            return array_map(function($msg) {
                // 支持多模态输入（文本和图片URL）
                if (isset($msg['content']) && is_array($msg['content'])) {
                    $result = [
                        'role' => $msg['role'] ?? 'user',
                        'content' => $msg['content']
                    ];
                    if (isset($msg['file_id'])) {
                        $result['file_id'] = $msg['file_id'];
                    }
                    return $result;
                }
                
                // 传统文本消息格式
                $result = [
                    'role' => $msg['role'] ?? 'user',
                    'content' => is_array($msg['content']) ? $msg['content'] : $msg['content'] ?? ''
                ];
                if (isset($msg['file_id'])) {
                    $result['file_id'] = $msg['file_id'];
                }
                return $result;
            }, $messages);
        }
        
        // 如果是单个消息对象，转换为数组
        if (isset($messages['role'])) {
            return [[
                'role' => $messages['role'],
                'content' => $messages['content']
            ]];
        }
        
        // 默认情况，创建一个用户消息
        return [[
            'role' => 'user',
            'content' => is_string($messages) ? $messages : ''
        ]];
    }
    
    /**
     * 发送请求到API
     * @param array $params 请求参数或消息内容
     * @param array $files 文件数据，可选
     * @param array $options 附加选项
     * @return array 响应结果
     * @throws \Exception 当API请求失败时抛出异常
     */
    public function sendRequest($params, $files = [], $options = [])
    {
        if (empty($this->apiKey)) {
            throw new \Exception("API密钥无效或未设置");
        }
        
        // 标准化请求参数
        $requestParams = $this->prepareRequestParams($params, $options);

        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $ch = curl_init();
        
        try {
            // 统一使用JSON格式传输
            $jsonData = json_encode($requestParams, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("JSON编码错误: " . json_last_error_msg());
            }
            
            $curlOptions = [
                CURLOPT_URL => $this->apiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_TIMEOUT => config('ai_config.openai.timeout'),
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POSTFIELDS => $jsonData,
                CURLOPT_FAILONERROR => true // 更好的错误处理
            ];
            
            curl_setopt_array($ch, $curlOptions);
            
            $response = curl_exec($ch);
            
            if ($response === false) {
                $errorMsg = curl_error($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                throw new \Exception("API请求失败: [{$httpCode}] {$errorMsg}");
            }
            
            return $this->parseResponse($response, curl_getinfo($ch, CURLINFO_HTTP_CODE));
            
        } finally {
            curl_close($ch);
        }
    }
    
    /**
     * 解析API响应
     * @param string $response 响应内容
     * @param int $httpCode HTTP状态码
     * @return array 解析后的响应
     */
    protected function parseResponse($response, $httpCode)
    {
        $data = json_decode($response, true);
        
        if ($httpCode !== 200) {
            $errorMsg = isset($data['error']['message']) 
                ? $data['error']['message'] 
                : "API request failed with code $httpCode";
            throw new \Exception($errorMsg);
        }
        
        // 标准化响应格式，确保与OpenAI格式兼容
        return [
            'id' => $data['id'] ?? '',
            'object' => $data['object'] ?? 'chat.completion',
            'created' => $data['created'] ?? time(),
            'model' => $data['model'] ?? $this->model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $data['choices'][0]['message']['content'] ?? ''
                    ],
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? 'stop'
                ]
            ],
            'usage' => $data['usage'] ?? [
                'prompt_tokens' => 0,
                'completion_tokens' => 0,
                'total_tokens' => 0
            ]
        ];
    }
    
    /**
     * 处理流式响应
     * @param array $messages 消息数组
     * @param callable $callback 回调函数
     * @param array $options 附加选项
     * @return void
     */
    public function streamChat($messages, $callback, $options = [])
    {
        $options['stream'] = true;
        $params = $this->prepareRequestParams($messages, $options);
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];
        
        $ch = curl_init($this->apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        $jsonData = json_encode($params, JSON_UNESCAPED_UNICODE);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("JSON编码错误: " . json_last_error_msg());
        }
        if (!curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData)) {
            throw new \Exception("设置POST数据失败");
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) use ($callback) {
            // 处理SSE格式数据
            $lines = explode("\n", $data);
            foreach ($lines as $line) {
                if (strpos($line, 'data: ') === 0) {
                    $jsonData = substr($line, 6); // 移除 "data: " 前缀
                    if ($jsonData === '[DONE]') {
                        $callback(null, true); // 流结束
                    } else {
                        $chunk = json_decode($jsonData, true);
                        if (isset($chunk['choices'][0]['delta']['content'])) {
                            $content = $chunk['choices'][0]['delta']['content'];
                            $callback($content, false);
                        }
                    }
                }
            }
            return strlen($data);
        });
        
        curl_exec($ch);
        curl_close($ch);
    }
    
    /**
     * 上传图片并发送请求
     * @param string $imagePath 图片路径
     * @param string $prompt 提示词
     * @param array $options 附加选项
     * @return array 响应结果
     */
    public function sendImageRequest($imagePath, $prompt, $options = [])
    {
        // 检查文件是否存在
        if (!file_exists($imagePath)) {
            throw new \Exception("图片文件不存在: $imagePath");
        }
        
        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $imagePath);
        finfo_close($finfo);
        
        // 检查是否为图片类型
        if (!in_array($mimeType, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
            throw new \Exception("不支持的图片类型: $mimeType");
        }
        
        // 准备消息内容，包含文本和图片
        $messages = [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => "data:$mimeType;base64," . base64_encode(file_get_contents($imagePath))
                        ]
                    ]
                ]
            ]
        ];
        
        // 发送请求
        return $this->sendRequest(['messages' => $messages], [], $options);
    }
    
    /**
     * 上传PDF文件并发送请求
     * @param string $filePath PDF文件路径
     * @param string $prompt 提示词
     * @param array $options 附加选项
     * @return array 响应结果
     */
    public function sendPdfRequest($filePath, $prompt, $options = [])
    {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            throw new \Exception("PDF文件不存在: $filePath");
        }
        
        // 准备消息内容
        $messages = [
            ['role' => 'system', 'content' => $prompt]
        ];
        
        // 准备请求参数
        $params = $this->prepareRequestParams($messages, $options);
        
        // 准备文件数据
        $fileData = [
            'file' => new \CURLFile($filePath, 'application/pdf', basename($filePath))
        ];
        
        // 发送请求
        return $this->sendRequest($params, $fileData);
    }
    
    /**
     * 获取支持的模型列表
     * @return array
     */
    public function getAvailableModels()
    {
        return [
            // OpenAI模型
            'gpt-3.5-turbo',
            'gpt-4',
            'gpt-4-vision-preview',
            'gpt-4-turbo',
            // 豆包模型
            'doubao-lite',
            'doubao-pro',
            'doubao-plus',
            'doubao-1.5-vision-lite-250315',
        ];
    }
}