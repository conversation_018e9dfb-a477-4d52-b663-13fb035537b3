<?php
namespace app\model\ai;

use think\Model;

class AiRequestLog extends Model
{
    protected $name = 'ai_request_logs';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'request_time';
    protected $updateTime = 'response_time';
    
    /**
     * 创建请求日志
     * @param array $data 日志数据
     * @return int|false
     */
    public function createLog($data)
    {
        return $this->insertGetId($data);
    }
    
    /**
     * 更新响应结果
     * @param int $id 日志ID
     * @param array $response 响应数据
     * @param string $error 错误信息
     * @return bool
     */
    public function updateResponse($id, $response, $error = null)
    {
        $updateData = [
            'response_data' => json_encode($response),
            'status' => $error ? 3 : 2, // 2:成功,3:失败
            'error_message' => $error
        ];
        
        return $this->where('id', $id)->update($updateData);
    }
}