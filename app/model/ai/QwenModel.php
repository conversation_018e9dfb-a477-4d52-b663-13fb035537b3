<?php
namespace app\model\ai;

class QwenModel extends AiModel
{
    /**
     * 文件保留天数
     * @var int
     */
    protected $fileRetentionDays;

    public function __construct($apiKey, $model, $apiUrl)
    {
        parent::__construct($apiKey, $model, $apiUrl);
        $this->fileRetentionDays = config('ai_config.qianwen.file_retention_days');
        $this->filesApiUrl = config('ai_config.qianwen.files_api_url');
    }

    /**
     * 文件API基础URL
     * @var string
     */
    protected $filesApiUrl;

    /**
     * 上传文件并获取文件ID
     * @param string $filePath 文件路径
     * @param string $purpose 文件用途，默认为'file-extract'
     * @return array 包含文件ID的响应结果
     */
    public function uploadFile($filePath, $purpose = 'file-extract')
    {
        // 上传前先清理超过30天的旧文件
        $this->cleanupOldFiles();
        
        if (!file_exists($filePath)) {
            throw new \Exception("文件不存在: $filePath");
        }

        $headers = [
            'Authorization: Bearer ' . $this->apiKey
        ];

        $postData = [
            'purpose' => $purpose,
            'file' => new \CURLFile($filePath)
        ];

        $ch = curl_init($this->filesApiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_TIMEOUT => config('ai_config.qianwen.timeout'),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($error) {
            throw new \Exception("cURL Error: $error");
        }

        return $this->parseFileResponse($response, $httpCode);
    }

    /**
     * 查询文件信息
     * @param string $fileId 文件ID
     * @return array 文件信息
     */
    public function retrieveFile($fileId)
    {
        $url = $this->filesApiUrl . '/' . $fileId;
        $headers = [
            'Authorization: Bearer ' . $this->apiKey
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => config('ai_config.qianwen.timeout'),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($error) {
            throw new \Exception("cURL Error: $error");
        }

        return $this->parseFileResponse($response, $httpCode);
    }

    /**
     * 查询文件列表
     * @param string $after 分页游标，可选
     * @param int $limit 每页数量，默认为20
     * @return array 文件列表
     */
    public function listFiles($after = null, $limit = 20)
    {
        $query = ['limit' => $limit];
        if ($after) {
            $query['after'] = $after;
        }

        $url = $this->filesApiUrl . '?' . http_build_query($query);
        $headers = [
            'Authorization: Bearer ' . $this->apiKey
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => config('ai_config.qianwen.timeout'),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($error) {
            throw new \Exception("cURL Error: $error");
        }

        return $this->parseFileResponse($response, $httpCode);
    }

    /**
     * 删除文件
     * @param string $fileId 文件ID
     * @return array 删除结果
     */
    public function deleteFile($fileId)
    {
        $url = $this->filesApiUrl . '/' . $fileId;
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'DELETE',
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => config('ai_config.qianwen.timeout'),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($error) {
            throw new \Exception("cURL Error: $error");
        }

        return $this->parseFileResponse($response, $httpCode);
    }

    /**
     * 解析文件API响应
     * @param string $response 响应内容
     * @param int $httpCode HTTP状态码
     * @return array 解析后的响应
     */
    protected function parseFileResponse($response, $httpCode)
    {
        $data = json_decode($response, true);

        if ($httpCode !== 200) {
            $errorMsg = isset($data['error']['message']) 
                ? $data['error']['message'] 
                : "文件API请求失败，状态码: $httpCode";
            throw new \Exception($errorMsg);
        }

        return $data;
    }
    
    /**
     * 发送符合千问OpenAI文件接口规范的请求
     * @param array $messages 消息数组
     * @param float $temperature 温度参数
     * @param float $top_p top_p参数
     * @param int $max_tokens 最大token数
     * @param bool $stream 是否流式返回
     * @return array API响应结果
     */
    public function sendRequest($messages, $temperature = null, $top_p = null, $max_tokens = null, $stream = false)
    {
        // 如果消息中包含file_id，则将其作为系统消息
        if (isset($messages[0]['file_id'])) {
            $fileId = $messages[0]['file_id'];
            $messages = [
                ['role' => 'system', 'content' => 'fileid://' . $fileId],
                ['role' => 'user', 'content' => $messages[0]['content']]
            ];
        }

        // 如果是流式请求，使用streamRequest方法处理
        if ($stream) {
            return $this->streamRequest($messages, $temperature ?? config('ai_config.qianwen.temperature'), $top_p ?? config('ai_config.qianwen.top_p'), $max_tokens);
        }

        $requestParams = [
            'model' => $this->model,
            'messages' => $messages,
            'temperature' => $temperature ?? config('ai_config.qianwen.temperature'),
            'top_p' => $top_p ?? config('ai_config.qianwen.top_p'),
            'max_tokens' => $max_tokens ?? config('ai_config.qianwen.max_tokens'),
            'stream' => false
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $ch = curl_init($this->apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($requestParams, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            CURLOPT_TIMEOUT => config('ai_config.qianwen.timeout'),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("cURL Error: $error");
        }
        
        return $this->parseFileResponse($response, $httpCode);
    }

    /**
     * 发送流式请求并处理响应
     * @param array $messages 消息数组
     * @param float $temperature 温度参数
     * @param float $top_p top_p参数
     * @param int $max_tokens 最大token数
     * @return \think\response\Json 流式响应
     */
    public function streamRequest($messages, $temperature = null, $top_p = null, $max_tokens = 1500)
    {
        $requestParams = [
            'model' => $this->model,
            'messages' => $messages,
            'temperature' => $temperature ?? config('ai_config.qianwen.temperature'),
            'top_p' => $top_p ?? config('ai_config.qianwen.top_p'),
            'max_tokens' => $max_tokens ?? config('ai_config.qianwen.max_tokens'),
            'stream' => true
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: text/event-stream'
        ];
        
        // 设置响应头，支持流式输出
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // 禁用Nginx缓冲
        
        // 关闭输出缓冲
        if (ob_get_level()) ob_end_clean();
        
        // 用于收集完整响应内容
        $fullContent = '';
        
        // 初始化cURL
        $ch = curl_init($this->apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($requestParams, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            CURLOPT_TIMEOUT => 0, // 无超时限制
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_WRITEFUNCTION => function($curl, $data) use (&$fullContent) {
                // 处理数据流
                if (strpos($data, 'data: ') === 0) {
                    $jsonData = substr($data, 6); // 移除 'data: ' 前缀
                    if ($jsonData !== '[DONE]') {
                        try {
                            $decoded = json_decode($jsonData, true);
                            if (isset($decoded['choices'][0]['delta']['content'])) {
                                $content = $decoded['choices'][0]['delta']['content'];
                                $fullContent .= $content;
                                // 转义特殊字符并输出完整JSON格式
                                $escapedContent = json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                                $reportId = isset($GLOBALS['reportId']) ? $GLOBALS['reportId'] : 0;
                                echo "data: {\"content\":$escapedContent, \"reportId\":$reportId}\n\n";
                            }
                        } catch (\Exception $e) {
                            // 忽略解析错误
                        }
                    } else {
                        echo "data: [DONE]\n\n";
                    }
                }
                flush();
                return strlen($data);
            }
        ]);
        
        curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "data: {\"error\":{\"message\":\"cURL Error: $error\",\"type\":\"api_error\",\"code\":\"stream_error\"}}\n\n";
            flush();
            return;
        }
        
        // 构建完整响应结果
        $result = [
            'choices' => [
                [
                    'message' => [
                        'content' => $fullContent,
                        'role' => 'assistant'
                    ]
                ]
            ]
        ];
        
        // 更新日志和报告状态
        if (isset($GLOBALS['logId'])) {
            $logModel = new \app\model\ai\AiRequestLog();
            $logModel->updateResponse($GLOBALS['logId'], $result, null);
        }
        
        if (isset($GLOBALS['reportId'])) {
            $reportModel = new \app\model\ai\UserReport();
            $reportModel->updateAnalysisResult($GLOBALS['reportId'], json_encode($result), $fullContent);
        }
        
        exit; // 终止后续执行
    }
    
    /**
     * 清理超过30天的旧文件
     * @return int 被删除的文件数量
     */
    public function cleanupOldFiles()
    {
        $files = $this->listFiles();
        $deletedCount = 0;
        $thirtyDaysAgo = time() - ($this->fileRetentionDays * 24 * 60 * 60);

        if (!empty($files['data'])) {
            foreach ($files['data'] as $file) {
                if ($file['created_at'] < $thirtyDaysAgo) {
                    try {
                        $this->deleteFile($file['id']);
                        $deletedCount++;
                    } catch (\Exception $e) {
                        // 记录错误但继续处理其他文件
                        error_log("删除文件失败: " . $e->getMessage());
                    }
                }
            }
        }

        return $deletedCount;
    }

    /**
     * 删除所有文件（测试用）
     * @return int 被删除的文件数量
     */
    public function deleteAllFiles()
    {
        $files = $this->listFiles();
        $deletedCount = 0;

        if (!empty($files['data'])) {
            foreach ($files['data'] as $file) {
                try {
                    $this->deleteFile($file['id']);
                    $deletedCount++;
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他文件
                    error_log("删除文件失败: " . $e->getMessage());
                }
            }
        }

        return $deletedCount;
    }
    
    /**
     * 构建多模态消息格式
     * @param string $prompt 提示词
     * @param array $images 图片URL数组
     * @return array 格式化后的消息数组
     */
    public function buildMultiModalMessage($prompt, $images)
    {
        $content = [];
        
        // 添加文本内容
        if (!empty($prompt)) {
            $content[] = [
                'type' => 'text',
                'text' => $prompt
            ];
        }
        
        // 添加图片内容
        foreach ($images as $imageUrl) {
            // 移除URL中的反引号
            $cleanUrl = str_replace('`', '', $imageUrl);
            $content[] = [
                'type' => 'image_url',
                'image_url' => [
                    'url' => $cleanUrl,
                    // 可添加详细参数
                    'detail' => 'auto'
                ]
            ];
        }
        
        return [
            [
                'role' => 'user',
                'content' => $content
            ]
        ];
    }
}