<?php
namespace app\model\ai;

use think\Model;

class UserReport extends Model
{
    protected $name = 'user_reports';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'upload_time';
    protected $updateTime = 'analysis_time';
    
    /**
     * 创建报告记录
     * @param array $data 报告数据
     * @return int|false
     */
    public function createReport($data)
    {
        return $this->insertGetId($data);
    }
    
    /**
     * 更新分析结果
     * @param int $id 报告ID
     * @param string $result 分析结果
     * @param string $summary 系统总结
     * @return bool
     */
    public function updateAnalysisResult($id, $result, $summary)
    {
        return $this->where('id', $id)
            ->update([
                'analysis_result' => $result,
                'system_summary_result' => $summary,
                'analysis_status' => 2, // 分析完成
                'analysis_time' => date('Y-m-d H:i:s'),
            ]);
    }
    
    /**
     * 更新用户编辑结果
     * @param int $id 报告ID
     * @param string $editedResult 用户编辑结果
     * @return bool
     */
    public function updateUserEditedResult($id, $editedResult)
    {
        return $this->where('id', $id)
            ->update([
                'user_edited_result' => $editedResult
            ]);
    }
}