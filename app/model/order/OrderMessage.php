<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\order;

use app\model\member\Member;
use app\model\message\Sms;
use app\model\BaseModel;
use addon\wechat\model\Message as WechatMessage;

/**
 * 订单消息操作
 *
 * <AUTHOR>
 *
 */
class OrderMessage extends BaseModel
{
    /**
     * 订单生成提醒
     * @param $data
     */
    public function messageOrderCreate($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "*");

        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);
        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];
        //绑定微信公众号才发送
        $wechat_model          = new WechatMessage();
        if (!empty($member_info) && !empty($member_info["wx_openid"])) {
            $data["openid"]        = $member_info["wx_openid"];
            $data["template_data"] = [
                'keyword1' => $order_info['site_name'],
                'keyword2' => time_to_date($order_info['create_time']),
                'keyword3' => str_sub($order_info['order_name']),
                'keyword4' => $order_info['order_money'],
                'keyword5' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile']
            ];
          //$data["page"] = $this->handleUrl($order_info['order_type'], $order_id);
            $wechat_model->sendMessage($data);
        }

        //通知医生
        $user = model("user")->getInfo([["uid", "=", $member_info['user_id']]], "*");
        if ($user['member_id'] != $member_info['member_id']){
            $member_info_result = $member_model->getMemberInfo([["member_id", "=", $user['member_id']]]);
            $member_info        = $member_info_result["data"];
            if (!empty($member_info) && !empty($member_info["wx_openid"])) {
                $data["openid"]        = $member_info["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['site_name'],
                    'keyword2' => time_to_date($order_info['create_time']),
                    'keyword3' => str_sub($order_info['order_name']),
                    'keyword4' => $order_info['order_money'],
                    'keyword5' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile']
                ];
                $wechat_model->sendMessage($data);
            }
        }

    }

    /**
     * 消息发送——支付成功
     * @param $params
     * @return array|mixed|void
     */
    public function messagePaySuccess($params)
    {
        $order_id   = $params["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "*");

        // 发送短信
        /*$var_parse             = [
            "orderno"    => $params['order_no'],
            "username"   => replaceSpecialChar($params["name"]),
            "ordermoney" => $params["order_money"],
        ];
        $params["sms_account"] = $params["mobile"] ?? '';//手机号
        $params["var_parse"]   = $var_parse;
        $sms_model             = new Sms();
        $sms_result            = $sms_model->sendMessage($params);*/

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        $data = $params;
        //绑定微信公众号才发送
        $wechat_model          = new WechatMessage();
        if (!empty($member_info) && !empty($member_info["wx_openid"])) {
            $data["openid"]        = $member_info["wx_openid"];
            $data["template_data"] = [
                'keyword1' => $order_info['order_no'],
                'keyword2' => '支付成功',
                'keyword3' => time_to_date($order_info['pay_time']),
                'keyword4' => $order_info['site_name'],
                'keyword5' => $order_info['pay_money']
            ];
            //$data["page"]          = $this->handleUrl($params['order_type'], $params["order_id"]);
            $wechat_model->sendMessage($data);
        }

        //通知医生
        $user = model("user")->getInfo([["uid", "=", $member_info['user_id']]], "*");
        if ($user['member_id'] != $member_info['member_id']){
            $member_info_result = $member_model->getMemberInfo([["member_id", "=", $user['member_id']]]);
            $member_info        = $member_info_result["data"];
            if (!empty($member_info) && !empty($member_info["wx_openid"])) {
                $data["openid"]        = $member_info["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['order_no'],
                    'keyword2' => '支付成功',
                    'keyword3' => time_to_date($order_info['pay_time']),
                    'keyword4' => $order_info['site_name'],
                    'keyword5' => $order_info['pay_money']
                ];
                $wechat_model->sendMessage($data);
            }
        }

        //系统消息通知
        $condition = array(
            ['er.group_id', '=', 1],
        );

        $alias = 'er';
        $join  = [
            [
                'member ber',
                'er.member_id = ber.member_id',
                'left'
            ]
        ];
        $field = 'ber.wx_openid';

        $list = model('user')->getList($condition, $field, '', $alias, $join);
        foreach ($list as $key=>$vo){
            if ($vo["wx_openid"]){
                $data['message_info']['wechat_template_id'] = 'mMe20iqPxlTk_bF1DUB6MxyQyhKx9t7DadAzdin8XAQ';
                $data["openid"]        = $vo["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => time_to_date($order_info['pay_time']),
                    'keyword2' => $order_info['order_name'],
                    'keyword3' => $order_info['order_no']
                ];
                $wechat_json = json_decode($data['message_info']['wechat_json'], true);
                $wechat_json['headtext'] = '订单下单支付通知';
                $wechat_json['bottomtext'] = '订单已经下单支付，请及时备货发货，谢谢!'.'购买者信息：'.$order_info['name'].$order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile'];
                $data['message_info']['wechat_json'] = json_encode($wechat_json);
                $wechat_model->sendMessage($data);
            }
        }

    }

    /**
     * 订单关闭提醒
     * @param $data
     */
    public function messageOrderClose($data)
    {
        //发送短信
        $sms_model           = new Sms();
        $order_id            = $data["order_id"];
        $order_info          = model("order")->getInfo([["order_id", "=", $order_id]], "*");
        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];
        $wechat_model          = new WechatMessage();
        if (!empty($member_info) && !empty($member_info["wx_openid"])) {
            $data["openid"]        = $member_info["wx_openid"];
            $data["template_data"] = [
                'keyword1' => $order_info['order_money'],
                'keyword2' => $order_info['order_no'],
                'keyword3' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile'],
                'keyword4' => $order_info['order_id']
            ];
            //$data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
            $wechat_model->sendMessage($data);
        }

        //通知医生
        $user = model("user")->getInfo([["uid", "=", $member_info['user_id']]], "*");
        if ($user['member_id'] != $member_info['member_id']){
            $member_info_result = $member_model->getMemberInfo([["member_id", "=", $user['member_id']]]);
            $member_info        = $member_info_result["data"];
            if (!empty($member_info) && !empty($member_info["wx_openid"])) {
                $data["openid"]        = $member_info["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['order_money'],
                    'keyword2' => $order_info['order_no'],
                    'keyword3' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile'],
                    'keyword4' => $order_info['order_id']
                ];
                $wechat_model->sendMessage($data);
            }
        }

    }

    /**
     * 订单完成提醒
     * @param $data
     */
    public function messageOrderComplete($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "*");

        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);
        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        //发送模板消息
        $wechat_model          = new WechatMessage();
        $data["openid"]        = $member_info["wx_openid"];
        $data["template_data"] = [
            'keyword1' => $order_info['order_no'],
            'keyword2' => str_sub($order_info['order_name']),
            'keyword3' => time_to_date($order_info['create_time']),
            'keyword4' => time_to_date($order_info['delivery_time']),
            'keyword5' => time_to_date($order_info['sign_time']),
        ];
        //$data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
        $wechat_model->sendMessage($data);

        //系统消息通知
        $condition = array(
            ['er.group_id', '=', 1],
        );

        $alias = 'er';
        $join  = [
            [
                'member ber',
                'er.member_id = ber.member_id',
                'left'
            ]
        ];
        $field = 'ber.wx_openid';

        $list = model('user')->getList($condition, $field, '', $alias, $join);
        foreach ($list as $key=>$vo){
            if ($vo["wx_openid"]){
                $data['message_info']['wechat_template_id'] = 'uEU1hMFF1GAx6VihF6xXcubaqrNWcGPn4G6aP3K3rfw';
                $data["openid"]        = $vo["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['order_no'],
                    'keyword2' => $order_info['order_name'],
                    'keyword3' => time_to_date($order_info['create_time']),
                    'keyword4' => time_to_date($order_info['delivery_time']),
                    'keyword5' => time_to_date($order_info['sign_time']),
                ];
                $wechat_json = json_decode($data['message_info']['wechat_json'], true);
                $wechat_json['headtext'] = '买家购买的商品已经确认收货！';
                $wechat_json['bottomtext'] = '购买者信息：'.$order_info['name'].$order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile'];
                $data['message_info']['wechat_json'] = json_encode($wechat_json);
                $wechat_model->sendMessage($data);
            }
        }

    }

    /**
     * 订单发货提醒
     * @param $data
     */
    public function messageOrderDelivery($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "*");

        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);

        $package = model("express_delivery_package")->getInfo([["order_id", "=", $order_id]], "*");

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        //发送模板消息
        $wechat_model          = new WechatMessage();
        if (!empty($member_info) && !empty($member_info["wx_openid"])) {
            $data["openid"] = $member_info["wx_openid"];
            $data["template_data"] = [
                'keyword1' => $order_info['order_no'],
                'keyword2' => $package['express_company_name'],
                'keyword3' => $package['delivery_no'],
                'keyword4' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile']
            ];
            //$data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
            $wechat_model->sendMessage($data);
        }
        //通知医生
        $user = model("user")->getInfo([["uid", "=", $member_info['user_id']]], "*");
        if ($user['member_id'] != $member_info['member_id']){
            $member_info_result = $member_model->getMemberInfo([["member_id", "=", $user['member_id']]]);
            $member_info        = $member_info_result["data"];
            if (!empty($member_info) && !empty($member_info["wx_openid"])) {
                $data["openid"]        = $member_info["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['order_no'],
                    'keyword2' => $package['express_company_name'],
                    'keyword3' => $package['delivery_no'],
                    'keyword4' => $order_info['full_address'] . $order_info['address'] . " " . $order_info['mobile']
                ];
                $wechat_model->sendMessage($data);
            }
        }

    }

    /**
     * 订单收货提醒
     * @param $data
     */
    public function messageOrderTakeDelivery($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "*");

        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        //发送模板消息
        $wechat_model          = new WechatMessage();
        if (!empty($member_info) && !empty($member_info["wx_openid"])) {
            $data["openid"] = $member_info["wx_openid"];
            $data["template_data"] = [
                'keyword1' => $order_info['order_no'],
                'keyword2' => str_sub($order_info['order_name']),
                'keyword3' => time_to_date($order_info['create_time']),
                'keyword4' => time_to_date($order_info['delivery_time']),
                'keyword5' => time_to_date($order_info['sign_time']),
            ];
            //$data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
            $wechat_model->sendMessage($data);
        }
        //通知医生
        $user = model("user")->getInfo([["uid", "=", $member_info['user_id']]], "*");
        if ($user['member_id'] != $member_info['member_id']){
            $member_info_result = $member_model->getMemberInfo([["member_id", "=", $user['member_id']]]);
            $member_info        = $member_info_result["data"];
            if (!empty($member_info) && !empty($member_info["wx_openid"])) {
                $data["openid"]        = $member_info["wx_openid"];
                $data["template_data"] = [
                    'keyword1' => $order_info['order_no'],
                    'keyword2' => str_sub($order_info['order_name']),
                    'keyword3' => time_to_date($order_info['create_time']),
                    'keyword4' => time_to_date($order_info['delivery_time']),
                    'keyword5' => time_to_date($order_info['sign_time']),
                ];
                $wechat_model->sendMessage($data);
            }
        }
    }

    /**
     * 订单退款同意提醒
     * @param $data
     */
    public function messageOrderRefundAgree($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "order_type,order_no,mobile,member_id");

        $order_goods_info    = model("order_goods")->getInfo([["order_goods_id", "=", $data["order_goods_id"]]], "refund_apply_money,refund_time,refund_action_time");
        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        //发送模板消息
        $wechat_model          = new WechatMessage();
        $data["openid"]        = $member_info["wx_openid"];
        $data["template_data"] = [
            'keyword1' => $order_info['order_no'],
            'keyword2' => $order_goods_info["refund_apply_money"],
            'keyword3' => time_to_date($order_goods_info['refund_time']),
        ];
        $data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
        $wechat_model->sendMessage($data);
    }

    /**
     * 订单退款拒绝提醒
     * @param $data
     */
    public function messageOrderRefundRefuse($data)
    {
        //发送短信
        $sms_model           = new Sms();
        $order_id            = $data["order_id"];
        $order_info          = model("order")->getInfo([["order_id", "=", $order_id]], "order_type,order_no,mobile,member_id");
        $order_goods_info    = model("order_goods")->getInfo([["order_goods_id", "=", $data["order_goods_id"]]], "refund_apply_money,refund_time,refund_action_time");
        $var_parse           = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);

        $member_model       = new Member();
        $member_info_result = $member_model->getMemberInfo([["member_id", "=", $order_info["member_id"]]]);
        $member_info        = $member_info_result["data"];

        //发送模板消息
        $wechat_model          = new WechatMessage();
        $data["openid"]        = $member_info["wx_openid"];
        $data["template_data"] = [
            'keyword1' => $order_info['order_no'],
            'keyword2' => $order_goods_info["refund_apply_money"],
            'keyword3' => time_to_date($order_goods_info['refund_action_time']),
        ];
        $data["page"]          = $this->handleUrl($order_info['order_type'], $order_id);
        $wechat_model->sendMessage($data);
    }

    /**
     * 订单核销通知
     * @param $data
     */
    public function messageOrderVerify($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "order_type,order_no,mobile,member_id,order_name,goods_num,sign_time");

        $var_parse           = array(
            "orderno" => $order_info["order_no"],//订单编号
        );
        $data["sms_account"] = $order_info["mobile"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);
    }

    /**
     * 买家发起退款，卖家通知
     * @param $data
     */
    public function messageOrderRefundApply($data)
    {
        //发送短信
        $sms_model        = new Sms();
        $order_goods_id   = $data["order_goods_id"];
        $order_goods_info = model('order_goods')->getInfo(['order_goods_id' => $order_goods_id], 'order_id,refund_no,refund_reason,refund_apply_money,sku_name');

        $order_info = model("order")->getInfo([["order_id", "=", $order_goods_info['order_id']]], "order_no,mobile,member_id,site_id,name");
        $var_parse  = array(
            "username"     => replaceSpecialChar($order_info["name"]),//会员名
            "orderno"      => $order_info["order_no"],//订单编号
            "goodsname"    => replaceSpecialChar($order_goods_info["sku_name"]),//商品名称
            "refundno"     => $order_goods_info["refund_no"],//退款编号
            "refundmoney"  => $order_goods_info["refund_apply_money"],//退款申请金额
            "refundreason" => replaceSpecialChar($order_goods_info["refund_reason"]),//退款原因
        );
        $site_id    = $order_info['site_id'];
        $shop_info  = model("shop")->getInfo([["site_id", "=", $site_id]], "telephone,email");

        $data["sms_account"] = $shop_info["telephone"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);
    }

    /**
     * 买家已退款，卖家通知
     * @param $data
     */
    public function messageOrderRefundDelivery($data)
    {
        //发送短信
        $sms_model  = new Sms();
        $order_id   = $data["order_id"];
        $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "order_no,mobile,member_id,site_id");

        $var_parse = array(
            "orderno" => $order_info["order_no"],//商品名称
        );
        $site_id   = $order_info['site_id'];
        $shop_info = model("shop")->getInfo([["site_id", "=", $site_id]], "telephone,email");

        $data["sms_account"] = $shop_info["telephone"];//手机号
        $data["var_parse"]   = $var_parse;
        $sms_model->sendMessage($data);
    }

    /**
     * 处理订单链接
     * @param unknown $order_type
     * @param unknown $order_id
     * @return string
     */
    private function handleUrl($order_type, $order_id)
    {
        switch ($order_type) {
            case 2:
                return 'pages/order/detail_pickup/detail_pickup?order_id=' . $order_id;
                break;
            case 3:
                return 'pages/order/detail_local_delivery/detail_local_delivery?order_id=' . $order_id;
                break;
            case 4:
                return 'pages/order/detail_virtual/detail_virtual?order_id=' . $order_id;
                break;
            default:
                return 'pages/order/detail/detail?order_id=' . $order_id;
                break;
        }
    }
}