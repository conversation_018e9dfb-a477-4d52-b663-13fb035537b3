<?php
namespace app\model\pharmacy;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 套餐生产记录模型
 * Class PharmacyProductionLog
 * @package app\model\pharmacy
 */
class PharmacyProductionLog extends BaseModel
{
    protected $table = 'pharmacy_production_log';
    
    /**
     * 获取生产记录列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getProductionList($condition = [], $page = 1, $page_size = 20)
    {
        // 使用原生查询方式实现分页
        $offset = ($page - 1) * $page_size;

        // 获取总数
        $count = \think\facade\Db::name($this->table)->where($condition)->count();

        // 获取列表数据
        $list = \think\facade\Db::name($this->table)
            ->where($condition)
            ->order('create_time desc')
            ->limit($offset, $page_size)
            ->select()
            ->toArray();

        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
                $item['consumed_details_array'] = json_decode($item['consumed_details'], true);
                $item['produced_loose_details_array'] = json_decode($item['produced_loose_details'], true);
                $item['status_name'] = $item['status'] == 1 ? '成功' : '失败';

                // 计算生产统计
                $item['total_bottles_consumed'] = $this->calculateTotalBottles($item['consumed_details_array']);
                $item['total_loose_produced'] = $this->calculateTotalLoose($item['produced_loose_details_array']);
            }
        }

        $result = [
            'count' => $count,
            'list' => $list,
            'page_count' => ceil($count / $page_size)
        ];

        return $this->success($result);
    }
    
    /**
     * 获取生产记录详情
     * @param int $production_id 生产记录ID
     * @return array
     */
    public function getProductionDetail($production_id)
    {
        $production_info = Db::name($this->table)->where([['id', '=', $production_id]])->find();

        if (empty($production_info)) {
            return $this->error('', '生产记录不存在');
        }

        // 解析JSON数据
        $production_info['consumed_details_array'] = json_decode($production_info['consumed_details'], true) ?: [];
        $production_info['produced_loose_details_array'] = json_decode($production_info['produced_loose_details'], true) ?: [];
        $production_info['create_time_format'] = date('Y-m-d H:i:s', strtotime($production_info['create_time']));
        $production_info['status_name'] = $production_info['status'] == 1 ? '成功' : '失败';

        // 计算统计信息
        $production_info['total_bottles_consumed'] = $this->calculateTotalBottles($production_info['consumed_details_array']);
        $production_info['total_loose_produced'] = $this->calculateTotalLoose($production_info['produced_loose_details_array']);

        // 获取相关的库存操作日志
        $stock_logs = Db::name('pharmacy_stock_log')->where([
            ['related_id', '=', $production_id],
            ['operation_type', '=', 2]
        ])->select()->toArray();

        $production_info['related_stock_logs'] = $stock_logs;

        return $this->success($production_info);
    }
    
    /**
     * 获取生产统计信息
     * @param array $condition 查询条件
     * @return array
     */
    public function getProductionStats($condition = [])
    {
        // 今日生产统计
        $today_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-d 00:00:00')],
            ['create_time', '<=', date('Y-m-d 23:59:59')],
            ['status', '=', 1]
        ]);
        
        $today_stats = model($this->table)->field('COUNT(*) as count, SUM(production_quantity) as total_quantity, SUM(total_cost) as total_cost')
            ->where($today_condition)
            ->find();
        
        // 本月生产统计
        $month_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-01 00:00:00')],
            ['create_time', '<=', date('Y-m-t 23:59:59')],
            ['status', '=', 1]
        ]);
        
        $month_stats = model($this->table)->field('COUNT(*) as count, SUM(production_quantity) as total_quantity, SUM(total_cost) as total_cost')
            ->where($month_condition)
            ->find();

        // 按模板统计
        $template_stats = model($this->table)->field('template_id, template_name, COUNT(*) as count, SUM(production_quantity) as total_quantity')
            ->where(array_merge($condition, [['status', '=', 1]]))
            ->group('template_id')
            ->order('count desc')
            ->limit(10)
            ->select();

        // 按操作员统计
        $operator_stats = model($this->table)->field('operator_id, operator_name, COUNT(*) as count, SUM(production_quantity) as total_quantity')
            ->where(array_merge($condition, [['status', '=', 1]]))
            ->group('operator_id')
            ->order('count desc')
            ->limit(10)
            ->select();
        
        return $this->success([
            'today_stats' => [
                'count' => $today_stats['count'] ?? 0,
                'total_quantity' => $today_stats['total_quantity'] ?? 0,
                'total_cost' => $today_stats['total_cost'] ?? 0
            ],
            'month_stats' => [
                'count' => $month_stats['count'] ?? 0,
                'total_quantity' => $month_stats['total_quantity'] ?? 0,
                'total_cost' => $month_stats['total_cost'] ?? 0
            ],
            'template_stats' => $template_stats,
            'operator_stats' => $operator_stats
        ]);
    }
    
    /**
     * 获取模板生产历史
     * @param int $template_id 模板ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getTemplateHistory($template_id, $limit = 10)
    {
        $condition = [
            ['template_id', '=', $template_id],
            ['status', '=', 1]
        ];
        
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->limit($limit)
            ->select();
        
        foreach ($list as &$item) {
            $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
            $item['consumed_details_array'] = json_decode($item['consumed_details'], true);
            $item['total_bottles_consumed'] = $this->calculateTotalBottles($item['consumed_details_array']);
        }
        
        return $this->success($list);
    }
    
    /**
     * 获取操作员生产记录
     * @param int $operator_id 操作员ID
     * @param array $date_range 日期范围
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getOperatorProductions($operator_id, $date_range = [], $page = 1, $page_size = 20)
    {
        $condition = [['operator_id', '=', $operator_id]];
        
        if (!empty($date_range) && count($date_range) == 2) {
            $condition[] = ['create_time', '>=', $date_range[0]];
            $condition[] = ['create_time', '<=', $date_range[1]];
        }
        
        return $this->getProductionList($condition, $page, $page_size);
    }
    
    /**
     * 导出生产记录
     * @param array $condition 查询条件
     * @param string $format 导出格式
     * @return array
     */
    public function exportProductions($condition = [], $format = 'csv')
    {
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->select();
        
        $export_data = [];
        $export_data[] = [
            '生产ID', '模板名称', '生产数量', '操作员', '总成本', 
            '状态', '生产时间', '消耗详情', '产生散药详情'
        ];
        
        foreach ($list as $item) {
            $consumed_details = json_decode($item['consumed_details'], true);
            $produced_details = json_decode($item['produced_loose_details'], true);
            
            $consumed_summary = $this->formatConsumedSummary($consumed_details);
            $produced_summary = $this->formatProducedSummary($produced_details);
            
            $export_data[] = [
                $item['id'],
                $item['template_name'],
                $item['production_quantity'],
                $item['operator_name'],
                $item['total_cost'],
                $item['status'] == 1 ? '成功' : '失败',
                $item['create_time'],
                $consumed_summary,
                $produced_summary
            ];
        }
        
        return $this->success([
            'data' => $export_data,
            'filename' => 'pharmacy_production_log_' . date('YmdHis') . '.' . $format,
            'total_count' => count($list) - 1
        ]);
    }
    
    /**
     * 计算总消耗瓶数
     * @param array $consumed_details 消耗详情
     * @return int
     */
    private function calculateTotalBottles($consumed_details)
    {
        $total = 0;
        if (!empty($consumed_details['items'])) {
            foreach ($consumed_details['items'] as $item) {
                $total += $item['bottles_consumed'] ?? 0;
            }
        }
        return $total;
    }
    
    /**
     * 计算总产生散药数
     * @param array $produced_details 产生详情
     * @return int
     */
    private function calculateTotalLoose($produced_details)
    {
        $total = 0;
        if (!empty($produced_details['items'])) {
            foreach ($produced_details['items'] as $item) {
                $total += $item['loose_grains_added'] ?? 0;
            }
        }
        return $total;
    }
    
    /**
     * 格式化消耗摘要
     * @param array $consumed_details 消耗详情
     * @return string
     */
    private function formatConsumedSummary($consumed_details)
    {
        $summary = [];
        if (!empty($consumed_details['items'])) {
            foreach ($consumed_details['items'] as $item) {
                $summary[] = $item['sku_name'] . ':' . $item['bottles_consumed'] . '瓶';
            }
        }
        return implode('; ', $summary);
    }
    
    /**
     * 格式化产生摘要
     * @param array $produced_details 产生详情
     * @return string
     */
    private function formatProducedSummary($produced_details)
    {
        $summary = [];
        if (!empty($produced_details['items'])) {
            foreach ($produced_details['items'] as $item) {
                if ($item['loose_grains_added'] > 0) {
                    $summary[] = $item['sku_name'] . ':+' . $item['loose_grains_added'] . '颗';
                }
            }
        }
        return implode('; ', $summary);
    }
    
    /**
     * 添加生产记录
     * @param array $data 生产数据
     * @return int|false
     */
    public function addProduction($data)
    {
        // 确保必要字段存在
        $required_fields = ['site_id', 'template_id', 'template_name', 'production_quantity', 'operator_id', 'operator_name'];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        // 设置默认值
        $data['create_time'] = $data['create_time'] ?? date('Y-m-d H:i:s');
        $data['consumed_details'] = $data['consumed_details'] ?? '{}';
        $data['produced_loose_details'] = $data['produced_loose_details'] ?? '{}';
        $data['total_cost'] = $data['total_cost'] ?? 0;
        $data['status'] = $data['status'] ?? 1;
        $data['error_message'] = $data['error_message'] ?? '';
        
        return model($this->table)->add($data);
    }
}
