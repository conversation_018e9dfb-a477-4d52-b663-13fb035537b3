<?php
namespace app\model\pharmacy;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 药房库存管理模型
 * Class PharmacyStock
 * @package app\model\pharmacy
 */
class PharmacyStock extends BaseModel
{
    /**
     * 拆零操作
     * @param int $sku_id SKU ID
     * @param array $operator_info 操作员信息
     * @return array
     */
    public function splitBottleToLoose($sku_id, $operator_info)
    {
        Db::startTrans();
        try {
            // 1. 获取当前库存信息
            $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $sku_id]], 
                'sku_id,sku_name,whole_stock,loose_stock,daizhuang,goods_id');
            
            if (empty($sku_info)) {
                return $this->error('', 'SKU不存在');
            }
            
            if ($sku_info['whole_stock'] < 1) {
                return $this->error('', '整瓶库存不足，无法拆零');
            }
            
            // 2. 执行库存转换
            $grains_per_bottle = $sku_info['daizhuang'];
            $new_whole_stock = $sku_info['whole_stock'] - 1;
            $new_loose_stock = $sku_info['loose_stock'] + $grains_per_bottle;
            $new_total_stock = ($new_whole_stock * $grains_per_bottle) + $new_loose_stock;
            
            // 3. 更新库存
            model('goods_sku')->update([
                'whole_stock' => $new_whole_stock,
                'loose_stock' => $new_loose_stock,
                'stock' => $new_total_stock
            ], [['sku_id', '=', $sku_id]]);
            
            // 4. 更新商品总库存
            $this->updateGoodsStock($sku_info['goods_id']);
            
            // 5. 记录操作日志
            $log_data = [
                'site_id' => $operator_info['site_id'],
                'sku_id' => $sku_id,
                'sku_name' => $sku_info['sku_name'],
                'operation_type' => 1, // 拆零操作
                'operator_id' => $operator_info['uid'],
                'operator_name' => $operator_info['user_name'],
                'before_whole_stock' => $sku_info['whole_stock'],
                'before_loose_stock' => $sku_info['loose_stock'],
                'after_whole_stock' => $new_whole_stock,
                'after_loose_stock' => $new_loose_stock,
                'change_amount' => $grains_per_bottle,
                'remark' => "拆零操作：整瓶-1，散药+{$grains_per_bottle}颗"
            ];
            
            $stock_log_model = new \app\model\pharmacy\PharmacyStockLog();
            $stock_log_model->addLog($log_data);
            
            Db::commit();
            return $this->success([
                'sku_info' => $sku_info,
                'new_stock' => [
                    'whole_stock' => $new_whole_stock,
                    'loose_stock' => $new_loose_stock,
                    'total_grains' => $new_total_stock
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '拆零操作失败：' . $e->getMessage());
        }
    }
    
    /**
     * 套餐生产
     * @param int $template_id 模板ID
     * @param int $quantity 生产数量
     * @param array $operator_info 操作员信息
     * @return array
     */
    public function producePackage($template_id, $quantity, $operator_info)
    {
        Db::startTrans();
        try {
            // 1. 获取处方模板信息
            $template_info = model('order_attr_class')->getInfo([['class_id', '=', $template_id]], 
                'class_id,class_name,sku_ids,goods_name,goods_num');
            
            if (empty($template_info)) {
                return $this->error('', '处方模板不存在');
            }
            
            $sku_ids = array_filter(explode(',', $template_info['sku_ids']));
            $goods_names = json_decode($template_info['goods_name'], true) ?: [];
            $goods_nums = json_decode($template_info['goods_num'], true) ?: [];

            // 数据完整性检查
            if (empty($sku_ids)) {
                return $this->error('', '处方模板SKU数据为空');
            }

            if (count($sku_ids) !== count($goods_nums)) {
                return $this->error('', '处方模板数据不完整：SKU数量与商品数量不匹配');
            }

            // 2. 计算所需库存和验证
            $consumption_plan = [];
            $total_cost = 0;

            foreach ($sku_ids as $index => $sku_id) {
                // 确保SKU ID有效
                $sku_id = intval(trim($sku_id));
                if ($sku_id <= 0) {
                    continue;
                }

                // 检查数组边界
                if (!isset($goods_nums[$index])) {
                    continue;
                }

                $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $sku_id]],
                    'sku_id,sku_name,whole_stock,loose_stock,daizhuang,cost_price');

                if (empty($sku_info)) {
                    return $this->error('', "SKU {$sku_id} 不存在");
                }

                // 检查关键字段
                if ($sku_info['daizhuang'] <= 0) {
                    return $this->error('', "{$sku_info['sku_name']} 的每瓶颗粒数配置错误");
                }

                $needed_grains = $quantity * floatval($goods_nums[$index]);
                $bottles_needed = ceil($needed_grains / $sku_info['daizhuang']);
                $remaining_grains = ($bottles_needed * $sku_info['daizhuang']) - $needed_grains;
                
                if ($sku_info['whole_stock'] < $bottles_needed) {
                    return $this->error('', "{$sku_info['sku_name']} 整瓶库存不足，需要{$bottles_needed}瓶，现有{$sku_info['whole_stock']}瓶");
                }
                
                $consumption_plan[] = [
                    'sku_id' => $sku_id,
                    'sku_name' => $sku_info['sku_name'],
                    'needed_grains' => $needed_grains,
                    'bottles_consumed' => $bottles_needed,
                    'grains_per_bottle' => $sku_info['daizhuang'],
                    'remaining_grains' => $remaining_grains,
                    'current_whole_stock' => $sku_info['whole_stock'],
                    'current_loose_stock' => $sku_info['loose_stock'],
                    'cost_price' => $sku_info['cost_price']
                ];
                
                $total_cost += $bottles_needed * $sku_info['cost_price'];
            }
            
            // 3. 执行库存扣减
            $produced_loose_details = [];
            foreach ($consumption_plan as $item) {
                $new_whole_stock = $item['current_whole_stock'] - $item['bottles_consumed'];
                $new_loose_stock = $item['current_loose_stock'] + $item['remaining_grains'];
                $new_total_stock = ($new_whole_stock * $item['grains_per_bottle']) + $new_loose_stock;
                
                // 更新SKU库存
                model('goods_sku')->update([
                    'whole_stock' => $new_whole_stock,
                    'loose_stock' => $new_loose_stock,
                    'stock' => $new_total_stock
                ], [['sku_id', '=', $item['sku_id']]]);
                
                $produced_loose_details[] = [
                    'sku_id' => $item['sku_id'],
                    'sku_name' => $item['sku_name'],
                    'loose_grains_added' => $item['remaining_grains']
                ];
                
                // 记录库存操作日志
                $log_data = [
                    'site_id' => $operator_info['site_id'],
                    'sku_id' => $item['sku_id'],
                    'sku_name' => $item['sku_name'],
                    'operation_type' => 2, // 套餐生产
                    'operator_id' => $operator_info['uid'],
                    'operator_name' => $operator_info['user_name'],
                    'before_whole_stock' => $item['current_whole_stock'],
                    'before_loose_stock' => $item['current_loose_stock'],
                    'after_whole_stock' => $new_whole_stock,
                    'after_loose_stock' => $new_loose_stock,
                    'change_amount' => $item['remaining_grains'],
                    'related_id' => 0, // 稍后更新为生产记录ID
                    'remark' => "套餐生产：消耗{$item['bottles_consumed']}瓶，产生{$item['remaining_grains']}颗散药"
                ];
                
                $stock_log_model = new \app\model\pharmacy\PharmacyStockLog();
                $stock_log_model->addLog($log_data);
            }
            
            // 4. 记录生产日志
            $production_data = [
                'site_id' => $operator_info['site_id'],
                'template_id' => $template_id,
                'template_name' => $template_info['class_name'],
                'production_quantity' => $quantity,
                'operator_id' => $operator_info['uid'],
                'operator_name' => $operator_info['user_name'],
                'consumed_details' => json_encode(['items' => $consumption_plan]),
                'produced_loose_details' => json_encode(['items' => $produced_loose_details]),
                'total_cost' => $total_cost,
                'status' => 1
            ];
            
            $production_log_model = new \app\model\pharmacy\PharmacyProductionLog();
            $production_id = $production_log_model->addProduction($production_data);
            
            // 5. 更新库存日志的关联ID
            model('pharmacy_stock_log')->update(['related_id' => $production_id],
                [['operation_type', '=', 2], ['related_id', '=', 0], ['operator_id', '=', $operator_info['uid']]]);
            
            Db::commit();
            return $this->success([
                'production_id' => $production_id,
                'consumption_plan' => $consumption_plan,
                'total_cost' => $total_cost
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '套餐生产失败：' . $e->getMessage());
        }
    }
    
    /**
     * 入库操作
     * @param int $sku_id SKU ID
     * @param int $quantity 入库数量（瓶数）
     * @param array $operator_info 操作员信息
     * @return array
     */
    public function stockIn($sku_id, $quantity, $operator_info)
    {
        Db::startTrans();
        try {
            // 1. 获取当前库存信息
            $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $sku_id]], 
                'sku_id,sku_name,whole_stock,loose_stock,daizhuang,goods_id');
            
            if (empty($sku_info)) {
                return $this->error('', 'SKU不存在');
            }
            
            // 2. 计算新库存
            $new_whole_stock = $sku_info['whole_stock'] + $quantity;
            $new_total_stock = ($new_whole_stock * $sku_info['daizhuang']) + $sku_info['loose_stock'];
            $change_grains = $quantity * $sku_info['daizhuang'];
            
            // 3. 更新库存
            model('goods_sku')->update([
                'whole_stock' => $new_whole_stock,
                'stock' => $new_total_stock
            ], [['sku_id', '=', $sku_id]]);
            
            // 4. 更新商品总库存
            $this->updateGoodsStock($sku_info['goods_id']);
            
            // 5. 记录操作日志
            $log_data = [
                'site_id' => $operator_info['site_id'],
                'sku_id' => $sku_id,
                'sku_name' => $sku_info['sku_name'],
                'operation_type' => 3, // 入库操作
                'operator_id' => $operator_info['uid'],
                'operator_name' => $operator_info['user_name'],
                'before_whole_stock' => $sku_info['whole_stock'],
                'before_loose_stock' => $sku_info['loose_stock'],
                'after_whole_stock' => $new_whole_stock,
                'after_loose_stock' => $sku_info['loose_stock'],
                'change_amount' => $change_grains,
                'remark' => "入库操作：整瓶+{$quantity}，总颗粒+{$change_grains}"
            ];
            
            $stock_log_model = new \app\model\pharmacy\PharmacyStockLog();
            $stock_log_model->addLog($log_data);
            
            Db::commit();
            return $this->success([
                'sku_info' => $sku_info,
                'new_stock' => [
                    'whole_stock' => $new_whole_stock,
                    'loose_stock' => $sku_info['loose_stock'],
                    'total_grains' => $new_total_stock
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '入库操作失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取实时库存信息
     * @param int $sku_id SKU ID
     * @return array
     */
    public function getRealTimeStock($sku_id)
    {
        $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $sku_id]], 
            'sku_id,sku_name,whole_stock,loose_stock,daizhuang,sku_image');
        
        if (empty($sku_info)) {
            return $this->error('', 'SKU不存在');
        }
        
        $total_grains = ($sku_info['whole_stock'] * $sku_info['daizhuang']) + $sku_info['loose_stock'];
        
        return $this->success([
            'sku_info' => $sku_info,
            'total_grains' => $total_grains,
            'stock_detail' => [
                'whole_bottles' => $sku_info['whole_stock'],
                'loose_grains' => $sku_info['loose_stock'],
                'grains_per_bottle' => $sku_info['daizhuang']
            ]
        ]);
    }
    
    /**
     * 更新商品总库存
     * @param int $goods_id 商品ID
     */
    private function updateGoodsStock($goods_id)
    {
        // 计算商品所有SKU的总库存
        $total_stock = Db::name('goods_sku')->where([['goods_id', '=', $goods_id]])->sum('stock');

        // 更新商品表的库存
        model('goods')->update(['goods_stock' => $total_stock], [['goods_id', '=', $goods_id]]);
    }
}
