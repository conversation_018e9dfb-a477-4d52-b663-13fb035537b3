<?php
namespace app\model\pharmacy;

use app\model\BaseModel;

/**
 * 药房库存操作日志模型
 * Class PharmacyStockLog
 * @package app\model\pharmacy
 */
class PharmacyStockLog extends BaseModel
{
    protected $table = 'pharmacy_stock_log';
    
    /**
     * 获取操作日志列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getLogList($condition = [], $page = 1, $page_size = 20)
    {
        // 使用原生查询方式实现分页
        $offset = ($page - 1) * $page_size;

        // 获取总数
        $count = \think\facade\Db::name($this->table)->where($condition)->count();

        // 获取列表数据
        $list = \think\facade\Db::name($this->table)
            ->where($condition)
            ->order('create_time desc')
            ->limit($offset, $page_size)
            ->select()
            ->toArray();

        // 格式化操作类型
        $operation_types = [
            1 => '拆零操作',
            2 => '套餐生产',
            3 => '入库操作'
        ];

        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['operation_type_name'] = $operation_types[$item['operation_type']] ?? '未知操作';
                $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));

                // 计算库存变化
                $item['whole_stock_change'] = $item['after_whole_stock'] - $item['before_whole_stock'];
                $item['loose_stock_change'] = $item['after_loose_stock'] - $item['before_loose_stock'];

                // 格式化变化显示
                $item['stock_change_display'] = $this->formatStockChange($item);
            }
        }

        $result = [
            'count' => $count,
            'list' => $list,
            'page_count' => ceil($count / $page_size)
        ];

        return $this->success($result);
    }
    
    /**
     * 获取操作统计信息
     * @param array $condition 查询条件
     * @return array
     */
    public function getOperationStats($condition = [])
    {
        // 按操作类型统计
        $type_stats = model($this->table)->field('operation_type, COUNT(*) as count')
            ->where($condition)
            ->group('operation_type')
            ->select();
        
        $operation_types = [
            1 => '拆零操作',
            2 => '套餐生产', 
            3 => '入库操作'
        ];
        
        $formatted_stats = [];
        foreach ($type_stats as $stat) {
            $formatted_stats[] = [
                'type' => $stat['operation_type'],
                'type_name' => $operation_types[$stat['operation_type']] ?? '未知操作',
                'count' => $stat['count']
            ];
        }
        
        // 今日操作统计
        $today_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-d 00:00:00')],
            ['create_time', '<=', date('Y-m-d 23:59:59')]
        ]);
        
        $today_count = model($this->table)->where($today_condition)->count();

        // 本月操作统计
        $month_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-01 00:00:00')],
            ['create_time', '<=', date('Y-m-t 23:59:59')]
        ]);

        $month_count = model($this->table)->where($month_condition)->count();
        
        return $this->success([
            'type_stats' => $formatted_stats,
            'today_count' => $today_count,
            'month_count' => $month_count,
            'total_count' => model($this->table)->where($condition)->count()
        ]);
    }
    
    /**
     * 获取SKU操作历史
     * @param int $sku_id SKU ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getSkuHistory($sku_id, $limit = 10)
    {
        $condition = [['sku_id', '=', $sku_id]];
        
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->limit($limit)
            ->select();
        
        $operation_types = [
            1 => '拆零操作',
            2 => '套餐生产',
            3 => '入库操作'
        ];
        
        foreach ($list as &$item) {
            $item['operation_type_name'] = $operation_types[$item['operation_type']] ?? '未知操作';
            $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
            $item['stock_change_display'] = $this->formatStockChange($item);
        }
        
        return $this->success($list);
    }
    
    /**
     * 获取操作员操作记录
     * @param int $operator_id 操作员ID
     * @param array $date_range 日期范围
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getOperatorLogs($operator_id, $date_range = [], $page = 1, $page_size = 20)
    {
        $condition = [['operator_id', '=', $operator_id]];
        
        if (!empty($date_range) && count($date_range) == 2) {
            $condition[] = ['create_time', '>=', $date_range[0]];
            $condition[] = ['create_time', '<=', $date_range[1]];
        }
        
        return $this->getLogList($condition, $page, $page_size);
    }
    
    /**
     * 批量删除过期日志
     * @param int $days 保留天数
     * @return array
     */
    public function cleanExpiredLogs($days = 90)
    {
        $expire_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $count = model($this->table)->where([['create_time', '<', $expire_date]])->count();

        if ($count > 0) {
            $deleted = model($this->table)->where([['create_time', '<', $expire_date]])->delete();
            return $this->success(['deleted_count' => $deleted]);
        }
        
        return $this->success(['deleted_count' => 0]);
    }
    
    /**
     * 导出操作日志
     * @param array $condition 查询条件
     * @param string $format 导出格式 csv|excel
     * @return array
     */
    public function exportLogs($condition = [], $format = 'csv')
    {
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->select();
        
        $operation_types = [
            1 => '拆零操作',
            2 => '套餐生产',
            3 => '入库操作'
        ];
        
        $export_data = [];
        $export_data[] = [
            '日志ID', 'SKU名称', '操作类型', '操作员', '操作前整瓶库存', 
            '操作前散药库存', '操作后整瓶库存', '操作后散药库存', 
            '变化数量', '备注', '操作时间'
        ];
        
        foreach ($list as $item) {
            $export_data[] = [
                $item['id'],
                $item['sku_name'],
                $operation_types[$item['operation_type']] ?? '未知操作',
                $item['operator_name'],
                $item['before_whole_stock'],
                $item['before_loose_stock'],
                $item['after_whole_stock'],
                $item['after_loose_stock'],
                $item['change_amount'],
                $item['remark'],
                $item['create_time']
            ];
        }
        
        return $this->success([
            'data' => $export_data,
            'filename' => 'pharmacy_stock_log_' . date('YmdHis') . '.' . $format,
            'total_count' => count($list) - 1 // 减去标题行
        ]);
    }
    
    /**
     * 格式化库存变化显示
     * @param array $item 日志项
     * @return string
     */
    private function formatStockChange($item)
    {
        $whole_change = $item['after_whole_stock'] - $item['before_whole_stock'];
        $loose_change = $item['after_loose_stock'] - $item['before_loose_stock'];
        
        $changes = [];
        
        if ($whole_change != 0) {
            $changes[] = "整瓶" . ($whole_change > 0 ? '+' : '') . $whole_change;
        }
        
        if ($loose_change != 0) {
            $changes[] = "散药" . ($loose_change > 0 ? '+' : '') . $loose_change;
        }
        
        return implode(', ', $changes);
    }
    
    /**
     * 添加操作日志
     * @param array $data 日志数据
     * @return int|false
     */
    public function addLog($data)
    {
        // 确保必要字段存在
        $required_fields = ['site_id', 'sku_id', 'sku_name', 'operation_type', 'operator_id', 'operator_name'];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        // 设置默认值
        $data['create_time'] = $data['create_time'] ?? date('Y-m-d H:i:s');
        $data['before_whole_stock'] = $data['before_whole_stock'] ?? 0;
        $data['before_loose_stock'] = $data['before_loose_stock'] ?? 0;
        $data['after_whole_stock'] = $data['after_whole_stock'] ?? 0;
        $data['after_loose_stock'] = $data['after_loose_stock'] ?? 0;
        $data['change_amount'] = $data['change_amount'] ?? 0;
        $data['related_id'] = $data['related_id'] ?? 0;
        $data['batch_no'] = $data['batch_no'] ?? '';
        $data['remark'] = $data['remark'] ?? '';
        
        return model($this->table)->add($data);
    }
}
