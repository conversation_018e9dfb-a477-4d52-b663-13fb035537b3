<?php
namespace app\model\pharmacy;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 入库单管理模型
 * Class PharmacyStockinOrder
 * @package app\model\pharmacy
 */
class PharmacyStockinOrder extends BaseModel
{
    protected $table = 'pharmacy_stockin_orders';
    
    /**
     * 创建入库单
     * @param array $items 入库项目列表
     * @param array $operator_info 操作员信息
     * @return array
     */
    public function createStockinOrder($items, $operator_info)
    {
        Db::startTrans();
        try {
            // 1. 生成入库单号
            $order_no = $this->generateOrderNo('RK');
            
            // 2. 验证SKU并计算统计信息
            $total_bottles = 0;
            $product_count = [];
            $validated_items = [];
            
            foreach ($items as $item) {
                $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $item['sku_id']]], 
                    'sku_id,sku_name,sku_no,whole_stock,loose_stock,daizhuang,goods_id');
                
                if (empty($sku_info)) {
                    throw new \Exception("SKU ID {$item['sku_id']} 不存在");
                }
                
                $validated_items[] = [
                    'sku_info' => $sku_info,
                    'quantity' => $item['quantity']
                ];
                
                $total_bottles += $item['quantity'];
                $product_count[$item['sku_id']] = true;
            }
            
            $total_products = count($product_count);
            
            // 3. 创建入库单主记录
            $order_data = [
                'order_no' => $order_no,
                'site_id' => $operator_info['site_id'],
                'operator_id' => $operator_info['uid'],
                'operator_name' => $operator_info['user_name'],
                'total_bottles' => $total_bottles,
                'total_products' => $total_products,
                'status' => 1
            ];
            
            $order_id = model($this->table)->add($order_data);
            
            // 4. 执行入库操作并创建明细记录
            foreach ($validated_items as $item) {
                $sku_info = $item['sku_info'];
                $quantity = $item['quantity'];
                
                // 计算新库存
                $new_whole_stock = $sku_info['whole_stock'] + $quantity;
                $new_total_stock = ($new_whole_stock * $sku_info['daizhuang']) + $sku_info['loose_stock'];
                
                // 更新库存
                model('goods_sku')->update([
                    'whole_stock' => $new_whole_stock,
                    'stock' => $new_total_stock
                ], [['sku_id', '=', $sku_info['sku_id']]]);
                
                // 创建明细记录
                $item_data = [
                    'order_id' => $order_id,
                    'sku_id' => $sku_info['sku_id'],
                    'sku_name' => $sku_info['sku_name'],
                    'sku_no' => $sku_info['sku_no'],
                    'quantity' => $quantity,
                    'before_whole_stock' => $sku_info['whole_stock'],
                    'after_whole_stock' => $new_whole_stock
                ];
                
                model('pharmacy_stockin_order_items')->add($item_data);
                
                // 创建操作日志
                $log_data = [
                    'site_id' => $operator_info['site_id'],
                    'sku_id' => $sku_info['sku_id'],
                    'sku_name' => $sku_info['sku_name'],
                    'operation_type' => 3, // 入库操作
                    'operator_id' => $operator_info['uid'],
                    'operator_name' => $operator_info['user_name'],
                    'before_whole_stock' => $sku_info['whole_stock'],
                    'before_loose_stock' => $sku_info['loose_stock'],
                    'after_whole_stock' => $new_whole_stock,
                    'after_loose_stock' => $sku_info['loose_stock'],
                    'change_amount' => $quantity,
                    'order_type' => 2, // 入库单
                    'order_id' => $order_id,
                    'remark' => "入库单 {$order_no} - 入库 {$quantity} 瓶"
                ];
                
                model('pharmacy_stock_log')->add($log_data);
                
                // 更新商品总库存
                $this->updateGoodsStock($sku_info['goods_id']);
            }
            
            Db::commit();
            return $this->success([
                'order_id' => $order_id,
                'order_no' => $order_no,
                'total_bottles' => $total_bottles,
                'total_products' => $total_products
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '入库单创建失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取入库单列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getStockinOrderList($condition = [], $page = 1, $page_size = 20)
    {
        // 使用原生查询方式实现分页
        $offset = ($page - 1) * $page_size;

        // 获取总数
        $count = Db::name($this->table)->where($condition)->count();

        // 获取列表数据
        $list = Db::name($this->table)
            ->where($condition)
            ->order('create_time desc')
            ->limit($offset, $page_size)
            ->select()
            ->toArray();

        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
                $item['status_name'] = $item['status'] == 1 ? '成功' : '失败';
            }
        }

        $result = [
            'count' => $count,
            'list' => $list,
            'page_count' => ceil($count / $page_size)
        ];

        return $this->success($result);
    }
    
    /**
     * 获取入库单详情
     * @param int $order_id 入库单ID
     * @return array
     */
    public function getStockinOrderDetail($order_id)
    {
        $order_info = model($this->table)->getInfo([['id', '=', $order_id]], '*');
        if (empty($order_info)) {
            return $this->error('', '入库单不存在');
        }
        
        // 获取明细列表
        $items = model('pharmacy_stockin_order_items')->getList([['order_id', '=', $order_id]], '*', 'id asc');
        
        $order_info['create_time_format'] = date('Y-m-d H:i:s', strtotime($order_info['create_time']));
        $order_info['status_name'] = $order_info['status'] == 1 ? '成功' : '失败';
        $order_info['items'] = $items;
        
        return $this->success($order_info);
    }
    
    /**
     * 生成单据号
     * @param string $prefix 前缀
     * @return string
     */
    private function generateOrderNo($prefix = 'RK')
    {
        return $prefix . date('Ymd') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * 更新商品总库存
     * @param int $goods_id 商品ID
     */
    private function updateGoodsStock($goods_id)
    {
        $total_stock = Db::name('goods_sku')->where([['goods_id', '=', $goods_id]])->sum('stock');
        model('goods')->update(['goods_stock' => $total_stock], [['goods_id', '=', $goods_id]]);
    }
}
