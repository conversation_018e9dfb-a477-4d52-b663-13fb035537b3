<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\prescription;

use app\model\BaseModel;
use think\facade\Cache;

/**
 * 处方模板分类模型
 */
class PrescriptionCategory extends BaseModel
{
    /**
     * 添加分类
     * @param array $data
     * @return array
     */
    public function addCategory($data)
    {
        $site_id = isset($data['site_id']) ? $data['site_id'] : '';
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        // 处理分类路径
        if ($data['parent_id'] > 0) {
            $parent_info = model('prescription_template_category')->getInfo([['category_id', '=', $data['parent_id']], ['site_id', '=', $site_id]]);
            if (empty($parent_info)) {
                return $this->error('', '父级分类不存在');
            }
            $data['level'] = $parent_info['level'] + 1;
            $data['category_path'] = $parent_info['category_path'] . ',' . $data['parent_id'];
        } else {
            $data['level'] = 1;
            $data['category_path'] = '';
        }

        $category_id = model('prescription_template_category')->add($data);
        
        // 更新分类路径
        if ($data['parent_id'] == 0) {
            model('prescription_template_category')->update(['category_path' => $category_id], [['category_id', '=', $category_id]]);
        }

        Cache::tag("prescription_category_" . $site_id)->clear();
        return $this->success($category_id);
    }

    /**
     * 编辑分类
     * @param array $data
     * @return array
     */
    public function editCategory($data)
    {
        $site_id = isset($data['site_id']) ? $data['site_id'] : '';
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        $category_id = $data['category_id'];

        // 检查是否修改了父级分类
        if (isset($data['parent_id'])) {
            $current_info = model('prescription_template_category')->getInfo([['category_id', '=', $category_id]]);
            if ($data['parent_id'] != $current_info['parent_id']) {
                // 不能将分类设置为自己的子分类
                if ($data['parent_id'] > 0) {
                    $parent_info = model('prescription_template_category')->getInfo([['category_id', '=', $data['parent_id']]]);
                    if (strpos($parent_info['category_path'], (string)$category_id) !== false) {
                        return $this->error('', '不能将分类设置为自己的子分类');
                    }
                    $data['level'] = $parent_info['level'] + 1;
                    $data['category_path'] = $parent_info['category_path'] . ',' . $data['parent_id'];
                } else {
                    $data['level'] = 1;
                    $data['category_path'] = $category_id;
                }
            }
        }

        $res = model('prescription_template_category')->update($data, [['category_id', '=', $category_id], ['site_id', '=', $site_id]]);
        Cache::tag("prescription_category_" . $site_id)->clear();
        return $this->success($res);
    }

    /**
     * 删除分类
     * @param int $category_id
     * @param int $site_id
     * @return array
     */
    public function deleteCategory($category_id, $site_id)
    {
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        // 检查是否有子分类
        $child_count = model('prescription_template_category')->getCount([['parent_id', '=', $category_id], ['site_id', '=', $site_id]]);
        if ($child_count > 0) {
            return $this->error('', '该分类下存在子分类，无法删除');
        }

        // 检查是否有处方模板使用该分类
        $template_count = model('order_attr_class')->getCount([['category_id', '=', $category_id], ['site_id', '=', $site_id]]);
        if ($template_count > 0) {
            return $this->error('', '该分类下存在处方模板，无法删除');
        }

        $res = model('prescription_template_category')->delete([['category_id', '=', $category_id], ['site_id', '=', $site_id]]);
        Cache::tag("prescription_category_" . $site_id)->clear();
        return $this->success($res);
    }

    /**
     * 修改排序
     * @param int $sort
     * @param int $category_id
     * @param int $site_id
     * @return array
     */
    public function modifySort($sort, $category_id, $site_id)
    {
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        $res = model('prescription_template_category')->update(['sort' => $sort], [['category_id', '=', $category_id], ['site_id', '=', $site_id]]);
        Cache::tag("prescription_category_" . $site_id)->clear();
        return $this->success($res);
    }

    /**
     * 获取分类信息
     * @param array $condition
     * @param string $field
     * @return array
     */
    public function getCategoryInfo($condition, $field = '*')
    {
        $check_condition = array_column($condition, 2, 0);
        $site_id = isset($check_condition['site_id']) ? $check_condition['site_id'] : '';
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        $data = json_encode([$condition, $field]);
        $cache = Cache::get("prescription_category_getCategoryInfo_" . $site_id . "_" . $data);
        if (!empty($cache)) {
            return $this->success($cache);
        }

        $res = model('prescription_template_category')->getInfo($condition, $field);
        Cache::tag("prescription_category_" . $site_id)->set("prescription_category_getCategoryInfo_" . $site_id . "_" . $data, $res);
        return $this->success($res);
    }

    /**
     * 获取分类列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param null $limit
     * @return array
     */
    public function getCategoryList($condition = [], $field = '*', $order = 'sort asc, category_id asc', $limit = null)
    {
        $check_condition = array_column($condition, 2, 0);
        $site_id = isset($check_condition['site_id']) ? $check_condition['site_id'] : '';
        if ($site_id === '') {
            return $this->error('', 'REQUEST_SITE_ID');
        }

        $data = json_encode([$condition, $field, $order, $limit]);
        $cache = Cache::get("prescription_category_getCategoryList_" . $site_id . "_" . $data);
        if (!empty($cache)) {
            return $this->success($cache);
        }

        $list = model('prescription_template_category')->getList($condition, $field, $order, $limit);
        Cache::tag("prescription_category_" . $site_id)->set("prescription_category_getCategoryList_" . $site_id . "_" . $data, $list);
        return $this->success($list);
    }

    /**
     * 获取分类树形结构
     * @param int $site_id
     * @param int $parent_id
     * @return array
     */
    public function getCategoryTree($site_id, $parent_id = 0)
    {
        $condition = [
            ['site_id', '=', $site_id],
            ['status', '=', 1],
            ['parent_id', '=', $parent_id]
        ];
        
        $list = model('prescription_template_category')->getList($condition, '*', 'sort asc, category_id asc');
        
        foreach ($list as &$item) {
            $item['children'] = $this->getCategoryTree($site_id, $item['category_id']);
        }
        
        return $list;
    }

    /**
     * 获取分类下拉选项
     * @param int $site_id
     * @param int $level
     * @return array
     */
    public function getCategoryOptions($site_id, $level = 0)
    {
        $condition = [
            ['site_id', '=', $site_id],
            ['status', '=', 1]
        ];

        if ($level > 0) {
            $condition[] = ['level', '<=', $level];
        }
        
        $list = model('prescription_template_category')->getList($condition, 'category_id, category_name, parent_id, level', 'sort asc, category_id asc');
        
        $options = [];
        foreach ($list as $item) {
            $prefix = str_repeat('　', ($item['level'] - 1) * 2);
            $options[] = [
                'value' => $item['category_id'],
                'text' => $prefix . $item['category_name']
            ];
        }
        
        return $options;
    }
}
