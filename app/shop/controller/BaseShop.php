<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\Controller;
use app\model\system\Addon;
use app\model\system\Group as GroupModel;
use app\model\system\Menu;
use app\model\system\User as UserModel;
use app\model\web\Config as ConfigModel;
use app\model\web\WebSite;
use app\model\system\Site;
use app\model\goods\Goods as GoodsModel;
use addon\weapp\model\Config as WeappConfigModel;
use think\facade\Session;

class BaseShop extends Controller
{
    protected $init_menu = [];
    protected $crumbs = [];
    protected $crumbs_array = [];

    protected $uid;
    protected $user_info;
    protected $url;
    protected $group_info;
    protected $menus;
    protected $site_id;
    protected $shop_info;
    protected $app_module = "shop";
    protected $addon = '';

    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();
        //未来医生免登录
        $weilai   = input("weilaiyisheng", '0');
        $yuanzheng   = input("yuanzheng", '0');

        if ($yuanzheng==1){
            $user_condition = [
                ['username', '=', 'admin'],
                ['app_module', '=', 'shop'],
                ['site_id', '=', 1]
            ];
            $user_info      = model('user')->getInfo($user_condition);
            $time = time();
            //初始化登录信息
            $auth = array(
                'uid'         => $user_info['uid'],
                'username'    => $user_info['username'],
                'create_time' => $user_info['create_time'],
                'status'      => $user_info['status'],
                'group_id'    => $user_info["group_id"],
                'site_id'     => $user_info["site_id"],
                'app_group'   => $user_info["app_group"],
                'is_admin'    => $user_info['is_admin'],
                'login_time'  => $time,
                'login_ip'    => request()->ip(),
                'sys_uid'     => $user_info['sys_uid']
            );

            //更新登录记录
            $data = [
                'login_time' => time(),
                'login_ip'   => request()->ip(),
            ];
            model('user')->update($data, [['uid', "=", $user_info['uid']]]);
            Session::set($user_info['app_module'] . "_" . $user_info['site_id'] . ".uid", $user_info['uid']);
            Session::set($user_info['app_module'] . "_" . $user_info['site_id'] . ".user_info", $auth);
        }

        /*if ($weilai==1 || (isset($_COOKIE['weilaiyisheng']) && $_COOKIE['weilaiyisheng'] == 1)){
            $user_condition = [
                ['username', '=', '怀尚啦'],
                ['app_module', '=', 'shop'],
                ['site_id', '=', 1]
            ];
            $user_info      = model('user')->getInfo($user_condition);
            $time = time();
            //初始化登录信息
            $auth = array(
                'uid'         => $user_info['uid'],
                'username'    => $user_info['username'],
                'create_time' => $user_info['create_time'],
                'status'      => $user_info['status'],
                'group_id'    => $user_info["group_id"],
                'site_id'     => $user_info["site_id"],
                'app_group'   => $user_info["app_group"],
                'is_admin'    => $user_info['is_admin'],
                'login_time'  => $time,
                'login_ip'    => request()->ip(),
                'sys_uid'     => $user_info['sys_uid']
            );

            //更新登录记录
            $data = [
                'login_time' => time(),
                'login_ip'   => request()->ip(),
            ];
            model('user')->update($data, [['uid', "=", $user_info['uid']]]);
            Session::set($user_info['app_module'] . "_" . $user_info['site_id'] . ".uid", $user_info['uid']);
            Session::set($user_info['app_module'] . "_" . $user_info['site_id'] . ".user_info", $auth);
            setcookie('weilaiyisheng',1,time()+60*60*24*30,'/; samesite=None','.clinicsupplement.com',1);

        }*/
        //检测基础登录
        $this->site_id = request()->siteid();
        $user_model = new UserModel();
        $this->uid = $user_model->uid($this->app_module, $this->site_id);
        if (empty($this->uid)) {
            $this->redirect(url("shop/login/login"));
        }
        $this->url = request()->parseUrl();
        $this->addon = request()->addon() ? request()->addon() : '';
        $this->user_info = $user_model->userInfo($this->app_module, $this->site_id);
        $this->assign("user_info", $this->user_info);
        $user_condition = [
            ['uid', '=', $this->uid],
            ['app_module', '=', 'shop'],
            ['site_id', '=', 1]
        ];
        $userr      = model('user')->getInfo($user_condition);
        //查询钱包记录
        $userr_balance      = model('user_balance_log')->getInfo([
            ['uid', '=', $this->uid]
        ]);
        if ($userr['isbalance'] && $userr_balance){
            $userr['balance_tixing_is'] = 1;
        }else{
            $userr['balance_tixing_is'] = 0;
        }
        $this->assign("userr", $userr);
        $this->checkLogin();
        //检测用户组
        $this->getGroupInfo();
        if (!$this->checkAuth()) {
            if (!request()->isAjax()) {
                $this->error('权限不足');
            } else {
                echo json_encode(error('', '权限不足'));
                exit;
            }

        }
        //获取店铺信息
        $site_model = new Site();
        $shop_info = $site_model->getSiteInfo([ [ 'site_id', '=', $this->site_id ] ], 'site_name,logo,seo_keywords,seo_description, create_time');
        $this->shop_info = $shop_info[ 'data' ];

        $this->assign("shop_info", $shop_info[ 'data' ]);
        //产品预警消息
        $goods_model = new GoodsModel();
        $ress        = $goods_model->getbaojingGood();
        $this->assign("baojingnotice", (count($ress)>0&&($this->user_info[ "group_id" ]==1||$this->user_info[ "group_id" ]==3))?'预警库存产品:'.count($ress).' ':'');
        //消息统计
        $listr = model('notice_uid')->getList([['uid','=',$this->uid]], '*');
        $listn = model('notice')->getList();
        $notice_s = count($listn) - count($listr);
        $this->assign("notice", $notice_s);
        //一天之内获取最新消息
        $notice_title = '';
        $notice_content = '';
        $listt = model('notice_uid')->getList([['uid','=',$this->uid],['timed','>',time()-24*60*60]], '*');
        if (!$listt){
            //取一条消息出来
            $listnow = model('notice')->getList([['id','not in',array_column($listr,'notice_id')]], '*');
            if ($listnow){
                $notice_title = $listnow[0]['title'];
                $notice_content = $listnow[0]['content'];
                model('notice_uid')->add([
                    'notice_id'=>$listnow[0]['id'],
                    'uid'=>$this->uid,
                    'timed'=>time()
                ]);
            }
        }
        $listback = model('notice')->getList([['is_back','=',1]], '*');
        //如果有余额不足提醒,每天提醒一次
        $listt = model('notice_balance')->getList([['uid','=',$this->uid],['timed','>',time()-24*60*60]], '*');
        if ($userr['balance_tixing_is']>0 && $userr['balance_tixing']>$userr['balance'] && !$listt){
            model('notice_balance')->add([
                'uid'=>$this->uid,
                'timed'=>time()
            ]);
            $notice_title = '余额提醒';
            $notice_content = '<style>
        .balance-container {
            text-align: center;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
        }
        .balance {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .warning {
            color: red;
            font-size: 18px;
        }
    </style>
    <div class="balance-container">
        <div class="balance">您的当前余额：'.$userr['balance'].'</div>
        <div class="warning">您的余额已不足，为不影响您的正常业务，请尽快充值!</div>
    </div>';
        }
        $this->assign("notice_back", $listback);
        $this->assign("notice_title", $notice_title);
        $this->assign("notice_content", $notice_content);
        if (!request()->isAjax()) {
            //获取菜单
            $this->menus = $this->getMenuList();
            $this->initBaseInfo();
        }
    }

    /**
     * 加载基础信息
     */
    private function initBaseInfo()
    {
        //获取一级权限菜单
        $this->getTopMenu();
        $menu_model = new Menu();
        $info_result = $menu_model->getMenuInfoByUrl($this->url, $this->app_module, $this->addon);
        $info = [];
        if (!empty($info_result[ "data" ])) {
            $info = $info_result[ "data" ];
            $this->getParentMenuList($info[ 'name' ]);
        }
        $this->assign("menu_info", $info);
        //加载菜单树
        $init_menu = $this->initMenu($this->menus, '');

        // 应用下的菜单特殊处理
        if (!empty($this->crumbs) && $this->crumbs[ 0 ][ 'name' ] == 'TOOL_ROOT') {

            //如果当前选择了【应用管理】，则只保留【应用管理】菜单
            if ($this->crumbs[ 1 ][ 'name' ] == 'PROMOTION_TOOL') {
                foreach ($init_menu as $k => $v) {
                    if ($v[ 'selected' ]) {
                        $init_menu[ $k ][ 'child_list' ] = [ $v[ 'child_list' ][ 'PROMOTION_TOOL' ] ];
                        break;
                    }
                }
            } else {
                //选择了应用下的某个插件，则移除【应用管理】菜单，显示该插件下的菜单，并且标题名称改为插件名称
                $addon_model = new Addon();
                $addon_info = $addon_model->getAddonInfo([ [ 'name', '=', request()->addon() ] ], 'name,title');
                $addon_info = $addon_info[ 'data' ];
                foreach ($init_menu as $k => $v) {
                    if ($v[ 'selected' ]) {
//                        var_dump($init_menu[ $k ][ 'child_list' ]);
//                        var_dump("<hr/>");
//                        var_dump("<hr/>");
                        $this->crumbs[ 0 ][ 'title' ] = $addon_info[ 'title' ];
                        unset($init_menu[ $k ][ 'child_list' ][ 'PROMOTION_TOOL' ]);
//                        var_dump($init_menu[$k]['child_list']);
                        foreach ($init_menu[ $k ][ 'child_list' ] as $ck => $cv) {
//                            var_dump($addon_info[ 'name' ]);
                            if ($cv[ 'addon' ] != $addon_info[ 'name' ]) {
                                unset($init_menu[ $k ][ 'child_list' ][ $ck ]);
                            }
//                            var_dump($init_menu[ $k ][ 'child_list' ][ $ck ]);
//                            var_dump("<hr/>");
                        }
                        break;
                    }
                }
            }
        }

        $this->assign("url", $this->url);
        $this->assign("menu", $init_menu);
        $this->assign("crumbs", $this->crumbs);
        //加载版权信息
        $config_model = new ConfigModel();
        $copyright = $config_model->getCopyright();
        $this->assign('copyright', $copyright[ 'data' ][ 'value' ]);

        // 查询小程序配置信息
        $weapp_config_model = new WeappConfigModel();
        $weapp_config = $weapp_config_model->getWeappConfig($this->site_id);
        $weapp_config = $weapp_config[ 'data' ][ 'value' ];
        $this->assign('base_weapp_config', $weapp_config);
    }

    /**
     * layui化处理菜单数据
     */
    public function initMenu($menus_list, $parent = "")
    {
        $temp_list = [];
        if (!empty($menus_list)) {
            foreach ($menus_list as $menu_k => $menu_v) {

                if (in_array($menu_v[ 'name' ], $this->crumbs_array)) {
                    $selected = true;
                } else {
                    $selected = false;
                }

                if ($menu_v[ "parent" ] == $parent && $menu_v[ "is_show" ] == 1) {
                    $temp_item = array (
                        'addon' => $menu_v[ 'addon' ],
                        'selected' => $selected,
                        'url' => addon_url($menu_v[ 'url' ]),
                        'title' => $menu_v[ 'title' ],
                        'icon' => $menu_v[ 'picture' ],
                        'icon_selected' => $menu_v[ 'picture_select' ],
                        'target' => ''
                    );

                    $child = $this->initMenu($menus_list, $menu_v[ "name" ]);//获取下级的菜单
                    $temp_item[ "child_list" ] = $child;
                    $temp_list[ $menu_v[ "name" ] ] = $temp_item;
                }
            }
        }
        return $temp_list;
    }

    /**
     * 获取上级菜单列表
     * @param number $menu_id
     */
    private function getParentMenuList($name = '')
    {
        if (!empty($name)) {
            $menu_model = new Menu();
            $menu_info_result = $menu_model->getMenuInfo([ [ 'name', "=", $name ], [ 'app_module', '=', $this->app_module ] ]);
            $menu_info = $menu_info_result[ "data" ];
            if (!empty($menu_info)) {
                $this->getParentMenuList($menu_info[ 'parent' ]);
                $menu_info[ "url" ] = addon_url($menu_info[ "url" ]);
                $this->crumbs[] = $menu_info;
                $this->crumbs_array[] = $menu_info[ 'name' ];
            }
        }

    }

    /**
     * 获取当前用户的用户组
     */
    private function getGroupInfo()
    {
        $group_model = new GroupModel();
        $group_info_result = $group_model->getGroupInfo([ [ "group_id", "=", $this->user_info[ "group_id" ] ], [ "site_id", "=", $this->site_id ], [ "app_module", "=", $this->app_module ] ]);
        $this->group_info = $group_info_result[ "data" ];

    }

    /**
     * 验证登录
     */
    private function checkLogin()
    {
        //验证基础登录
        if (!$this->uid) {
            $this->redirect(url('shop/login/login'));
        }
    }

    /**
     * 检测权限
     */
    private function checkAuth()
    {
        $user_model = new UserModel();
        $res = $user_model->checkAuth($this->url, $this->app_module, $this->group_info);
        return $res;
    }

    /**
     * 获取菜单
     */
    private function getMenuList()
    {
        $menu_model = new Menu();
        //暂定全部权限，系统用户做完后放开
        if ($this->user_info[ 'is_admin' ] || $this->group_info[ 'is_system' ] == 1) {
            $menus = $menu_model->getMenuList([ [ 'app_module', "=", $this->app_module ], [ 'is_show', "=", 1 ] ], '*', 'sort asc');
        } else {
            //特殊处理一些账号权限
            //非上级取消钱包显示
            $user      = model('user')->getInfo([
                ['uid', '=', $this->uid]
            ]);

            if (( $this->uid == 14 ||$this->uid == 478 ||$this->uid == 15 || $this->uid == 16 || $this->uid == 21 || $this->uid == 29 || $this->uid == 30 || $this->uid == 31 || $this->uid == 69)){
                $menus = $menu_model->getMenuList([ [ 'name', 'in', 'SHOP_ROOT,WEBSITE_CONFIG,WEBSITE_NOTICE,WEBSITE_NOTICE_DETAIL,ORDER_ROOT,ORDER_MANUAL,ORDER_MANUAK,ORDERATTR_MANAGE,ORDERATTR_ADD,ORDERATTR_ADDD,ORDERATTR_EDIT,ORDERATTR_SORT,ORDERATTR_DELETE,ORDER_MANAGE,EXPRESS_ORDER_DETAIL,EXPRESS_ORDER_CLOSE,EXPRESS_ORDER_EDIT_ADDRESS,LOCAL_ORDER_DETAIL,LOCAL_ORDER_DELIVER,STORE_ORDER_DETAIL,VIRTUAL_ORDER_DETAIL,ORDERGUDING_MANAGE,ORDERGUDING_ADD,ORDERGUDING_EXP,ORDERGUDING_EDIT,ORDERCANKAO_MANAGE,AI_ROOT,AI_HELP_ADD,MEMBER_ROOT,SHOP_MEMBER_INDEX,MEMBER_INDEX,MEMBER_LIST,MEMBER_ADD,MEMBER_EDIT,MEMBER_DELETE,MEMBER_ORDER,MEMBER_ADDRESS,MEMBER_DETAIL,MEMBER_STATUS_MODIFY,MEMBER_PASSWORD_MODIFY,INV_ROOT,INV_LIST,INV_ADD,INV_EDIT,INV_DEL,INV_WAL,STAT_ROOT,STAT_SHOP,KECHEN_ROOT,HELP_ROOT,WEBSITE_HELP,WEBSITE_HELP_ADD,WEBSITE_HELP_EDIT,WEBSITE_HELP_DELETE,INV_PIC'], [ 'is_show', "=", 1 ], [ 'app_module', "=", $this->app_module ] ], '*', 'sort asc');
            }else{
                if ($user['source_uid']!=0){
                    $menus = $menu_model->getMenuList([ [ 'name', 'in', 'SHOP_ROOT,WEBSITE_CONFIG,WEBSITE_NOTICE,WEBSITE_NOTICE_DETAIL,ORDER_ROOT,ORDER_MANUAL,ORDER_MANUAK,ORDERATTR_MANAGE,ORDERATTR_ADD,ORDERATTR_ADDD,ORDERATTR_EDIT,ORDERATTR_SORT,ORDERATTR_DELETE,ORDER_MANAGE,EXPRESS_ORDER_DETAIL,EXPRESS_ORDER_CLOSE,EXPRESS_ORDER_EDIT_ADDRESS,LOCAL_ORDER_DETAIL,LOCAL_ORDER_DELIVER,STORE_ORDER_DETAIL,VIRTUAL_ORDER_DETAIL,ORDERGUDING_MANAGE,ORDERGUDING_ADD,ORDERGUDING_EXP,ORDERGUDING_EDIT,ORDERCANKAO_MANAGE,MEMBER_ROOT,SHOP_MEMBER_INDEX,MEMBER_INDEX,MEMBER_LIST,MEMBER_ADD,MEMBER_EDIT,MEMBER_DELETE,MEMBER_ORDER,MEMBER_ADDRESS,MEMBER_DETAIL,MEMBER_STATUS_MODIFY,MEMBER_PASSWORD_MODIFY,INV_LIST,INV_ADD,INV_EDIT,INV_DEL,INV_WAL,KECHEN_ROOT,HELP_ROOT,WEBSITE_HELP,WEBSITE_HELP_ADD,WEBSITE_HELP_EDIT,WEBSITE_HELP_DELETE'], [ 'is_show', "=", 1 ], [ 'app_module', "=", $this->app_module ] ], '*', 'sort asc');

                }else{
                    $menus = $menu_model->getMenuList([ [ 'name', 'in', 'SHOP_ROOT,WEBSITE_CONFIG,WEBSITE_NOTICE,WEBSITE_NOTICE_DETAIL,ORDER_ROOT,ORDER_MANUAL,ORDER_MANUAK,ORDERATTR_MANAGE,ORDERATTR_ADD,ORDERATTR_ADDD,ORDERATTR_EDIT,ORDERATTR_SORT,ORDERATTR_DELETE,ORDER_MANAGE,EXPRESS_ORDER_DETAIL,EXPRESS_ORDER_CLOSE,EXPRESS_ORDER_EDIT_ADDRESS,LOCAL_ORDER_DETAIL,LOCAL_ORDER_DELIVER,STORE_ORDER_DETAIL,VIRTUAL_ORDER_DETAIL,ORDERGUDING_MANAGE,ORDERGUDING_ADD,ORDERGUDING_EXP,ORDERGUDING_EDIT,ORDERCANKAO_MANAGE,MEMBER_ROOT,SHOP_MEMBER_INDEX,MEMBER_INDEX,MEMBER_LIST,MEMBER_ADD,MEMBER_EDIT,MEMBER_DELETE,MEMBER_ORDER,MEMBER_ADDRESS,MEMBER_DETAIL,MEMBER_STATUS_MODIFY,MEMBER_PASSWORD_MODIFY,INV_ROOT,INV_LIST,INV_ADD,INV_EDIT,INV_DEL,INV_WAL,STAT_ROOT,STAT_SHOP,KECHEN_ROOT,HELP_ROOT,WEBSITE_HELP,WEBSITE_HELP_ADD,WEBSITE_HELP_EDIT,WEBSITE_HELP_DELETE,INV_PIC' ], [ 'is_show', "=", 1 ], [ 'app_module', "=", $this->app_module ] ], '*', 'sort asc');

                }
            }
        }

        return $menus[ 'data' ];
    }

    /**
     * 获取顶级菜单
     */
    protected function getTopMenu()
    {
        $list = array_filter($this->menus, function($v) {
            return $v[ 'parent' ] == '0';
        });
        return $list;

    }

    /**
     * 四级菜单
     * @param unknown $params
     */
    protected function forthMenu($params = [])
    {
        $url = strtolower($this->url);
        $menu_model = new Menu();
        $menu_info = $menu_model->getMenuInfo([ [ 'url', "=", $url ], [ 'level', '=', 4 ] ], 'parent');
        if (!empty($menu_info[ 'data' ])) {
            $menus = $menu_model->getMenuList([ [ 'app_module', "=", $this->app_module ], [ 'is_show', "=", 1 ], [ 'parent', '=', $menu_info[ 'data' ][ 'parent' ] ] ], '*', 'sort asc');
            foreach ($menus[ 'data' ] as $k => $v) {
                $menus[ 'data' ][ $k ][ 'parse_url' ] = addon_url($menus[ 'data' ][ $k ][ 'url' ], $params);
                if ($menus[ 'data' ][ $k ][ 'url' ] == $url) {
                    $menus[ 'data' ][ $k ][ 'selected' ] = 1;
                } else {
                    $menus[ 'data' ][ $k ][ 'selected' ] = 0;
                }
            }
            $this->assign('forth_menu', $menus[ 'data' ]);
        }
    }

    /**
     * 五级菜单
     * @param unknown $params
     */
    protected function fiveMenu($params = [])
    {
        $url = strtolower($this->url);
        $menu_model = new Menu();
        $menu_info = $menu_model->getMenuInfo([ [ 'url', "=", $url ], [ 'level', '=', 5 ] ], 'parent');

        if (!empty($menu_info[ 'data' ])) {
            $menus = $menu_model->getMenuList([ [ 'app_module', "=", $this->app_module ], [ 'is_show', "=", 1 ], [ 'parent', '=', $menu_info[ 'data' ][ 'parent' ] ] ], '*', 'sort asc');
            foreach ($menus[ 'data' ] as $k => $v) {
                $menus[ 'data' ][ $k ][ 'parse_url' ] = addon_url($menus[ 'data' ][ $k ][ 'url' ], $params);
                if ($menus[ 'data' ][ $k ][ 'url' ] == $url) {
                    $menus[ 'data' ][ $k ][ 'selected' ] = 1;
                } else {
                    $menus[ 'data' ][ $k ][ 'selected' ] = 0;
                }
            }
            $this->assign('forth_menu', $menus[ 'data' ]);
        }
    }

    /**
     * 添加日志
     * @param unknown $action_name
     * @param unknown $data
     */
    protected function addLog($action_name, $data = [])
    {
        $user = new UserModel();
        $user->addUserLog($this->uid, $this->user_info[ 'username' ], $this->site_id, $action_name, $data);
    }

}