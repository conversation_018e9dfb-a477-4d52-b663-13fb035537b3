<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\express\ExpressTemplate as ExpressTemplateModel;
use app\model\goods\Goods as GoodsModel;
use app\model\system\User as UserModel;
use app\model\goods\GoodsAttribute as GoodsAttributeModel;
use app\model\goods\GoodsCategory as GoodsCategoryModel;
use app\model\goods\GoodsEvaluate as GoodsEvaluateModel;
use app\model\goods\GoodsService as GoodsServiceModel;
use app\model\goods\GoodsLabel as GoodsLabelModel;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\order\OrderExport;
use think\Exception;

/**
 * 实物商品
 * Class Goods
 * @package app\shop\controller
 */
class Cart extends BaseShop
{

    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();
    }

    // 获取购物车列表
    public function list()
    {
        $cartData = model('shop_cart')->getList([['member_id','=',$this->uid]], '*');
        return json(['code' => 0, 'data' => $cartData]);
    }

    // 添加到购物车
    public function add()
    {
        $member_id = $this->uid;

        $goods_id = input("goods_id",0);
        $goods_name = input("goods_name");
        $price = input("price");

        // 检查商品是否已存在
        $exists = model('shop_cart')->getInfo([['member_id','=',$this->uid],['goods_id','=',$goods_id]], '*');

        if ($exists) {
            return json(['code' => 1, 'message' => '商品已在购物车中']);
        }

        // 添加到购物车
        $data = [
            'member_id' => $member_id,
            'goods_id' => $goods_id,
            'goods_name' => $goods_name,
            'price' => $price,
            'create_time' => time()
        ];
        $result = model('shop_cart')->add($data);

        if ($result) {
            $cartData = model('shop_cart')->getList([['member_id','=',$this->uid]], '*');
            return json(['code' => 0, 'message' => '添加成功', 'data' => $cartData]);
        } else {
            return json(['code' => -1, 'message' => '添加失败']);
        }
    }

    // 批量添加到购物车
    public function batchAdd()
    {
        $member_id = $this->uid;

        $goodsList = json_decode(input("goods_list"), true);
        $addedCount = 0;

        foreach ($goodsList as $goods) {
            // 检查商品是否已存在
            $exists = model('shop_cart')->getInfo([['member_id','=',$this->uid],['goods_id','=',$goods['goods_id']]], '*');

            if (!$exists) {
                $data = [
                    'member_id' => $member_id,
                    'goods_id' => $goods['goods_id'],
                    'goods_name' => $goods['goods_name'],
                    'price' => $goods['price'],
                    'create_time' => time()
                ];

                model('shop_cart')->add($data);
                $addedCount++;
            }
        }

        $cartData = model('shop_cart')->getList([['member_id','=',$this->uid]], '*');

        return json(['code' => 0, 'message' => "成功添加 {$addedCount} 件商品", 'data' => $cartData]);
    }

    // 从购物车移除
    public function remove()
    {
        $member_id = $this->uid;

        $goods_ids = explode(',', input("goods_ids"));

        $result = model('shop_cart')->delete([['member_id','=',$this->uid],['goods_id','in',$goods_ids]]);

        if ($result) {
            $cartData = model('shop_cart')->getList([['member_id','=',$this->uid]], '*');
            return json(['code' => 0, 'message' => '删除成功', 'data' => $cartData]);
        } else {
            return json(['code' => -1, 'message' => '删除失败']);
        }
    }

}