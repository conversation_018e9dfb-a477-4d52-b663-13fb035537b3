<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\goods\Config as GoodsConfigModel;
use app\model\system\Pay;
use app\model\web\Config as ConfigModel;
use app\model\system\Api;
use extend\RSA;
use app\model\system\Upgrade;

/**
 * 设置 控制器
 */
class Config extends BaseShop
{
    public function copyright()
    {
        $upgrade_model = new Upgrade();
        $auth_info     = $upgrade_model->authInfo();

        $config_model = new ConfigModel();
        $copyright    = $config_model->getCopyright($this->site_id, $this->app_module);
        if (request()->isAjax()) {
            $logo = input('logo', '');
            $data = [
                'icp'        => input('icp', ''),
                'gov_record' => $gov_record = input('gov_record', ''),
                'gov_url'    => input('gov_url', ''),
                'logo' => '',
                'company_name' => '',
                'copyright_link' => '',
                'copyright_desc' => ''
            ];
            if ($auth_info['code'] >= 0) {
                $data['logo'] = input('logo', '');
                $data['company_name'] = input('company_name', '');
                $data['copyright_link'] = input('copyright_link', '');
                $data['copyright_desc'] = input('copyright_desc', '');
            }
            $this->addLog("修改版权配置");
            $res = $config_model->setCopyright($data, $this->site_id, $this->app_module);
            return $res;
        }
        $this->assign('is_auth', ($auth_info['code'] >= 0 ? 1 : 0));
        $this->assign('copyright_config', $copyright['data']['value']);
        return $this->fetch('config/copyright');
    }

    /**
     * 支付管理
     */
    public function pay()
    {
        if (request()->isAjax()) {
            $pay_model = new Pay();
            $list      = $pay_model->getPayType([]);
            return $list;
        } else {
            return $this->fetch('config/pay');
        }
    }

    /**
     * 默认图设置
     */
    public function defaultPicture()
    {
        if (request()->isAjax()) {
            $data = array(
               // "default_goods_img" => input("default_goods_img", ""),
                "headimg"   => input("default_headimg", ""),
            );
            $this->addLog("修改说明单机构标识");
            $condition = array(
                ["uid", "=", $this->uid]
            );
            $res = model("user")->update($data, $condition);

            return $res;
        } else {
            $user = model("user")->getInfo([["uid", "=", $this->uid]], "*");
            $this->assign("default_img", $user['headimg']);
            return $this->fetch('config/default_picture');
        }
    }

    /*
     * 售后保障
     */
    public function aftersale()
    {
        $goods_config_model = new GoodsConfigModel();
        if (request()->isAjax()) {
            $content = input('content', '');//售后保障协议
            return $goods_config_model->setAfterSaleConfig('售后保障协议', $content, $this->site_id);
        } else {
            $content = $goods_config_model->getAfterSaleConfig($this->site_id);
            $this->assign('content', $content);
            return $this->fetch('config/aftersale');
        }
    }

    /**
     * 验证码设置
     */
    public function captcha()
    {
        $sor_list = Db::connect('v3')->name('sor')->where('customer=2')->select()->toArray();
        $sell_list = Db::connect('v3')->name('sell')->where('customer=2')->select()->toArray();
        $stock_in = model('stock_in')->getList([],'*');
        $stock_in = array_column($stock_in,'package_no');
        $sor = [];
        $state = [0=>'未出库',1=>'部分出库',2=>'已出库',3=>'关闭'];
        foreach ($sor_list as $k=>$vo){
            $vo['timee'] = $vo['time'];
            $vo['time'] = date('Y-m-d',$vo['time']);
            $vo['state'] = $state[$vo['state']];
            $sor[$vo['id']] = $vo;
        }
        foreach($sell_list as $k=>$vo){
            if (isset($sor[$vo['source']])){
                $vo['timee'] = $vo['time'];
                $vo['time'] = date('Y-m-d',$vo['time']);
                $vo['ruku'] = in_array($vo['number'],$stock_in)?1:0;
                $sor[$vo['source']]['child'][]=$vo;
            }else{
                $sor[$vo['number']] = ['id'=>'-','time'=>'-','number'=>'-','state'=>'-','data'=>'-'];
                $vo['timee'] = $vo['time'];
                $vo['time'] = date('Y-m-d',$vo['time']);
                $vo['ruku'] = in_array($vo['number'],$stock_in)?1:0;
                $sor[$vo['number']]['child'][]=$vo;
            }
        }
        $user_model = new UserModel();
        foreach ($sor as $k=>$vo){
            if (isset($vo['child'])){
                $sorted = $user_model->array_orderby($vo['child'], 'timee', SORT_DESC);
                $sor[$k]['child'] = $sorted;
                $sor[$k]['timee'] = $sorted[0]['timee'];
            }else{
                $sor[$k]['child'] = [];
            }
        }
        $sorted = $user_model->array_orderby($sor, 'timee', SORT_DESC);
        $sor = $sorted;
        $this->assign("sor", $sor);
        return $this->fetch('config/captcha');
    }

    /**
     * api安全
     */
    public function api()
    {
        $api_model = new Api();
        if (request()->isAjax()) {
            $is_use      = input("is_use", 0);
            $public_key  = input("public_key", "");
            $private_key = input("private_key", "");
            $data        = array(
                "public_key"  => $public_key,
                "private_key" => $private_key,
            );
            $result      = $api_model->setApiConfig($data, $is_use);
            return $result;
        } else {
            $config_result = $api_model->getApiConfig();
            $config        = $config_result["data"];
            $this->assign("config", $config);
            return $this->fetch('config/api');
        }
    }

    public function generateRSA()
    {
        if (request()->isAjax()) {
            return RSA::getSecretKey();
        }
    }

    /**
     * 地图配置
     * @return mixed
     */
    public function map(){
        $config_model = new ConfigModel();
        if (request()->isAjax()) {
            $tencent_map_key = input("tencent_map_key", "");
            $result = $config_model->setMapConfig([
                'tencent_map_key' => $tencent_map_key
            ]);
            return $result;
        }
        $config = $config_model->getMapConfig();
        $this->assign('info', $config['data']['value']);
        return $this->fetch('config/map');
    }
}