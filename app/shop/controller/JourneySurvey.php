<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use think\facade\Db;
use app\shop\controller\BaseShop;
use app\model\member\Member as MemberModel;
use app\model\system\Menu;

/**
 * 问卷调查控制器
 */
class JourneySurvey extends BaseShop
{

    /**
     * 问卷调查管理页面
     * @return \think\response\View
     */
    public function index()
    {
        // 获取member_id参数
        $member_id = input('member_id', '');
        $this->assign('member_id', $member_id);
        return $this->fetch('journeysurvey/journey_survey');
    }

    /**
     * 问卷填写页面 (User Survey Filling Page)
     * 通过唯一token加载问卷模板结构和检查分配状态，并在服务器端渲染数据到视图（无需登录）
     *
     * @return \think\response\View
     */
    public function fill()
    {
        // 已移动到app\shop\controller\PublicController.php公共控制器中
        return null;
    }

    /**
     * 查看报告页面
     * @return \think\response\View
     */
    public function viewReport()
    {
        // 获取report_id参数，默认值为0
        $report_id = input('report_id', 0);
        
        if (request()->isAjax()) {
            // AJAX请求处理逻辑
            // 校验report_id是否为大于0的整数
            if (!is_numeric($report_id) || $report_id <= 0) {
                return json(['code' => 0, 'msg' => '无效的报告ID', 'data' => []]);
            }
            
            // 根据report_id查询报告记录
            $report = Db::name('user_reports')->where([['id', '=', $report_id], ['site_id', '=', $this->site_id]])->find();

            // 检查报告记录是否存在
            if (empty($report)) {
                return json(['code' => 0, 'msg' => '未找到报告', 'data' => []]);
            }
            
            // 根据优先级确定报告内容：用户编辑结果 > 系统二次总结 > 分析结果
            $report_content = '';
            if (!empty($report['user_edited_result'])) {
                $report_content = $report['user_edited_result'];
            } elseif (!empty($report['system_summary_result'])) {
                $report_content = $report['system_summary_result'];
            } elseif (!empty($report['analysis_result'])) {
                $report_content = $report['analysis_result'];
            }
            
            // 解析file_list字段
            $file_list_data = [];
            if (!empty($report['file_list'])) {
                $file_list_data = json_decode($report['file_list'], true);
                if (json_last_error() !== JSON_ERROR_NONE || !is_array($file_list_data)) {
                    $file_list_data = [];
                }
            }
            
            return json(['code' => 1, 'msg' => '获取成功', 'data' => [
                'report' => $report,
                'report_content' => $report_content,
                'file_list_data' => $file_list_data
            ]]);
        } else {
            // 非AJAX请求处理逻辑
            // 校验report_id是否为大于0的整数
            if (!is_numeric($report_id) || $report_id <= 0) {
                // 设置错误信息
                $this->assign('error_msg', '无效的报告ID');
                // 渲染并返回报告视图
                return $this->fetch('journeysurvey/journey_report_view');
            }
            
            // 根据report_id查询报告记录
            $report = Db::name('user_reports')->where([['id', '=', $report_id], ['site_id', '=', $this->site_id]])->find();

            // 检查报告记录是否存在
            if (empty($report)) {
                // 设置错误信息
                $this->assign('error_msg', '未找到报告');
                // 渲染并返回报告视图
                return $this->fetch('journeysurvey/journey_report_view');
            }
            
            // 根据优先级确定报告内容：用户编辑结果 > 系统二次总结 > 分析结果
            $report_content = '';
            if (!empty($report['user_edited_result'])) {
                $report_content = $report['user_edited_result'];
            } elseif (!empty($report['system_summary_result'])) {
                $report_content = $report['system_summary_result'];
            } elseif (!empty($report['analysis_result'])) {
                $report_content = $report['analysis_result'];
            }
            
            // 将确定的报告内容赋值给视图
            $this->assign('report_content', $report_content);
            
            // 解析file_list字段
            $file_list_data = [];
            if (!empty($report['file_list'])) {
                $file_list_data = json_decode($report['file_list'], true);
                if (json_last_error() !== JSON_ERROR_NONE || !is_array($file_list_data)) {
                    $file_list_data = [];
                }
            }
            
            // 将文件列表数据赋值给视图
            $this->assign('file_list_data', $file_list_data);
            
            // 将报告其他必要字段赋值给视图
            $this->assign('report_id', $report['id']);
            $this->assign('file_name', $report['file_name']);
            $this->assign('upload_time', $report['upload_time']);
            $this->assign('report_data', $report); // 可选：将完整报告数据赋值给视图
            $this->assign('error_msg', '');
            // 渲染并返回报告视图
            return $this->fetch('journeysurvey/journey_report_view');
        }
    }

    /**
     * 患者报告列表页面
     * 根据member_id显示该会员的所有报告列表页面
     * @return \think\response\View
     */
    public function reportList()
    {
        // 获取member_id参数
        $member_id = input('member_id', 0);

        // 初始化视图变量，处理可能的错误情况
        $this->assign('error_msg', '');
        $this->assign('member_id', $member_id); // 总是传递ID，即使无效或找不到会员
        $this->assign('member_name', '加载中...'); // 初始或默认名称


        // 验证member_id是否为大于0的整数
        if (!is_numeric($member_id) || $member_id <= 0) {
            // 无效ID，设置错误信息和默认名称
            $this->assign('error_msg', '无效的会员ID');
            $this->assign('member_name', '未知会员');
            // 直接渲染视图，前端会根据error_msg显示错误
            return $this->fetch('journeysurvey/report_list_for_member');
        }

        // 获取站点ID
        $site_id = $this->site_id; // 确保在BaseShop中已初始化并可通过$this->site_id访问

        // 查询会员信息，以获取会员名称用于页面标题显示
        // 假设会员表是 'member'，并且有 username 或 nickname 字段
        $memberInfo = Db::name('member')
            ->where([['member_id', '=', $member_id], ['site_id', '=', $site_id]])
            ->field('member_id, username, nickname') // 选择需要的字段
            ->find();

        // 检查会员是否存在
        if (empty($memberInfo)) {
            // 未找到会员，设置错误信息和默认名称
            $this->assign('error_msg', '未找到该会员信息');
            $this->assign('member_name', '未知会员');
            // 直接渲染视图
            return $this->fetch('journeysurvey/report_list_for_member');
        }

        // 将会员名称赋值给视图
        // 使用 ?? 运算符提供默认值，防止字段不存在
        $this->assign('member_name', $memberInfo['username'] ?? $memberInfo['nickname'] ?? '未知会员');

        // 渲染报告列表页面视图
        return $this->fetch('journeysurvey/report_list_for_member');
    }

    /**
     * 干预反馈收集页面
     * 获取 URL 参数中的 member_id 和 report_id（或 intervention_id），并分配到视图
     * @return \think\response\View
     */
    public function feedbackCollect()
    {
        $member_id = input('member_id', '');
        $report_id = input('report_id', '');
        $error_msg = '';
        try {
            if ($report_id === '') {
                $report_id = input('intervention_id', '');
            }
            $member_model = new MemberModel();
            $member_info  = $member_model->getMemberInfo([['member_id', '=', $member_id]]);
        } catch (\Exception $e) {
            $error_msg = $e->getMessage();
        } finally {
            $this->assign('member_name', $member_info['data']['username'] ?? 'null');
            $this->assign('member_id', $member_id);
            $this->assign('report_id', $report_id);
            $this->assign('error_msg', $error_msg);
            return $this->fetch('journeysurvey/feedback_collect');
        } 
    }

    /** 
     * 添加医生干预记录 
     * @return \think\response\Json 
     */ 
    public function addInterventionRecord() 
    { 
        // 确保只有 AJAX 请求才能访问此方法 
        if (request()->isAjax()) { 
            // 1. 获取输入参数 (默认POST请求) 
            $memberId = input('member_id', 0); // /d 表示转换为整数类型 
            $interventionDate = input('intervention_date', ''); 
            // 获取 intervention_details 参数
            $interventionDetails = input('intervention_details', ''); // 这将用于详细文本描述
            // 新增获取 intervention_type 参数
            $interventionType = input('intervention_type', '');

            // 2. 获取医生ID和站点ID (根据您的实际系统获取方式调整) 
            // 假设 user_id 可以从当前登录的用户信息中获取 
            $userId = $this->uid; 
            if (empty($userId)) { 
                return json(['code' => -1, 'msg' => '医生ID获取失败，请重新登录']); 
            } 
            // 假设 site_id 可以从当前站点配置或会话中获取 
            $siteId = $this->site_id ?? 0; 
            if (empty($siteId)) { 
                return json(['code' => -1, 'msg' => '站点ID获取失败']); 
            } 

            // 3. 数据验证 
            if (empty($memberId)) { 
                return json(['code' => -1, 'msg' => '患者ID不能为空']); 
            } 
            if (empty($interventionDate)) { 
                return json(['code' => -1, 'msg' => '干预日期不能为空']); 
            } 
            // 验证日期格式 (简单的YYYY-MM-DD 格式验证) 
            if (!preg_match("/^\\d{4}-\\d{2}-\\d{2}$/", $interventionDate)) { 
                return json(['code' => -1, 'msg' => '干预日期格式不正确，应为YYYY-MM-DD']); 
            } 
            // 干预详情允许为空 

            // 4. 构建插入数据 
            $data = [ 
                'member_id'          => $memberId, 
                'user_id'          => $userId, 
                'site_id'            => $siteId, 
                'intervention_date'  => $interventionDate, 
                'intervention_type'  => $interventionType, // 新增 intervention_type 字段
                'intervention_details' => $interventionDetails, // 保持此行，现在它将存储详细文本
                'is_deleted'         => 0 // 默认为未删除 
            ]; 

            // 5. 插入数据库 
            try { 
                $result = Db::name('doctor_intervention_records')->insert($data); 
                if ($result) { 
                    return json(['code' => 0, 'msg' => '干预记录添加成功']); 
                } else { 
                    return json(['code' => -1, 'msg' => '干预记录添加失败']); 
                } 
            } catch (\Exception $e) { 
                // 在开发环境中可以打印详细错误，生产环境建议记录到日志文件，只返回通用错误信息 
                trace('添加干预记录失败: ' . $e->getMessage(), 'error'); 
                return json(['code' => -1, 'msg' => '系统错误，请稍后再试']); 
            } 
        }
    }

    /**
     * 获取患者的干预记录列表
     * @return \think\response\Json
     */
    public function getInterventionRecords()
    {
        if(request()->isAjax()) {
            // 获取member_id参数并验证
            $member_id = input('member_id', 0);
            if (!is_numeric($member_id) || $member_id <= 0) {
                return json(['code' => 1, 'msg' => '无效的患者ID']);
            }
            
            try {
                // 获取当前登录的医生ID
                $userId = $this->uid;
                
                // 直接查询干预记录
                $records = Db::name('doctor_intervention_records')
                    ->where([
                        ['member_id', '=', $member_id],
                        ['user_id', '=', $userId],
                        ['site_id', '=', $this->site_id],
                        ['is_deleted', '=', 0]
                    ])
                    ->field('id, intervention_date, intervention_details, intervention_type, create_time') // 新增 intervention_type 字段
                    ->order('intervention_date desc, create_time desc')
                    ->select();
                
                // 格式化日期
                foreach ($records as &$record) {
                    if (isset($record['intervention_date'])) {
                        $record['intervention_date'] = date('Y-m-d', strtotime($record['intervention_date']));
                    }
                    $record['create_time'] = date('Y-m-d H:i', strtotime($record['create_time']));
                }
                unset($record);
                
                return json([
                    'code' => 0,
                    'msg'  => '获取干预记录成功',
                    'data' => $records
                ]);
                
            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg'  => '获取干预记录失败：' . $e->getMessage()
                ]);
            }
        }
        
        return json([
            'code' => 1,
            'msg'  => '非法请求'
        ]);
    }

    /**
     * 获取单条干预记录详情
     * @return \think\response\Json
     */
    public function getInterventionRecordDetail()
    {
        if (request()->isAjax()) {
            // 获取记录ID
            $id = input('id', 0);
            
            // 验证ID有效性
            if (!is_numeric($id) || $id <= 0) {
                return json(['code' => 1, 'msg' => '无效的记录ID']);
            }
            
            try {
                // 获取当前登录医生ID和站点ID
                $userId = $this->uid;
                $siteId = $this->site_id;
                
                // 查询单条记录
                $record = Db::name('doctor_intervention_records')
                    ->where([
                        ['id', '=', $id],
                        ['user_id', '=', $userId],
                        ['site_id', '=', $siteId],
                        ['is_deleted', '=', 0]
                    ])
                    ->field('id, intervention_date, intervention_details, intervention_type, create_time') // 新增 intervention_type 字段
                    ->find();
                
                if ($record) {
                    // 格式化日期
                    if (isset($record['intervention_date'])) {
                        $record['intervention_date'] = date('Y-m-d', strtotime($record['intervention_date']));
                    }
                    if (isset($record['create_time'])) {
                        $record['create_time'] = date('Y-m-d H:i', strtotime($record['create_time']));
                    }
                    
                    return json([
                        'code' => 0,
                        'msg' => '获取成功',
                        'data' => $record
                    ]);
                } else {
                    return json(['code' => 1, 'msg' => '记录不存在或已被删除']);
                }
                
            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg' => '获取记录详情失败：' . $e->getMessage()
                ]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 软删除干预记录
     * @return \think\response\Json
     */
    public function deleteInterventionRecord()
    {
        if (request()->isAjax()) {
            // 获取记录ID
            $id = input('id', 0);
            
            // 验证ID有效性
            if (!is_numeric($id) || $id <= 0) {
                return json(['code' => 1, 'msg' => '无效的记录ID']);
            }
            
            try {
                // 获取当前登录医生ID和站点ID
                $userId = $this->uid;
                $siteId = $this->site_id;
                
                // 执行软删除操作
                $result = Db::name('doctor_intervention_records')
                    ->where([
                        ['id', '=', $id],
                        ['user_id', '=', $userId],
                        ['site_id', '=', $siteId],
                        ['is_deleted', '=', 0]
                    ])
                    ->update(['is_deleted' => 1]);
                
                if ($result) {
                    return json(['code' => 0, 'msg' => '删除成功']);
                } else {
                    return json(['code' => 1, 'msg' => '记录不存在或已被删除']);
                }
                
            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg' => '删除失败：' . $e->getMessage()
                ]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 编辑干预记录
     * @return \think\response\Json
     */
    public function editInterventionRecord()
    {
        if (request()->isAjax() && request()->isPost()) {
            // 获取输入参数
            $id = input('id', 0);
            $memberId = input('member_id', 0);
            $interventionDate = input('intervention_date', '');
            $interventionDetails = input('intervention_details', '');
            $interventionType = input('intervention_type', ''); // 新增获取 intervention_type 参数
            
            // 验证参数
            if (!is_numeric($id) || $id <= 0) {
                return json(['code' => 1, 'msg' => '无效的记录ID']);
            }
            if (!is_numeric($memberId) || $memberId <= 0) {
                return json(['code' => 1, 'msg' => '无效的患者ID']);
            }
            if (empty($interventionDate) || !preg_match("/^\\d{4}-\\d{2}-\\d{2}$/", $interventionDate)) {
                return json(['code' => 1, 'msg' => '干预日期不能为空或格式不正确']);
            }
            
            try {
                // 获取当前登录医生ID和站点ID
                $userId = $this->uid;
                $siteId = $this->site_id;
                
                // 验证记录是否存在且属于当前用户
                $record = Db::name('doctor_intervention_records')
                    ->where([
                        ['id', '=', $id],
                        ['member_id', '=', $memberId],
                        ['user_id', '=', $userId],
                        ['site_id', '=', $siteId],
                        ['is_deleted', '=', 0]
                    ])
                    ->find();
                
                if (empty($record)) {
                    return json(['code' => 1, 'msg' => '无权编辑该记录或记录不存在']);
                }
                
                // 更新数据
                $data = [
                    'intervention_date' => $interventionDate,
                    'intervention_type' => $interventionType, // 新增 intervention_type 字段
                    'intervention_details' => $interventionDetails, // 保持此行，现在它将存储详细文本
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                $result = Db::name('doctor_intervention_records')
                    ->where([
                        ['id', '=', $id],
                        ['user_id', '=', $userId],
                        ['site_id', '=', $siteId],
                        ['is_deleted', '=', 0]
                    ])
                    ->update($data);
                
                if ($result) {
                    return json(['code' => 0, 'msg' => '干预记录更新成功']);
                } else {
                    return json(['code' => 1, 'msg' => '干预记录更新失败，数据无变化或已删除']);
                }
                
            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg' => '更新记录时发生错误：' . $e->getMessage()
                ]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }

    


    /**
     * 获取会员信息
     */
    public function getMemberInfo()
    {
        if(request()->isAjax()) {
            $member_id = input('member_id', 0);
            $member_model = new MemberModel();
            $res = $member_model->getMemberInfo([['member_id', '=', $member_id]]);
            unset($res['data']['password']);
            if ($res['code'] === 0) {
                $res['code'] = 1;
            }
            return $res;
        }
    }

    /**
     * 获取问卷模板列表
     */
    public function getSurveyTemplates()
    {
        try {
            // 获取当前登录用户的ID和站点ID 
            $current_user_id = $this->uid;
            $current_site_id = $this->site_id;

            // 管理员用户ID配置 (请根据实际情况修改)
            $admin_user_id = 1;

            // 构建数据库查询条件数组
            $where = [
                ['site_id', '=', $current_site_id]
            ];

            // 判断当前用户是否是管理员
            $is_admin = ($current_user_id == $admin_user_id);

            if (!$is_admin) {
                // 非管理员用户可以看到自己的模板和管理员创建的模板
                $where[] = function(\think\db\Query $query) use ($current_user_id, $admin_user_id) {
                    $query->where('user_id', '=', $current_user_id)
                          ->whereOr('user_id', '=', $admin_user_id);
                };
            } else {
                // 管理员只能看到自己创建的模板
                $where[] = ['user_id', '=', $current_user_id];
            }

            $result = model('survey_templates')->getList(
                $where,
                'id,title,description,status,create_time,user_id',
                'create_time desc'
            );
            
            if (is_array($result)) {
                return json([
                    'code' => 1,
                    'msg' => empty($result) ? '暂无问卷模板' : '获取成功',
                    'data' => $result
                ]);
            }
            
            return json(['code' => 0, 'msg' => '查询失败', 'data' => []]);
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 保存问卷模板
     */
    public function saveSurveyTemplate()
    {
        if (request()->isAjax()) {
            try {
                // 获取数据
                $id = input('id', 0);
                $title = input('title', '');
                $description = input('description', '');
                $items = input('items', []);
                
                // 开启事务
                model()->startTrans();
                
                // 处理模板主表数据
                $templateData = [
                    'title' => $title,
                    'description' => $description,
                    'site_id' => $this->site_id,
                    'user_id' => $this->uid,
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                if (empty($id)) {
                    // 新建模板
                    $templateData['status'] = 0;
                    $templateData['create_time'] = date('Y-m-d H:i:s');
                    $templateId = model('survey_templates')->add($templateData);
                } else {
                    // 编辑模板
                    $templateId = $id;
                    model('survey_templates')->update($templateData, [['id', '=', $templateId]]);
                    
                    // 删除原有题目
                    model('survey_template_items')->delete([['template_id', '=', $templateId]]);
                }
                
                // 处理题目数据
                foreach ($items as $index => $item) {
                    $itemData = [
                        'template_id' => $templateId,
                        'item_type' => $item['itemType'],
                        'item_order' => $index,
                    ];
                    
                    if ($item['itemType'] == 'section') {
                        $itemData['title'] = $item['title'];
                        $itemData['settings_json'] = '{}';
                    } else {
                        $itemData['question_text'] = $item['text'];
                        $itemData['question_type'] = $item['questionType'];
                        $itemData['is_required'] = $item['is_required'] ?? 0;
                        
                        // 处理题目设置，包括推荐商品ID
                        $settings = $item['settings'] ?? [];
                        if (isset($settings['recommended_product_ids'])) {
                            // 基本校验推荐商品ID数组
                            if (!is_array($settings['recommended_product_ids'])) {
                                model()->rollback();
                                return json(['code' => 0, 'msg' => '推荐商品ID格式无效', 'data' => []]);
                            }
                            
                            foreach ($settings['recommended_product_ids'] as $productId) {
                                if (!is_numeric($productId) || $productId < 0) {
                                    model()->rollback();
                                    return json(['code' => 0, 'msg' => '推荐商品ID必须是非负整数', 'data' => []]);
                                }
                            }
                        }
                        
                        $itemData['settings_json'] = json_encode($settings);
                    }
                    
                    model('survey_template_items')->add($itemData);
                }
                
                // 提交事务
                model()->commit();
                
                return json(['code' => 1, 'msg' => '保存成功', 'data' => ['id' => $templateId]]);
            } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
                // 回滚事务
                model()->rollback();
                return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage(), 'data' => []]);
            }
        }
    }

    /**
     * 发布问卷模板
     */
    public function publishSurveyTemplate()
    {
        try {
            // 获取模板ID
            $templateId = input('id', 0);
            
            // 获取患者ID列表
            $patientIds = input('patient_ids', []);
            
            // 数据校验
            if (empty($templateId) || !is_numeric($templateId)) {
                return json(['code' => 0, 'msg' => '模板ID无效', 'data' => []]);
            }
            
            if (empty($patientIds) || !is_array($patientIds)) {
                return json(['code' => 0, 'msg' => '患者ID列表不能为空', 'data' => []]);
            }
            
            // 验证模板状态
            $templateInfo = model('survey_templates')->getInfo(
                [['id', '=', $templateId], ['site_id', '=', $this->site_id]],
                'id,status'
            );
            
            if (empty($templateInfo)) {
                return json(['code' => 0, 'msg' => '问卷模板不存在', 'data' => []]);
            }

            // 如果模板状态为未使用(0),则更新为已使用(1)
            if ($templateInfo['status'] == 0) {
                model('survey_templates')->update(
                    ['status' => 1],
                    [['id', '=', $templateId]]
                );
            }
            
            // 开启事务
            model()->startTrans();
            
            $successCount = 0;
            $assignedSurveyUrl = '';
            foreach ($patientIds as $patientId) {
                // 生成唯一token
                $token = md5(uniqid($patientId . $templateId, true) . mt_rand(1000, 9999));
                
                $assignmentData = [
                    'template_id' => $templateId,
                    'assigned_user_id' => $patientId,
                    'assigned_by_user_id' => $this->uid,
                    'assign_time' => date('Y-m-d H:i:s'),
                    'status' => 0,
                    'unique_link_token' => $token,
                    'site_id' => $this->site_id
                ];
                
                $result = model('survey_assignments')->add($assignmentData);
                if (!$result) {
                    throw new \Exception('分配记录创建失败');
                }
                $successCount++;
                
                // 构建问卷填写链接
                $baseUrl = env('APP_URL');
                $baseUrl = rtrim($baseUrl, '/');
                $assignedSurveyUrl = $baseUrl . '/shop/PublicController/fill?token=' . $token;
            }
            
            // 提交事务
            model()->commit();
            
            return json(['code' => 1, 'msg' => '问卷分配成功', 'data' => [
                    'success_count' => $successCount,
                    'assignment_url' => $assignedSurveyUrl
                ]]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            // 回滚事务
            model()->rollback();
            return json(['code' => 0, 'msg' => '分配失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 获取会员最新报告总结
     * @return \think\response\Json
     */
    public function getLatestReportSummary()
    {
        // 获取member_id参数并验证
        $member_id = input('member_id', 0);
        if (!is_numeric($member_id) || $member_id <= 0) {
            return json(['code' => 0, 'msg' => '无效的会员ID', 'data' => []]);
        }

        // 查询会员最新报告(标记为is_latest=1)
        $report = Db::name('user_reports')
            ->where([
                ['member_id', '=', $member_id],
                ['site_id', '=', $this->site_id],
                ['is_latest', '=', 1]
            ])
            ->order('upload_time desc')
            ->find();
            
        // 如果未找到标记为最新的报告，则查询上传时间最晚的报告作为回退
        if (empty($report)) {
            $report = Db::name('user_reports')
                ->where([
                    ['member_id', '=', $member_id],
                    ['site_id', '=', $this->site_id]
                ])
                ->order('upload_time desc')
                ->find();
        }

        // 检查报告是否存在
        if (empty($report)) {
            return json(['code' => 0, 'msg' => '未找到报告', 'data' => []]);
        }

        // 确定返回的总结内容
        $summary = '';
        if (!empty($report['user_edited_result'])) {
            $summary = $report['user_edited_result'];
        } elseif (!empty($report['system_summary_result'])) {
            $summary = $report['system_summary_result'];
        }

        // 返回成功响应
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'summary' => $summary,
                'report_id' => $report['id']
            ]
        ]);
    }

    

    /**
     * 获取会员所有已分析完成的报告时间轴列表
     * 用于前端AJAX请求，根据member_id返回报告列表及摘要片段
     * @return \think\response\Json
     */
    public function getReportTimelineForMember()
    {
        // 获取member_id参数并验证
        $member_id = input('member_id', 0);
        if (!is_numeric($member_id) || $member_id <= 0) {
            return json(['code' => 0, 'msg' => '无效的会员ID', 'data' => []]);
        }

        // 查询所有已分析完成的报告，按分析时间倒序排列
        $reports = Db::name('user_reports')
            ->where([
                ['member_id', '=', $member_id],
                ['site_id', '=', $this->site_id],
                ['analysis_status', '=', 2], // 2=分析完成
                ['analysis_time', '<>', '']
            ])
            ->whereNotNull('analysis_time')
            ->order('analysis_time desc')
            ->select();

        $timelineReports = [];
        foreach ($reports as $report) {
            // 优先级：用户编辑结果 > 系统总结 > 分析结果
            $originalSummary = '';
            if (!empty($report['user_edited_result'])) {
                $originalSummary = $report['user_edited_result'];
            } elseif (!empty($report['system_summary_result'])) {
                $originalSummary = $report['system_summary_result'];
            } elseif (!empty($report['analysis_result'])) {
                $originalSummary = $report['analysis_result'];
            }
            // 从医疗建议部分开始截取200字符作为摘要
            if(strpos($originalSummary, '### 医疗建议') !== false) {
                $medicalAdvice = substr($originalSummary, strpos($originalSummary, '### 医疗建议'));
                $summarySnippet = preg_replace('/[#*`_~]/', '', mb_substr($medicalAdvice, 0, 200));
            } else {
                // 如果找不到医疗建议部分，则从开头截取
                $summarySnippet = preg_replace('/[#*`_~]/', '', mb_substr($originalSummary, 0, 200));
            }
            $timelineReports[] = [
                'id' => $report['id'],
                'file_name' => $report['file_name'],
                'analysis_time' => $report['analysis_time'],
                'summary_snippet' => $summarySnippet
            ];
        }

        if (empty($timelineReports)) {
            return json(['code' => 0, 'msg' => '未找到已分析的报告', 'data' => []]);
        }
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $timelineReports]);
    }

    /**
     * 删除问卷模板
     */
    public function deleteSurveyTemplate()
    {
        try {
            // 获取模板ID
            $templateId = input('id', 0);
            
            // 验证模板ID
            if (empty($templateId) || !is_numeric($templateId)) {
                return json(['code' => 0, 'msg' => '模板ID无效', 'data' => []]);
            }
            
            // 验证模板是否存在且属于当前站点
            $templateInfo = model('survey_templates')->getInfo(
                [['id', '=', $templateId], ['site_id', '=', $this->site_id]],
                'id'
            );
            
            if (empty($templateInfo)) {
                return json(['code' => 0, 'msg' => '问卷模板不存在或无权操作', 'data' => []]);
            }
            
            // 检查是否有分配记录
            $assignmentCount = model('survey_assignments')->getCount(
                [['template_id', '=', $templateId], ['site_id', '=', $this->site_id]]
            );
            
            if ($assignmentCount > 0) {
                return json(['code' => 0, 'msg' => '问卷模板已被分配，无法直接删除', 'data' => []]);
            }
            
            // 开启事务
            model()->startTrans();
            
            try {
                // 删除关联项目
                model('survey_template_items')->delete([['template_id', '=', $templateId]]);
                
                // 删除模板主记录
                model('survey_templates')->delete([['id', '=', $templateId]]);
                
                // 提交事务
                model()->commit();
                
                return json(['code' => 1, 'msg' => '删除成功', 'data' => []]);
            } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
                // 回滚事务
                model()->rollback();
                return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage(), 'data' => []]);
            }
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 根据分配token获取问卷详情
     */
    public function getSurveyForAssignment()
    {
        try {
            // 获取token参数
            $token = input('token', '');
            
            // 验证token有效性
            if (empty($token)) {
                return json(['code' => 0, 'msg' => '链接无效', 'data' => []]);
            }
            
            // 查询分配记录
            $assignment = model('survey_assignments')->getInfo(
                [['unique_link_token', '=', $token], ['site_id', '=', $this->site_id]]
            );
            
            if (empty($assignment)) {
                return json(['code' => 0, 'msg' => '问卷链接不存在', 'data' => []]);
            }
            
            // 检查分配状态
            if ($assignment['status'] > 0) {
                return json(['code' => 0, 'msg' => '问卷已填写', 'data' => []]);
            }
            
            // 获取模板信息
            $template = model('survey_templates')->getInfo(
                [['id', '=', $assignment['template_id']], ['site_id', '=', $this->site_id]],
                'id,title,description'
            );
            
            if (empty($template)) {
                return json(['code' => 0, 'msg' => '关联的问卷模板不存在', 'data' => []]);
            }
            
            // 获取模板题目
            $items = model('survey_template_items')->getList(
                [['template_id', '=', $template['id']]],
                '*',
                'item_order asc'
            );
            
            $processedItems = [];
            foreach ($items as $item) {
                $itemData = [
                    'itemType' => $item['item_type'],
                    'item_order' => $item['item_order']
                ];
                
                if ($item['item_type'] == 'section') {
                    $itemData['title'] = $item['title'];
                } else {
                    $itemData['questionType'] = $item['question_type'];
                    $itemData['text'] = $item['question_text'];
                    $itemData['is_required'] = $item['is_required'];
                    $itemData['settings'] = json_decode($item['settings_json'], true);
                }
                
                $processedItems[] = $itemData;
            }
            
            // 返回整合数据
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'template' => $template,
                    'items' => $processedItems,
                    'assignment_id' => $assignment['id']
                ]
            ]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 获取患者问卷分配记录
     */
    public function listPatientAssignments()
    {
        try {
            // 获取患者ID
            $patientUserId = input('patient_user_id', 0);
            
            // 验证患者ID
            if (empty($patientUserId) || !is_numeric($patientUserId)) {
                return json(['code' => 0, 'msg' => '患者ID无效', 'data' => []]);
            }
            
            // 查询分配记录
            $list = model('survey_assignments')->getList(
                [['assigned_user_id', '=', $patientUserId], ['site_id', '=', $this->site_id]],
                'a.id as assignment_id, a.assign_time, a.status, a.completion_time, a.response_id, t.title as template_title',
                'a.assign_time desc',
                'a',
                [['survey_templates t', 'a.template_id = t.id', 'left']]
            );
            
            return json([
                'code' => 1,
                'msg' => empty($list) ? '暂无分配记录' : '获取成功',
                'data' => $list
            ]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 获取问卷分配记录列表(所有)
     */
    public function listAssignments()
    {
        try {
            // 获取分页参数
            $page = input('page', 1);
            $limit = input('limit', 10);
            
            // 初始化查询条件
            $conditions = [['sa.site_id', '=', $this->site_id]];
            
            // 处理过滤条件
            $templateId = input('template_id', 0);
            if (!empty($templateId)) {
                $conditions[] = ['sa.template_id', '=', $templateId];
            }
            
            $status = input('status', '');
            if ($status !== '' && in_array((int)$status, [0, 1, 2])) {
                $conditions[] = ['sa.status', '=', (int)$status];
            }
            
            // 构建查询
            $model = model('survey_assignments')->alias('sa')
                ->join('survey_templates st', 'sa.template_id = st.id')
                ->join('member m', 'sa.assigned_user_id = m.member_id')
                ->field('sa.*, st.title as template_title, m.username as assigned_user_name')
                ->where($conditions);
            
            // 获取总数和分页数据
            $count = $model->count();
            $list = $model->page($page, $limit)
                ->order('sa.assign_time DESC')
                ->select()
                ->toArray();
            
            // 转换状态文本
            foreach ($list as &$item) {
                $item['status_text'] = $this->getAssignmentStatusText($item['status']);
                $baseUrl = env('APP_URL');
                $baseUrl = rtrim($baseUrl, '/');
                $item['assignment_url'] = $baseUrl . '/shop/PublicController/fill?token=' . $item['unique_link_token'];
            }
            
            return json([
                'code' => 1, 
                'msg' => '获取分配列表成功', 
                'data' => ['list' => $list, 'count' => $count]
            ]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 状态码转文本
     */
    private function getAssignmentStatusText($status)
    {
        $statusTexts = [
            0 => '待填写',
            1 => '已填写',
            2 => '已完成',
            4 => '已取消'
        ];
        return $statusTexts[$status] ?? '未知状态';
    }
    
    /**
     * 取消问卷分配
     */
    public function cancelAssignment()
    {
        try {
            // 获取分配记录ID
            $assignmentId = input('assignment_id', 0);
            
            // 验证分配记录ID
            if (empty($assignmentId) || !is_numeric($assignmentId) || $assignmentId <= 0) {
                return json(['code' => 0, 'msg' => '分配记录ID无效', 'data' => []]);
            }
            
            // 查询分配记录
            $assignment = Db::name('survey_assignments')->where([
                ['id', '=', $assignmentId],
                ['site_id', '=', $this->site_id]
            ])->find();
            
            if (empty($assignment)) {
                return json(['code' => 0, 'msg' => '分配记录不存在', 'data' => []]);
            }
            
            // 检查状态是否允许取消
            if ($assignment['status'] > 1) {
                return json(['code' => 0, 'msg' => '该问卷状态不允许取消', 'data' => []]);
            }
            
            // 更新状态为已取消(4)
            $result = Db::name('survey_assignments')->where('id', $assignmentId)->update(['status' => 4]);
            
            if ($result === false) {
                throw new \Exception('更新分配记录状态失败');
            }
            
            return json(['code' => 1, 'msg' => '取消分配成功', 'data' => []]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '取消分配失败：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 获取患者问卷分配记录列表
     */
    public function listAssignmentsForPatient()
    {
        try {
            $memberId = input('member_id', 0);
            
            if (empty($memberId)) {
                return json(['code' => 0, 'msg' => '患者ID无效', 'data' => []]);
            }
            
            $page = input('page', 1);
            $limit = input('limit', 10);
            
            $conditions = [
                ['ns_survey_assignments.site_id', '=', $this->site_id],
                ['ns_survey_assignments.assigned_user_id', '=', $memberId],
                ['ns_survey_assignments.assigned_by_user_id', '=', $this->uid],
            ];
            
            $status = input('status', '');
            if ($status !== '' && in_array((int)$status, [0, 1, 2])) {
                $conditions[] = ['ns_survey_assignments.status', '=', (int)$status];
            }
            
            $baseQuery = Db::name('survey_assignments')->where($conditions)
                ->join('survey_templates', 'ns_survey_assignments.template_id = survey_templates.id', 'LEFT')
                ->join('member', 'ns_survey_assignments.assigned_user_id = member.member_id', 'LEFT');
            
            $countQuery = clone $baseQuery;
            $count = $countQuery->count();
            
            $list = $baseQuery->field('ns_survey_assignments.*, survey_templates.title as template_title, member.username as assigned_user_name')
                ->page($page, $limit)
                ->order('ns_survey_assignments.assign_time DESC')
                ->select()
                ->toArray();
            
            foreach ($list as &$item) {
                $item['status_text'] = $this->getAssignmentStatusText($item['status']);
                $baseUrl = env('APP_URL');
                $baseUrl = rtrim($baseUrl, '/');
                $item['assignment_url'] = $baseUrl . '/shop/PublicController/fill?token=' . $item['unique_link_token'];
            }
            unset($item);
            
            return json(['code' => 1, 'msg' => '获取患者分配列表成功', 'data' => ['list' => $list, 'count' => $count]]);
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 查看问卷答案 (View Survey Responses)
     * 根据问卷分配记录ID获取问卷模板结构和患者提交的答案数据
     *
     * @return \think\response\Json
     */
    public function viewAssignmentResponses()
    {
        try {
            // 1. 获取分配记录ID参数 (前端表格传递的是分配ID)
            $assignmentId = input('assignment_id', 0);

            // 2. 验证分配记录ID有效性
            if (empty($assignmentId) || !is_numeric($assignmentId) || $assignmentId <= 0) {
                return json(['code' => 0, 'msg' => '分配记录ID无效', 'data' => []]);
            }

            // 3. 查询分配记录 (ns_survey_assignments)
            // 确保表名正确 'ns_survey_assignments'
            $assignment = Db::name('survey_assignments')->where([
                ['id', '=', $assignmentId],
                ['site_id', '=', $this->site_id] // 按站点ID过滤
            ])->find();

            // 4. 检查记录是否存在
            if (empty($assignment)) {
                return json(['code' => 0, 'msg' => '分配记录不存在', 'data' => []]);
            }

            // 4.1 检查状态是否是已完成(2)
            // 根据ns_survey_assignments表结构，状态 2 是已完成
            if ($assignment['status'] != 2) {
                 // 如果不是已完成状态，检查是否有关联的response_id，如果没有，则无法查看答案
                 // 如果状态不是2但response_id存在（例如状态3已过期但之前填写了），根据需求可以决定是否允许查看
                 // 当前逻辑严格要求状态为2
                 return json(['code' => 0, 'msg' => '问卷尚未完成，无法查看答案', 'data' => []]);
            }

            // 5. 获取模板ID和答案ID (从分配记录中获取)
            $templateId = $assignment['template_id'];
            $responseId = $assignment['response_id'];

            // 5.1 检查response_id有效性 (再次检查，理论上状态2应该保证有response_id)
             if (empty($responseId) || $responseId <= 0) {
                 return json(['code' => 0, 'msg' => '该分配记录没有关联的答案ID', 'data' => []]);
             }

            // --- 开始获取模板结构和答案数据 ---

            // 6. 获取问卷模板基本信息 (ns_survey_templates)
            // 确保表名正确 'ns_survey_templates'
            $template = Db::name('survey_templates')->where([['id', '=', $templateId]])->find();
             if (empty($template)) {
                 // 理论上在分配时已经验证过模板，但这里为了健壮性再检查一次
                 return json(['code' => 0, 'msg' => '问卷模板不存在或已删除', 'data' => []]);
             }

            // 7. 获取问卷模板项目（题目和章节） (ns_survey_template_items)
            // 确保表名正确 'ns_survey_template_items'
            // 获取所有必要字段，特别是 settings_json 用于题目的选项/设置
            $items = Db::name('survey_template_items')
                ->where([['template_id', '=', $templateId]])
                ->order('item_order ASC') // 按顺序排序
                ->select()
                ->toArray(); // 转换为数组

            // 可以选择在这里遍历 $items，解析 settings_json，或者在前端解析

            // 8. 获取患者响应记录 (ns_survey_user_responses)
            // 确保表名正确 'ns_survey_user_responses'
            $responseRecord = Db::name('survey_user_responses')->where([['id', '=', $responseId]])->find();
            if (empty($responseRecord)) {
                 // 理论上 response_id 已经验证过，但这里为了健壮性再检查一次
                 return json(['code' => 0, 'msg' => '未找到对应的问卷答案记录', 'data' => []]);
             }

            // 9. 解析JSON答案数据 (response_data_json)
            $responseData = json_decode($responseRecord['response_data_json'], true); // true 表示解码为关联数组
            if (json_last_error() !== JSON_ERROR_NONE) {
                // JSON 解析失败处理
                return json(['code' => 0, 'msg' => '问卷答案数据格式错误', 'data' => []]);
            }
            // 将解析后的答案数据添加到响应记录中，方便后续组织
            // 注意：这里的 $responseData 应该是 问题ID => 答案 的结构，以便前端匹配
            $responseRecord['answers'] = $responseData;


            // --- 组织最终返回数据结构 ---
            // 10. 组织数据结构
            $returnData = [
                // 可以选择包含 assignment 记录信息，如果前端需要的话
                // 'assignment' => $assignment,
                'template' => $template,     // 包含基本信息的模板
                'items' => $items,           // 模板项目 (题目和章节)，前端将根据这个和 answers 渲染
                'response' => $responseRecord // 包含响应基本信息和解析后答案 (responseRecord['answers'])
            ];

            // 11. 返回成功响应
            return json(['code' => 1, 'msg' => '获取问卷答案成功', 'data' => $returnData]);

        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            // 12. 处理异常，返回失败响应
            return json(['code' => 0, 'msg' => '获取问卷答案失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    
    /**
     * 提交问卷答案
     */
    public function submitSurveyResponse()
    {
        try {
            // 获取token和答案数据
            $token = input('token', '');
            $responseData = input('response_data', []);
            
            // 数据校验
            if (empty($token)) {
                return json(['code' => 0, 'msg' => '提交token无效', 'data' => []]);
            }
            
            if (empty($responseData) || !is_array($responseData)) {
                return json(['code' => 0, 'msg' => '提交数据无效', 'data' => []]);
            }
            
            // 开启事务
            model()->startTrans();
            
            // 查询分配记录
            $assignment = model('survey_assignments')->getInfo(
                [['unique_link_token', '=', $token], ['site_id', '=', $this->site_id]]
            );
            
            if (empty($assignment)) {
                model()->rollback();
                return json(['code' => 0, 'msg' => '分配记录不存在', 'data' => []]);
            }
            
            // 检查分配状态
            if ($assignment['status'] >= 2) {
                model()->rollback();
                return json(['code' => 0, 'msg' => '问卷已提交或已失效', 'data' => []]);
            }
            
            // 插入响应记录
            $responseData = [
                'assignment_id' => $assignment['id'],
                'submit_time' => date('Y-m-d H:i:s'),
                'response_data_json' => json_encode($responseData)
            ];
            
            $responseId = model('survey_user_responses')->add($responseData);
            if (!$responseId) {
                model()->rollback();
                return json(['code' => 0, 'msg' => '问卷提交失败', 'data' => []]);
            }
            
            // 更新分配状态
            $updateData = [
                'status' => 2,
                'completion_time' => date('Y-m-d H:i:s'),
                'response_id' => $responseId
            ];
            
            $updateResult = model('survey_assignments')->update($updateData, [['id', '=', $assignment['id']]]);
            if (!$updateResult) {
                model()->rollback();
                return json(['code' => 0, 'msg' => '问卷状态更新失败', 'data' => []]);
            }
            
            // 提交事务
            model()->commit();
            
            return json(['code' => 1, 'msg' => '问卷提交成功', 'data' => []]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            // 回滚事务
            model()->rollback();
            return json(['code' => 0, 'msg' => '提交失败：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 获取问卷模板详情
     */
    public function getSurveyTemplateDetails()
    {
        try {
            $templateId = input('template_id', 0);
            // 验证模板ID
            if (empty($templateId) || !is_numeric($templateId)) {
                return json(['code' => 0, 'msg' => '模板ID无效', 'data' => []]);
            }
            
            // 查询模板基本信息
            $templateInfo = model('survey_templates')->getInfo(
                [['id', '=', $templateId], ['site_id', '=', $this->site_id]],
                'id,title,description,status,user_id,site_id'
            );
            
            if (empty($templateInfo)) {
                return json(['code' => 0, 'msg' => '问卷模板不存在或无权访问', 'data' => []]);
            }
            
            // 查询模板项目
            $items = model('survey_template_items')->getList(
                [['template_id', '=', $templateId]],
                '*',
                'item_order asc'
            );
            
            $processedItems = [];
            foreach ($items as $item) {
                $itemData = [
                    'itemType' => $item['item_type'],
                    'item_order' => $item['item_order']
                ];
                
                if ($item['item_type'] == 'section') {
                    $itemData['title'] = $item['title'];
                } else {
                    $itemData['questionType'] = $item['question_type'];
                    $itemData['text'] = $item['question_text'];
                    $itemData['is_required'] = $item['is_required'];
                    $itemData['settings'] = json_decode($item['settings_json'], true) ?: [];
                }
                
                $processedItems[] = $itemData;
            }
            
            $result = [
                'id' => $templateInfo['id'],
                'title' => $templateInfo['title'],
                'description' => $templateInfo['description'],
                'items' => $processedItems
            ];
            
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $result]);
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }
    
    /**
     * 查看问卷响应详情
     */
    public function viewResponseDetails()
    {
        try {
            // 获取响应ID
            $responseId = input('response_id', 0);
            
            // 校验响应ID
            if (empty($responseId) || !is_numeric($responseId)) {
                return json(['code' => 0, 'msg' => '响应ID无效', 'data' => []]);
            }
            
            // 查询响应记录
            $response = model('ns_survey_user_responses')->getInfo(
                [['id', '=', $responseId]],
                'id,assignment_id,response_data_json'
            );
            
            if (empty($response)) {
                return json(['code' => 0, 'msg' => '问卷响应记录不存在', 'data' => []]);
            }
            
            // 安全检查：验证分配记录
            $assignment = model('survey_assignments')->getInfo(
                [['id', '=', $response['assignment_id']], ['site_id', '=', $this->site_id]],
                'id,template_id'
            );
            
            if (empty($assignment)) {
                return json(['code' => 0, 'msg' => '无权查看此响应', 'data' => []]);
            }
            
            // 获取模板信息
            $template = model('survey_templates')->getInfo(
                [['id', '=', $assignment['template_id']], ['site_id', '=', $this->site_id]],
                'id,title,description'
            );
            
            // 获取模板题目列表
            $items = model('survey_template_items')->getList(
                [['template_id', '=', $assignment['template_id']]],
                'id,item_type,title,question_text,question_type,is_required,settings_json,item_order',
                'item_order asc'
            );
            
            // 处理题目数据
            $processedItems = [];
            foreach ($items as $item) {
                $itemData = [
                    'id' => $item['id'],
                    'type' => $item['item_type'],
                    'order' => $item['item_order']
                ];
                
                if ($item['item_type'] == 'section') {
                    $itemData['title'] = $item['title'];
                    $itemData['settings'] = json_decode($item['settings_json'], true);
                } else {
                    $itemData['questionText'] = $item['question_text'];
                    $itemData['questionType'] = $item['question_type'];
                    $itemData['isRequired'] = $item['is_required'];
                    $itemData['settings'] = json_decode($item['settings_json'], true);
                }
                
                $processedItems[] = $itemData;
            }
            
            // 解析响应数据
            $responseData = json_decode($response['response_data_json'], true);
            
            // 收集推荐商品ID
            $recommendedProductIds = [];
            $productPriorityMap = [];
            
            // 遍历题目处理推荐商品
            foreach ($processedItems as $item) {
                if ($item['itemType'] == 'question' && isset($responseData[$item['id']])) {
                    $answer = $responseData[$item['id']];
                    $settings = $item['settings'];
                    
                    // 根据题目类型处理
                    switch ($item['questionType']) {
                        case 'single-choice':
                            // 单选题目匹配选项
                            foreach ($settings['options'] as $option) {
                                if ($option['value'] == $answer || $option['text'] == $answer) {
                                    if (!empty($option['recommended_product_ids'])) {
                                        foreach ($option['recommended_product_ids'] as $index => $productId) {
                                            if (!in_array($productId, $recommendedProductIds)) {
                                                $recommendedProductIds[] = $productId;
                                                $productPriorityMap[$productId] = count($recommendedProductIds);
                                            }
                                        }
                                    }
                                    break;
                                }
                            }
                            break;
                        case 'scoring':
                            // 评分题目匹配分值范围或具体值
                            foreach ($settings['scoreRanges'] as $scoreEntry) {
                                if (isset($scoreEntry['min']) && isset($scoreEntry['max']) && 
                                    $answer >= $scoreEntry['min'] && $answer <= $scoreEntry['max']) {
                                    if (!empty($scoreEntry['recommended_product_ids']) && is_array($scoreEntry['recommended_product_ids'])) {
                                        foreach ($scoreEntry['recommended_product_ids'] as $productId) {
                                            if (!in_array($productId, $recommendedProductIds)) {
                                                $recommendedProductIds[] = $productId;
                                                $productPriorityMap[$productId] = count($recommendedProductIds);
                                            }
                                        }
                                    }
                                    break;
                                } elseif (isset($scoreEntry['value']) && $answer == $scoreEntry['value']) {
                                    if (!empty($scoreEntry['recommended_product_ids']) && is_array($scoreEntry['recommended_product_ids'])) {
                                        foreach ($scoreEntry['recommended_product_ids'] as $productId) {
                                            if (!in_array($productId, $recommendedProductIds)) {
                                                $recommendedProductIds[] = $productId;
                                                $productPriorityMap[$productId] = count($recommendedProductIds);
                                            }
                                        }
                                    }
                                    break;
                                }
                            }
                            break;
                    }
                }
            }
            
            // 查询商品详情
            $recommendedProducts = [];
            if (!empty($recommendedProductIds)) {
                $products = model('goods')->getList(
                    [['goods_id', 'in', $recommendedProductIds]],
                    'goods_id,goods_name,goods_image,price'
                );
                
                // 按优先级排序
                usort($products, function($a, $b) use ($productPriorityMap) {
                    return $productPriorityMap[$a['goods_id']] - $productPriorityMap[$b['goods_id']];
                });
                
                $recommendedProducts = $products;
            }
            
            // 返回整合数据
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'response' => $responseData,
                    'template' => $template,
                    'items' => $processedItems
                ]
            ]);
            
        } catch (\Exception $e) {
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 获取会员报告信息列表
     * 用于前端AJAX请求，根据member_id返回报告列表
     * @return \think\response\Json
     */
    public function getReportsList()
    {
        // 获取member_id参数并验证
        $member_id = input('member_id', 0);
        if (!is_numeric($member_id) || $member_id <= 0) {
            return json(['code' => 0, 'msg' => '无效的会员ID', 'data' => []]);
        }

        // 获取站点ID (假设在BaseShop中已初始化并可通过$this->site_id访问)
        $site_id = $this->site_id;

        // 查询所有与该会员相关的报告，按上传时间倒序排列
        $reportsList = Db::name('user_reports')
            ->where([
                ['member_id', '=', $member_id],
                ['site_id', '=', $site_id],
            ])
            ->field('id, file_name, upload_time, analysis_time, analysis_status')
            ->order('upload_time desc')
            ->select();

        // 检查是否找到报告
        if (empty($reportsList)) {
            return json(['code' => 0, 'msg' => '未找到相关报告', 'data' => []]);
        }

        // 返回成功响应
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $reportsList]);
    }
}