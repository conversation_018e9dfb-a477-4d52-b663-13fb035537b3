<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\member\Member as MemberModel;
use app\model\member\MemberAddress as MemberAddressModel;
use app\model\member\MemberLabel as MemberLabelModel;
use app\model\member\MemberLevel as MemberLevelModel;
use app\model\member\MemberAccount as MemberAccountModel;
use app\model\system\User as UserModel;
use app\model\member\Config as ConfigModel;
use app\model\system\Address as AddressModel;
use think\facade\Db;
use phpoffice\phpexcel\Classes\PHPExcel;
use phpoffice\phpexcel\Classes\PHPExcel\Writer\Excel2007;

/**
 * 会员管理 控制器
 */
class Member extends BaseShop
{
    /*
     *  会员概况
     */
    public function index()
    {

        $member = new MemberModel();

        // 累计会员数
        $total_count = $member->getMemberCount([['site_id', '=', $this->site_id]]);
        // 今日新增数
        $newadd_count = $member->getMemberCount([['site_id', '=', $this->site_id], ['reg_time', 'between', [date_to_time(date('Y-m-d 00:00:00')), time()]]]);
        // 已购会员数
        $buyed_count = $member->getMemberCount([['site_id', '=', $this->site_id], ['order_complete_num', '>', 0]]);

        $this->assign('data', [
            'total_count'  => $total_count['data'],
            'newadd_count' => $newadd_count['data'],
            'buyed_count'  => $buyed_count['data']
        ]);
        return $this->fetch('member/index');
    }

    /**
     * 历程
     */
    public function journeyMember()
    {
        //会员信息
        $member_id    = input('member_id', 0);
        $member_model = new MemberModel();
        $member_info  = $member_model->getMemberInfo([['member_id', '=', $member_id]]);
        $this->assign('member_info', $member_info);
        $this->assign('member_id', $member_id);
        $this->assign("http_type", get_http_type());
        return $this->fetch('member/journey_member');
    }

    /**
     * 获取区域会员数量
     */
    public function areaCount()
    {
        if (request()->isAjax()) {
            $member = new MemberModel();
            $handle = input('handle', false);
            $res    = $member->getMemberCountByArea($this->site_id, $handle);
            return $res;
        }
    }


    /**
     * 会员列表
     */
    public function memberList()
    {
        if (request()->isAjax()) {
            $page             = input('page', 1);
            $page_size        = input('page_size', PAGE_LIST_ROWS);
            $search_text      = input('search_text', '');
            $search_text_type = input('search_text_type', 'username');//可以传username mobile email
            $level_id         = input('level_id', 0);
            $label_id         = input('label_id', 0);
            $reg_start_date   = input('reg_start_date', '');
            $reg_end_date     = input('reg_end_date', '');
            $status           = input('status', '');

            $condition[] = ['site_id', '=', $this->site_id];
            //下拉选择
            $condition[] = [$search_text_type, 'like', "%" . $search_text . "%"];
            //会员等级
            if ($level_id != 0) {
                $condition[] = ['member_level', '=', $level_id];
            }
            //会员标签
            if ($label_id != 0) {
                //raw方法变为public类型 需要实例化以后调用
                $condition[] = ["", 'exp', Db::raw("FIND_IN_SET({$label_id}, member_label)")];
            }
            //注册时间
            if ($reg_start_date != '' && $reg_end_date != '') {
                $condition[] = ['reg_time', 'between', [strtotime($reg_start_date), strtotime($reg_end_date)]];
            } else if ($reg_start_date != '' && $reg_end_date == '') {
                $condition[] = ['reg_time', '>=', strtotime($reg_start_date)];
            } else if ($reg_start_date == '' && $reg_end_date != '') {
                $condition[] = ['reg_time', '<=', strtotime($reg_end_date)];
            }
            //会员状态
            if ($status != '') {
                $condition[] = ['status', '=', $status];
            }

            //医生组获取自己的订单
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                $user_model = new UserModel();
                $datau      = $user_model->bpdoctordata();
                if (isset($datau[$this->uid])){
                    $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                    $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                    if ($datu){
                        $condition[] = ["user_id", "in", $datu];
                    }else{
                        $condition[] = ["user_id", "=", $this->uid];
                    }

                }else{
                    $condition[] = ["user_id", "=", $this->uid];
                }
            }


            $order = 'reg_time desc';
            $field = 'user_id,member_id, username, mobile, email, status, headimg, member_level, member_level_name, member_label, member_label_name, qq, qq_openid, wx_openid, wx_unionid, ali_openid, baidu_openid, toutiao_openid, douyin_openid, login_ip, login_type, login_time, last_login_ip, last_login_type, last_login_time, login_num, nickname, realname, sex, location, birthday, reg_time, point, balance, balance_money, growth, account5';

            $member_model = new MemberModel();
            $list         = $member_model->getMemberPageList($condition, $page, $page_size, $order, $field);
            return $list;
        } else {
            //会员等级
            $member_level_model = new MemberLevelModel();
            $member_level_list  = $member_level_model->getMemberLevelList([['site_id', '=', $this->site_id]], 'level_id, level_name', 'growth asc');
            $this->assign('member_level_list', $member_level_list['data']);

            //会员标签
            $member_label_model = new MemberLabelModel();
            $member_label_list  = $member_label_model->getMemberLabelList([['site_id', '=', $this->site_id]], 'label_id, label_name', 'sort asc');
            $this->assign('member_label_list', $member_label_list['data']);

            /*奖励规则*/
            //积分
            $point = event('MemberAccountRule', ['account' => 'point', 'site_id' => $this->site_id]);
            $this->assign('point', $point);
            //余额
            $balance = event('MemberAccountRule', ['account' => 'balance', 'site_id' => $this->site_id]);
            $this->assign('balance', $balance);
            //成长值
            $growth = event('MemberAccountRule', ['account' => 'growth', 'site_id' => $this->site_id]);
            $this->assign('growth', $growth);

            return $this->fetch('member/member_list');
        }
    }

    /**
     * 会员添加
     */
    public function addMember()
    {
        if (request()->isAjax()) {

            $addressdata           = [
                'site_id'      => $this->site_id,
                'name'         => input('addressname', input('username')),
                'mobile'       => input('addressmobile', ''),
                'telephone'    => '',
                'province_id'  => input('province_id', ''),
                'city_id'      => input('city_id', ''),
                'district_id'  => input('district_id', ''),
                'community_id' => input('community_id', ''),
                'address'      => input('address', ''),
                'full_address' => input('full_address', ''),
                'longitude'    => input('longitude', ''),
                'latitude'     => input('latitude', ''),
                'is_default'   => 1,
            ];

            $data = [
                'site_id'           => $this->site_id,
                'user_id'           => $this->uid,
                'username'          => input('username', ''),
                'mobile'            => input('mobile', ''),
                'email'             => input('email', ''),
                'password'          => data_md5(input('password', '')),
                'status'            => input('status', 1),
                'headimg'           => input('headimg', ''),
                'member_level'      => 1,
                'member_level_name' => '普通会员',
                'nickname'          => input('nickname', ''),
                'sex'               => input('sex', 0),
                'birthday'          => input('birthday', '') ? strtotime(input('birthday', '')) : 0,
                'realname'          => input('realname', ''),
                'reg_time'          => time(),
                'goods_image'       => input("goods_image", ""),// 商品主图路径
                'goods_content'     => input("goods_content", ""),// 商品详情
            ];

            $member_model = new MemberModel();
            $this->addLog("添加会员" . $data['username'] . $data['mobile']);
            return $member_model->addMember($data,$addressdata);
        } else {
            //获取订单常用收货地址
            $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
            $this->assign("order_deverys", $order_deverys);
            //查询省级数据列表
            $address_model = new AddressModel();
            $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
            $this->assign("province_list", $list["data"]);
            //会员等级
            $member_level_model = new MemberLevelModel();
            $member_level_list  = $member_level_model->getMemberLevelList([['site_id', '=', $this->site_id]], 'level_id, level_name', 'growth asc');
            $this->assign('member_level_list', $member_level_list['data']);
            $this->assign("http_type", get_http_type());
            return $this->fetch('member/add_member');
        }
    }

    /**
     * 会员编辑
     */
    public function editMember()
    {
        if (request()->isAjax()) {
            $addressdata           = [
                'site_id'      => $this->site_id,
                'name'         => input('addressname', input('username')),
                'mobile'       => input('addressmobile', ''),
                'telephone'    => '',
                'province_id'  => input('province_id', ''),
                'city_id'      => input('city_id', ''),
                'district_id'  => input('district_id', ''),
                'community_id' => input('community_id', ''),
                'address'      => input('address', ''),
                'full_address' => input('full_address', ''),
                'longitude'    => input('longitude', ''),
                'latitude'     => input('latitude', ''),
                'is_default'   => 1,
            ];

            $data = [
                'mobile'            => input('mobile', ''),
                'email'             => input('email', ''),
                'status'            => input('status', 1),
                'headimg'           => input('headimg', ''),
                'member_level'      => 1,
                'member_level_name' => '普通会员',
                'nickname'          => input('nickname', ''),
                'sex'               => input('sex', 0),
                'birthday'          => input('birthday', '') ? strtotime(input('birthday', '')) : 0,
            ];

            $member_id    = input('member_id', 0);
            $member_model = new MemberModel();
            $this->addLog("编辑会员:id" . $member_id, $data);
            return $member_model->editMember($data, [['member_id', '=', $member_id]],$addressdata);
        } else {
            //获取订单常用收货地址
            $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
            $this->assign("order_deverys", $order_deverys);
            //查询省级数据列表
            $address_model = new AddressModel();
            $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
            $this->assign("province_list", $list["data"]);
            //会员等级
            $member_level_model = new MemberLevelModel();
            $member_level_list  = $member_level_model->getMemberLevelList([['site_id', '=', $this->site_id]], 'level_id, level_name', 'growth asc');
            $this->assign('member_level_list', $member_level_list['data']);

            //会员信息
            $member_id    = input('member_id', 0);
            $member_model = new MemberModel();
            $member_info  = $member_model->getMemberInfo([['member_id', '=', $member_id]]);
            $this->assign('member_info', $member_info);

            //获取会员默认收货地址
            $info = model('member_address')->getInfo([['member_id', '=', $member_id],['is_default', '=', 1]], '*');
            $this->assign('info', $info);
            //会员详情四级菜单
            $this->forthMenu(['member_id' => $member_id]);
            $this->assign("http_type", get_http_type());
            return $this->fetch('member/edit_member');
        }
    }

    /**
     * 会员删除
     */
    public function deleteMember()
    {
        $member_ids   = input('member_ids', '');
        $member_model = new MemberModel();
        $this->addLog("删除会员:id" . $member_ids);
        return $member_model->deleteMember([['member_id', 'in', $member_ids], ['site_id', '=', $this->site_id]]);
    }

    /**
     * 会员删除
     */
    public function memberAddress()
    {
        $member_id   = input('member_id', '0');
        //固定收获地址
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['shouhuodizhi']==1){
            $member_id = $user['member_id'];
        }
        $info = model('member_address')->getInfo([['member_id', '=', $member_id],['is_default', '=', 1]], '*');
        $member_model = new MemberModel();
        $member_info  = $member_model->getMemberDetail($member_id);
        $info['member_id'] = $member_id;
        $info['goods_image'] = $member_info['data']['goods_image'];
        $info['goods_content'] = $member_info['data']['goods_content'];
        $member_id   = input('member_id', '0');
        $member_info  = $member_model->getMemberDetail($member_id);
        $orderlist = model('order')->getList([['member_id', '=', $member_id],['order_status', '>', 0]],'*','order_id desc','',[],'',10);
        $memberhistroy = '<option value="">请选择既往处方</option>';
        foreach($orderlist as $key=>$vo){
            $memberhistroy = $memberhistroy.'<option value="'.$vo['order_id'].'">'.$member_info['data']['nickname'].date("Y年m月",$vo['create_time']).'</option>';
        }
        $info['memberhistroy'] = $memberhistroy;
        return [
            'code' => 0,
            'message' => '成功',
            'data' => $info
        ];
    }

    public function orderAddress()
    {
        $order_deverys   = input('order_deverys', '');
        $order_deverys = explode('-',$order_deverys);
        //订单收货地址
        $order = model('order')->getInfo([['admin_uid', "=", $this->uid],['name', "=", $order_deverys[0]],['mobile', "=", $order_deverys[1]],['address', "=", $order_deverys[2]]]);
        return [
            'code' => 0,
            'message' => '成功',
            'data' => $order
        ];
    }

    /**
     * 修改会员标签
     */
    public function modifyLabel()
    {
        $member_ids   = input('member_ids', '');
        $label_ids    = input('label_ids', '');
        $member_model = new MemberModel();
        return $member_model->modifyMemberLabel($label_ids, [['member_id', 'in', $member_ids]]);
    }

    /**
     * 修改会员状态
     */
    public function modifyStatus()
    {
        $member_ids   = input('member_ids', '');
        $status       = input('status', 0);
        $member_model = new MemberModel();
        return $member_model->modifyMemberStatus($status, [['member_id', 'in', $member_ids]]);
    }

    /**
     * 修改会员密码
     */
    public function modifyPassword()
    {
        $member_ids   = input('member_ids', '');
        $password     = input('password', '123456');
        $member_model = new MemberModel();
        return $member_model->resetMemberPassword($password, [['member_id', 'in', $member_ids]]);
    }

    /**
     * 账户详情
     */
    public function accountDetail()
    {
        if (request()->isAjax()) {
            $data = [
                'goods_image'       => input("goods_image", ""),// 商品主图路径
            ];

            $member_id    = input('member_id', 0);
            $member_model = new MemberModel();
            $this->addLog("编辑检查报告:id" . $member_id, $data);
            return $member_model->editMember($data, [['member_id', '=', $member_id]],['address'=>'']);
        } else {
            $member_id = input('member_id', 0);

            //会员信息
            $member_model = new MemberModel();
            $member_info  = $member_model->getMemberDetail($member_id);
            $this->assign('member_info', $member_info['data']);

            //账户类型和来源类型
            $member_account_model = new MemberAccountModel();
            $account_type_arr     = $member_account_model->getAccountType();
//			$from_type_arr = $member_account_model->getFromType();
            $this->assign('account_type_arr', $account_type_arr);
//			$this->assign('from_type_arr', $from_type_arr['point']);

            //会员详情四级菜单
            $this->forthMenu(['member_id' => $member_id]);

            return $this->fetch('member/account_detail');
        }
    }

    /**
     * 余额调整（不可提现）
     */
    public function adjustBalance()
    {
        $member_id  = input('member_id', 0);
        $adjust_num = input('adjust_num', 0);
        $remark     = input('remark', '');
        $this->addLog("会员余额调整id:" . $member_id . "金额" . $adjust_num);
        $member_account_model = new MemberAccountModel();
        return $member_account_model->addMemberAccount($this->site_id, $member_id, 'balance', $adjust_num, 'adjust', 0, $remark);
    }

    /**
     * 余额调整（可提现）
     */
    public function adjustBalanceMoney()
    {
        $member_id  = input('member_id', 0);
        $adjust_num = input('adjust_num', 0);
        $remark     = input('remark', '');
        $this->addLog("会员余额调整id:" . $member_id . "金额" . $adjust_num);
        $member_account_model = new MemberAccountModel();
        return $member_account_model->addMemberAccount($this->site_id, $member_id, 'balance_money', $adjust_num, 'adjust', 0, $remark);
    }

    /**
     * 积分调整
     */
    public function adjustPoint()
    {
        $member_id  = input('member_id', 0);
        $adjust_num = input('adjust_num', 0);
        $remark     = input('remark', '');
        $this->addLog("会员积分调整id:" . $member_id . "数量" . $adjust_num);
        $member_account_model = new MemberAccountModel();
        return $member_account_model->addMemberAccount($this->site_id, $member_id, 'point', $adjust_num, 'adjust', 0, $remark);
    }

    /**
     * 成长值调整
     */
    public function adjustGrowth()
    {
        $member_id  = input('member_id', 0);
        $adjust_num = input('adjust_num', 0);
        $remark     = input('remark', '');
        $this->addLog("会员成长值调整id:" . $member_id . "数量" . $adjust_num);
        $member_account_model = new MemberAccountModel();
        return $member_account_model->addMemberAccount($this->site_id, $member_id, 'growth', $adjust_num, 'adjust', 0, $remark);
    }

    /**
     * 注册协议
     */
    public function regAgreement()
    {
        if (request()->isAjax()) {
            //设置注册协议
            $title        = input('title', '');
            $content      = input('content', '');
            $config_model = new ConfigModel();
            return $config_model->setRegisterDocument($title, $content, $this->site_id, 'shop');
        } else {
            //获取注册协议
            $config_model  = new ConfigModel();
            $document_info = $config_model->getRegisterDocument($this->site_id, 'shop');
            $this->assign('document_info', $document_info);

            return $this->fetch('member/reg_agreement');
        }
    }

    /**
     * 注册设置
     */
    public function regConfig()
    {
        $config_model = new ConfigModel();
        if (request()->isAjax()) {
            //设置注册设置
            $data = array(
                'is_enable'          => input('is_enable', 1),
                'type'               => input('type', ''),
                'keyword'            => input('keyword', ''),
                'pwd_len'            => input('pwd_len', 6),
                'pwd_complexity'     => input('pwd_complexity', 'number,letter,upper_case,symbol'),
                'dynamic_code_login' => input('dynamic_code_login', 1)
            );
            return $config_model->setRegisterConfig($data, $this->site_id, 'shop');
        } else {
            //获取注册设置
            $config_info = $config_model->getRegisterConfig($this->site_id, 'shop');
            $value       = $config_info['data']['value'];
            if (!empty($value)) {
                $value['type_arr']           = explode(',', $value['type']);
                $value['pwd_complexity_arr'] = explode(',', $value['pwd_complexity']);
            }
            $this->assign('value', $value);
            return $this->fetch('member/reg_config');
        }
    }

    /**
     * 搜索会员
     * 不是菜单 不入权限
     */
    public function searchMember()
    {
        $search_text  = input('search_text', '');
        $member_model = new MemberModel();
        $member_info  = $member_model->getMemberInfo([['username|mobile', '=', $search_text], ['site_id', '=', $this->site_id]]);
        return $member_info;
    }

    /**
     * 导出会员信息
     */
    public function exportMember()
    {
        //获取会员信息
        $search_text      = input('search_text', '');
        $search_text_type = input('search_text_type', 'username');//可以传username mobile email
        $level_id         = input('level_id', 0);
        $label_id         = input('label_id', 0);
        $reg_start_date   = input('reg_start_date', '');
        $reg_end_date     = input('reg_end_date', '');
        $status           = input('status', '');

        $condition[] = ['site_id', '=', $this->site_id];
        //下拉选择
        $condition[] = [$search_text_type, 'like', "%" . $search_text . "%"];
        //会员等级
        if ($level_id != 0) {
            $condition[] = ['member_level', '=', $level_id];
        }
        //会员标签
        if ($label_id != 0) {
            //raw方法变为public类型 需要实例化以后调用
            $condition[] = ["", 'exp', Db::raw("FIND_IN_SET({$label_id}, member_label)")];
        }
        //注册时间
        if ($reg_start_date != '' && $reg_end_date != '') {
            $condition[] = ['reg_time', 'between', [strtotime($reg_start_date), strtotime($reg_end_date)]];
        } else if ($reg_start_date != '' && $reg_end_date == '') {
            $condition[] = ['reg_time', '>=', strtotime($reg_start_date)];
        } else if ($reg_start_date == '' && $reg_end_date != '') {
            $condition[] = ['reg_time', '<=', strtotime($reg_end_date)];
        }
        //会员状态
        if ($status != '') {
            $condition[] = ['status', '=', $status];
        }

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["user_id", "=", $this->uid];
                }

            }else{
                $condition[] = ["user_id", "=", $this->uid];
            }
        }

        $order = 'reg_time desc';
        $field = 'username,nickname,realname,mobile,sex,birthday,email,member_level_name,member_label_name,
        qq,location,balance,balance_money,point,growth,reg_time,last_login_ip,last_login_time';

        $member_model = new MemberModel();
        $list         = $member_model->getMemberList($condition, $field, $order);

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("会员信息");
        $phpExcel->getProperties()->setSubject("会员信息");
        // 对单元格设置居中效果
        $phpExcel->getActiveSheet()->getStyle('A')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('B')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('C')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('D')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('E')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('F')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('G')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('H')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('I')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('J')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('K')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('L')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('M')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('N')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('O')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('P')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        $phpExcel->getActiveSheet()->setCellValue('A1', '会员账号');//可以指定位置
        $phpExcel->getActiveSheet()->setCellValue('B1', '会员昵称');
        $phpExcel->getActiveSheet()->setCellValue('C1', '真实姓名');
        $phpExcel->getActiveSheet()->setCellValue('D1', '手机号');
        $phpExcel->getActiveSheet()->setCellValue('E1', '性别');
        $phpExcel->getActiveSheet()->setCellValue('F1', '生日');
        $phpExcel->getActiveSheet()->setCellValue('G1', '会员等级');
        $phpExcel->getActiveSheet()->setCellValue('H1', '会员标签');
        $phpExcel->getActiveSheet()->setCellValue('I1', 'qq');
        $phpExcel->getActiveSheet()->setCellValue('J1', '地址');
        $phpExcel->getActiveSheet()->setCellValue('K1', '余额');
        $phpExcel->getActiveSheet()->setCellValue('L1', '积分');
        $phpExcel->getActiveSheet()->setCellValue('M1', '成长值');
        $phpExcel->getActiveSheet()->setCellValue('N1', '上次登录时间');
        $phpExcel->getActiveSheet()->setCellValue('O1', '上次登录ip');
        $phpExcel->getActiveSheet()->setCellValue('P1', '注册时间');
        //循环添加数据（根据自己的逻辑）
        $sex = ['保密', '男', '女'];
        foreach ($list['data'] as $k => $v) {
            $i = $k + 2;
            $phpExcel->getActiveSheet()->setCellValue('A' . $i, $v['username']);
            $phpExcel->getActiveSheet()->setCellValue('B' . $i, $v['nickname']);
            $phpExcel->getActiveSheet()->setCellValue('C' . $i, $v['realname']);
            $phpExcel->getActiveSheet()->setCellValue('D' . $i, $v['mobile']);
            $phpExcel->getActiveSheet()->setCellValue('E' . $i, $sex[$v['sex']]);
            $phpExcel->getActiveSheet()->setCellValue('F' . $i, date('Y-m-d', $v['birthday']));
            $phpExcel->getActiveSheet()->setCellValue('G' . $i, $v['member_level_name']);
            $phpExcel->getActiveSheet()->setCellValue('H' . $i, $v['member_label_name']);
            $phpExcel->getActiveSheet()->setCellValue('I' . $i, $v['qq']);
            $phpExcel->getActiveSheet()->setCellValue('J' . $i, $v['location']);
            $phpExcel->getActiveSheet()->setCellValue('K' . $i, $v['balance'] + $v['balance_money']);
            $phpExcel->getActiveSheet()->setCellValue('L' . $i, $v['point']);
            $phpExcel->getActiveSheet()->setCellValue('M' . $i, $v['growth']);
            $phpExcel->getActiveSheet()->setCellValue('N' . $i, date('Y-m-d H:i:s', $v['last_login_time']));
            $phpExcel->getActiveSheet()->setCellValue('O' . $i, $v['last_login_ip']);
            $phpExcel->getActiveSheet()->setCellValue('P' . $i, date('Y-m-d H:i:s', $v['reg_time']));
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('会员信息');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('Y年m月d日-会员信息表', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }

    /**
     * 订单管理
     */
    public function order()
    {
        $member_id = input("member_id", 0);//会员id
        $this->assign('member_id', $member_id);
        //会员详情四级菜单
        $this->forthMenu(['member_id' => $member_id]);
        return $this->fetch('member/order');

    }

    /**
     * 会员地址
     */
    public function addressDetail()
    {
        if (request()->isAjax()) {
            $data = [
                'goods_content'       => input("goods_content", ""),// 商品主图路径
            ];

            $member_id    = input('member_id', 0);
            $member_model = new MemberModel();
            $this->addLog("编辑病历:id" . $member_id, $data);
            return $member_model->editMember($data, [['member_id', '=', $member_id]],['address'=>'']);
        } else {
            $member_id = input('member_id', 0);

            //会员信息
            $member_model = new MemberModel();
            $member_info = $member_model->getMemberDetail($member_id);
            $this->assign('member_info', $member_info['data']);

            //会员详情四级菜单
            $this->forthMenu(['member_id' => $member_id]);

            return $this->fetch('member/address_detail');
        }
    }

    /**
     * 根据账户类型获取来源类型
     * @return array
     */
    public function getFromType()
    {
        $type  = input('type', '');
        $model = new MemberAccountModel();
        $res   = $model->getFromType();

        return $res[$type];
    }
}