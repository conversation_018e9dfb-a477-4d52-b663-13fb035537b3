<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\goods\GoodsAttribute as GoodsAttributeModel;
use app\model\goods\Goods as GoodsModel;
use app\model\system\User as UserModel;

/**
 * 商品类型/属性管理 控制器
 */
class Orderattr extends BaseShop
{
    /**
     * 商品类型列表
     */
    public function lists()
    {
        if (request()->isAjax()) {

            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");
            $condition   = [];
            $condition[] = ['site_id', '=', $this->site_id];
            $condition[] = ['uid', '=', $this->uid];
            $condition[] = ['guding', '=', 0];
            if (!empty($search_keys)) {
                $condition[] = ['class_name', 'like', '%' . $search_keys . '%'];
            }
            $list = model('order_attr_class')->pageList($condition, 'class_id,price,class_name,sort', 'class_id desc', $page_index, $page_size);
            return success(0,'SUCCESS',$list);

        } else {

            return $this->fetch('orderattr/lists');
        }
    }

    public function export()
    {
        $condition   = [];
        $condition[] = ['site_id', '=', $this->site_id];
        $condition[] = ['uid', '=', 207];
        //   $condition[] = ['guding', '=', 0];
        if (!empty($search_keys)) {
            $condition[] = ['class_name', 'like', '%' . $search_keys . '%'];
        }
        $list = model('order_attr_class')->getList($condition, '*', 'class_id desc');
        $export = [];
        foreach($list as $key=>$vo){
            $goods_model = new GoodsModel();
            $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $vo['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
            $goods_sku = [];
            foreach ($goods_sku_list['data'] as $k=>$v){
                //获取中文名称
                $goods_model = new GoodsModel();
                $goods_info  = $goods_model->getGoodsInfo([['goods_id', '=', $v['goods_id']]], '*');
                $goods_info  = $goods_info['data'];
                $v['sku_chi'] = $goods_info['goods_chi'];
                $goods_sku[$v['sku_id']] = $v;
            }
            $goods_name = json_decode($vo['goods_name'],true);
            $goods_num  = json_decode($vo['goods_num'],true);
            $goods_list = [];
            foreach ($goods_name as $keyy=>$voo){
                $goods = explode('_',$voo);
                $goods_detail = $goods_sku[$goods[1]];
                $goods_detail['num'] = $goods_num[$keyy];
                if (isset($goods_list[$goods[1]])){
                    $goods_list[$goods[1]]['num'] = $goods_list[$goods[1]]['num'] + $goods_detail['num'];
                }else{
                    $goods_list[$goods[1]]=$goods_detail;
                }
            }
            $jilu = 0;
            $total_goodsprice = 0;
            $total_count = count($goods_list);
            foreach($goods_list as $keyyy=>$vooo){
                $detail = $vooo;
                $detail['goodsprice'] = $vooo['price'] * $vooo['num'] * $vo['day'];
                $total_goodsprice = $total_goodsprice + $detail['goodsprice'];
                if ($jilu == 0){
                    $detail['xuhao'] = $key+1;
                    $detail['class_name'] = $vo['class_name'];
                    $detail['day'] = $vo['day'];
                    //  $detail['totalprice'] = $vo['price'];
                    $detail['totalprice'] = '';
                    $detail['baoyao'] = '' ;
                    $detail['tgprice'] = '' ;
                }else{
                    $detail['xuhao'] = '';
                    $detail['class_name'] = '';
                    $detail['day'] = '';
                    $detail['totalprice'] = '';
                    $detail['baoyao'] = '';
                    $detail['tgprice'] = '' ;
                }
                //判断是否为最后一行
                if ($jilu == ($total_count - 1)){
                    //    $detail['baoyao'] = $vo['price'] - $total_goodsprice;
                    $detail['tgprice'] = $total_goodsprice ;
                }
                $export[] = $detail;
                $jilu = $jilu + 1;
            }
        }

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("处方模板");
        $phpExcel->getProperties()->setSubject("处方模板");
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);

        $field = [
            'xuhao'=>'序号',
            'class_name'=>'套餐名称',
            'sku_name'=>'商品名称',
            'sku_chi'=>'中文名称',
            'num'=>'数量',
            'price'=>'单价',
            'day'=>'天数',
            'goodsprice'=>'商品金额',
            'tgprice'=>'订单商品总金额',
            'baoyao'=>'包药费',
            'totalprice'=>'订单合计金额',
        ];
        $input_field=['xuhao','class_name','sku_name','sku_chi','num','price','day','goodsprice','tgprice','baoyao','totalprice'];
        $count = count($input_field);
        $header_arr  = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );

        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $field[$input_field[$i]]);
        }

        if (!empty($export)) {
            foreach ($export as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {

                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('处方模板');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('Y年m月d日-处方模板', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }

    /**
     * 固定模版列表
     */
    public function gudinglists()
    {
        if (request()->isAjax()) {

            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");
            $category_id = input('category_id', 0);
            $condition   = [];
            $condition[] = ['a.site_id', '=', $this->site_id];
            $condition[] = ['a.guding', '=', 1];
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if ($user_bpdotocr[$this->uid]['bp']){
                $condition[] = ['a.uid', '=', $user_bpdotocr[$this->uid]['bp']];
            }else{
                $condition[] = ['a.uid', '=', $this->uid];
            }
            if (!empty($search_keys)) {
                $condition[] = ['a.class_name', 'like', '%' . $search_keys . '%'];
            }
            if ($category_id > 0) {
                $condition[] = ['a.category_id', '=', $category_id];
            }
            // 使用JOIN关联查询获取分类名称
            $join = [
                ['prescription_template_category c', 'a.category_id = c.category_id', 'left']
            ];
            $list = model('order_attr_class')->pageList($condition, 'a.class_id,a.price,a.class_name,a.sort,a.category_id,a.header_image,c.category_name', 'a.class_id desc', $page_index, $page_size, 'a', $join);

            // 处理返回数据
            if (!empty($list['list'])) {
                foreach ($list['list'] as &$item) {
                    // 处理分类名称为空的情况
                    if (empty($item['category_name'])) {
                        $item['category_name'] = '未分类';
                    }
                    // 处理头图显示
                    if (!empty($item['header_image'])) {
                        // 如果已经是完整URL，直接使用；否则拼接域名
                        if (strpos($item['header_image'], 'http') === 0) {
                            $item['header_image_url'] = $item['header_image'];
                        } else {
                            $item['header_image_url'] = request()->domain() . $item['header_image'];
                        }
                    } else {
                        $item['header_image_url'] = '';
                    }
                }
            }
            return success(0,'SUCCESS',$list);

        } else {
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if ($user_bpdotocr[$this->uid]['bp'] == $this->uid || $this->uid==1){
                $this->assign("bp", 1);
            }else{
                $this->assign("bp", 0);
            }

            // 获取分类选项
            $category_options = model('prescription_template_category')->getList([['site_id', '=', $this->site_id], ['status', '=', 1]], 'category_id,category_name', 'sort asc, category_id asc');
            $this->assign("category_options", $category_options);

            return $this->fetch('orderattr/gudinglists');
        }
    }

    /**
     * 参考模版列表
     */
    public function cankaolists()
    {
        if (request()->isAjax()) {

            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");
            $condition   = [];
            $condition[] = ['site_id', '=', $this->site_id];
            $condition[] = ['uid', '=', 1];
            $condition[] = ['guding', '=', 1];
            if (!empty($search_keys)) {
                $condition[] = ['class_name', 'like', '%' . $search_keys . '%'];
            }
            $list = model('order_attr_class')->pageList($condition, 'class_id,age_type,price,class_name,sort', 'class_id desc', $page_index, $page_size);
            return success(0,'SUCCESS',$list);

        } else {

            return $this->fetch('orderattr/cankaolists');
        }
    }


    /**
     * order处生成处方模板
     */
    public function addorderAttr()
    {
        if (request()->isAjax()) {
            $class_id       = input('class_id', 0);
            $class_name       = input('class_name', '');
            $sort             = input('sort', 0);
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $price = input('price', '');
            $day = input('day',0);
            $sku_ids = input('sku_ids', '');
            $ccheck = input('ccheck', 0);
            $fenji = input('fenji', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');
            $data             = [
                'uid'        => $this->uid,
                'site_id'    => $this->site_id,
                'class_name' => $class_name,
                'sort'       => $sort,
                'goods_name' =>json_encode($goods_name),
                'goods_num'  =>json_encode($goods_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'price'    => floatval($price),
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            if ($class_id > 0){
                unset($data['class_name']);
                $res = model('order_attr_class')->update($data, [['class_id', '=', $class_id], ['site_id', '=', $this->site_id]]);
            }else{
                if (!$class_name){
                    return error(-1, '请填写处方模板名称！');
                }
                if (!$goods_name){
                    return error(-1, '请填写处方模板商品不能为空！');
                }
                $res = model("order_attr_class")->add($data);
            }
            return success(0, 'SUCCESS', $res);
        }
    }

    /**
     * 商品类型添加
     */
    public function addAttr()
    {
        if (request()->isAjax()) {
            $class_name       = input('class_name', '');
            $sort             = input('sort', 0);
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $price = input('price', '');
            $day = input('day',0);
            $sku_ids = input('sku_ids', '');
            $data             = [
                'uid'        => $this->uid,
                'site_id'    => $this->site_id,
                'class_name' => $class_name,
                'sort'       => $sort,
                'goods_name' =>json_encode($goods_name),
                'goods_num'  =>json_encode($goods_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'price'    => floatval($price),
            ];
            $res              = model("order_attr_class")->add($data);
            return success(0, 'SUCCESS', $res);
        }
    }

    /**
     * 商品类型添加
     */
    public function addgudingAttr()
    {
        if (request()->isAjax()) {
            $class_name       = input('class_name', '');
            $age_type = input('age_type', '');
            $category_id = input('category_id', 0);
            $header_image = input('header_image', '');
            $rich_content = input('rich_content', '');
            $sort             = input('sort', 0);
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $price = input('price', '');
            $day = input('day',0);
            $sku_ids = input('sku_ids', '');
            $data             = [
                'uid'        => $this->uid,
                'site_id'    => $this->site_id,
                'class_name' => $class_name,
                'age_type' => $age_type,
                'category_id' => $category_id,
                'header_image' => $header_image,
                'rich_content' => $rich_content,
                'sort'       => $sort,
                'goods_name' =>json_encode($goods_name),
                'goods_num'  =>json_encode($goods_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'price'    => floatval($price),
                'guding'    => 1,
            ];
            $res              = model("order_attr_class")->add($data);
            return success(0, 'SUCCESS', $res);
        }
    }

    /**
     * 商品类型编辑
     */
    public function editAttr()
    {
        $goods_attr_model = new GoodsAttributeModel();
        if (request()->isAjax()) {

            $class_id   = input("class_id", 0);
            $class_name = input('class_name', '');
            $age_type = input('age_type', '');
            $category_id = input('category_id', 0);
            $header_image = input('header_image', '');
            $rich_content = input('rich_content', '');
            $sort       = input('sort', 0);
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $price = input('price', 0);
            $day = input('day',0);
            $ccheck = input('ccheck', 0);
            $fenji = input('fenji', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');
            $sku_ids = input('sku_ids', '');
            $data       = [
                'class_name' => $class_name,
                'age_type' => $age_type,
                'category_id' => $category_id,
                'header_image' => $header_image,
                'rich_content' => $rich_content,
                'sort'       => $sort,
                'goods_name' =>json_encode($goods_name),
                'goods_num'  =>json_encode($goods_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'price'    => $price,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            if (!$goods_name){
                return error('-1','处方商品不能为空');
            }
            $res = model('order_attr_class')->update($data, [['class_id', '=', $class_id], ['site_id', '=', $this->site_id]]);
            return success(0,'SUCCESS',$res);

        } else {
            $class_id = input("class_id", 0);
            $this->assign("class_id", $class_id);

            //商品类型信息
            $attr_class_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id], ['site_id', '=', $this->site_id]], '*');
            $goods_model = new GoodsModel();
            $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $attr_class_info['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
            $goods_sku = [];
            foreach ($goods_sku_list['data'] as $key=>$vo){
                $goods_sku[$vo['sku_id']] = $vo;
            }
            $goods_name = json_decode($attr_class_info['goods_name'],true);
            $goods_num  = json_decode($attr_class_info['goods_num'],true);
            $goods_list = [];
            foreach ($goods_name as $key=>$vo){
                $goods = explode('_',$vo);
                $goods_detail = $goods_sku[$goods[1]];
                $goods_detail['num'] = $goods_num[$key];
                $goods_list[$goods[0]][$goods[1]]=$goods_detail;
            }
            $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
            $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
            $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
            $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
            $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
            $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
            $this->assign("earlymoning_key", isset($goods_list['earlymoring'])?array_keys($goods_list['earlymoring']):[]);
            $this->assign("moning_key", isset($goods_list['moning'])?array_keys($goods_list['moning']):[]);
            $this->assign("aftnoon_key", isset($goods_list['aftnoon'])?array_keys($goods_list['aftnoon']):[]);
            $this->assign("canjian_key", isset($goods_list['canjian'])?array_keys($goods_list['canjian']):[]);
            $this->assign("night_key", isset($goods_list['night'])?array_keys($goods_list['night']):[]);
            $this->assign("evening_key", isset($goods_list['evening'])?array_keys($goods_list['evening']):[]);
            $this->assign("attr_class_info", $attr_class_info);

            return $this->fetch('orderattr/edit_attr');
        }
    }

    //订单模版
    public function ordera(){
        $class_id   = input("class_id", 0);

        $attr_class_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id]], '*');

        $goods_model = new GoodsModel();
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $attr_class_info['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
        $goods_sku = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }
        //按产品生成的模板
        $sku_ids = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){

            $sku_ids[$vo['sku_id']]=$vo;
            $sku_ids[$vo['sku_id']]['earlymoring'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['earlymoring_s'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['earlymoring_num'] = isset($goods_list['earlymoring'][$vo['sku_id']])?$goods_list['earlymoring'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['moning'] = isset($goods_list['moning'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['moning_s'] = isset($goods_list['moning'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['moning_num'] = isset($goods_list['moning'][$vo['sku_id']])?$goods_list['moning'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['aftnoon'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['aftnoon_s'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['aftnoon_num'] = isset($goods_list['aftnoon'][$vo['sku_id']])?$goods_list['aftnoon'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['canjian'] = isset($goods_list['canjian'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['canjian_s'] = isset($goods_list['canjian'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['canjian_num'] = isset($goods_list['canjian'][$vo['sku_id']])?$goods_list['canjian'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['night'] = isset($goods_list['night'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['night_s'] = isset($goods_list['night'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['night_num'] = isset($goods_list['night'][$vo['sku_id']])?$goods_list['night'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['evening'] = isset($goods_list['evening'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['evening_s'] = isset($goods_list['evening'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['evening_num'] = isset($goods_list['evening'][$vo['sku_id']])?$goods_list['evening'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['teshu'] = isset($goods_list['teshu'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['teshu_s'] = isset($goods_list['teshu'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['teshu_num'] = isset($goods_list['teshu'][$vo['sku_id']])?$goods_list['teshu'][$vo['sku_id']]['num']:1;

        }
        return [
            'code' => 0,
            'message' => '成功',
            'earlymoning' => isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[],
            'moning' => isset($goods_list['moning'])?$goods_list['moning']:[],
            'aftnoon' => isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[],
            'canjian' => isset($goods_list['canjian'])?$goods_list['canjian']:[],
            'night' => isset($goods_list['night'])?$goods_list['night']:[],
            'evening' => isset($goods_list['evening'])?$goods_list['evening']:[],
            'teshu' => isset($goods_list['teshu'])?$goods_list['teshu']:[],
            'attr_class_info' => $attr_class_info,
            'sku_ids' => $sku_ids,
            'day'=>$attr_class_info['day'],
            'price'=>0,
        ];
    }

    /**
     * 商品类型编辑
     */
    public function editgudingAttr()
    {
        $goods_attr_model = new GoodsAttributeModel();
        if (request()->isAjax()) {

            $class_id   = input("class_id", 0);
            $class_name = input('class_name', '');
            $age_type = input('age_type', '');
            $category_id = input('category_id', 0);
            $header_image = input('header_image', '');
            $rich_content = input('rich_content', '');
            $sort       = input('sort', 0);
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $price = input('price', 0);
            $day = input('day',0);
            $ccheck = input('ccheck', 0);
            $fenji = input('fenji', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');
            $sku_ids = input('sku_ids', '');
            $data       = [
                'class_name' => $class_name,
                'age_type' => $age_type,
                'category_id' => $category_id,
                'header_image' => $header_image,
                'rich_content' => $rich_content,
                'sort'       => $sort,
                'goods_name' =>json_encode($goods_name),
                'goods_num'  =>json_encode($goods_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'price'    => $price,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            if (!$goods_name){
                return error('-1','处方商品不能为空');
            }

            // 调试信息：记录要更新的数据
            error_log('Update data: ' . json_encode($data));
            error_log('Update condition: class_id=' . $class_id . ', site_id=' . $this->site_id);

            // 清理字段缓存
            try {
                $cache_path = app()->getRuntimePath() . 'schema' . DIRECTORY_SEPARATOR . 'niushop2.ns_order_attr_class.php';
                if (file_exists($cache_path)) {
                    unlink($cache_path);
                    error_log('Cleared field cache: ' . $cache_path);
                }
            } catch (\Exception $e) {
                error_log('Failed to clear cache: ' . $e->getMessage());
            }

            try {
                // 先检查表结构
                $fields = \think\facade\Db::name('order_attr_class')->getTableFields();
                error_log('Table fields: ' . json_encode($fields));

                $res = model('order_attr_class')->update($data, [['class_id', '=', $class_id], ['site_id', '=', $this->site_id]]);
                // 调试信息：记录更新结果
                error_log('Update result: ' . json_encode($res));
            } catch (\Exception $e) {
                // 记录详细错误信息
                error_log('Update failed with error: ' . $e->getMessage());
                error_log('Error code: ' . $e->getCode());
                error_log('Error file: ' . $e->getFile() . ' line: ' . $e->getLine());
                return error(-1, '更新失败：' . $e->getMessage());
            }

            return success(0,'SUCCESS',$res);

        } else {
            $class_id = input("class_id", 0);
            $cankao = input("cankao", 0);
            $this->assign("class_id", $class_id);
            $this->assign("cankao", $cankao);

            //商品类型信息
            $attr_class_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id], ['site_id', '=', $this->site_id]], '*');
            $goods_model = new GoodsModel();
            $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $attr_class_info['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
            $goods_sku = [];
            foreach ($goods_sku_list['data'] as $key=>$vo){
                $goods_sku[$vo['sku_id']] = $vo;
            }
            $goods_name = json_decode($attr_class_info['goods_name'],true);
            $goods_num  = json_decode($attr_class_info['goods_num'],true);
            $goods_list = [];
            foreach ($goods_name as $key=>$vo){
                $goods = explode('_',$vo);
                $goods_detail = $goods_sku[$goods[1]];
                $goods_detail['num'] = $goods_num[$key];
                $goods_list[$goods[0]][$goods[1]]=$goods_detail;
            }
            $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
            $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
            $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
            $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
            $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
            $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
            $this->assign("earlymoning_key", isset($goods_list['earlymoring'])?array_keys($goods_list['earlymoring']):[]);
            $this->assign("moning_key", isset($goods_list['moning'])?array_keys($goods_list['moning']):[]);
            $this->assign("aftnoon_key", isset($goods_list['aftnoon'])?array_keys($goods_list['aftnoon']):[]);
            $this->assign("canjian_key", isset($goods_list['canjian'])?array_keys($goods_list['canjian']):[]);
            $this->assign("night_key", isset($goods_list['night'])?array_keys($goods_list['night']):[]);
            $this->assign("evening_key", isset($goods_list['evening'])?array_keys($goods_list['evening']):[]);

            // 处理头图URL
            if (!empty($attr_class_info['header_image'])) {
                // 如果已经是完整URL，直接使用；否则拼接域名
                if (strpos($attr_class_info['header_image'], 'http') === 0) {
                    $attr_class_info['header_image_url'] = $attr_class_info['header_image'];
                } else {
                    $attr_class_info['header_image_url'] = request()->domain() . $attr_class_info['header_image'];
                }
            } else {
                $attr_class_info['header_image_url'] = '';
            }

            $this->assign("attr_class_info", $attr_class_info);

            // 获取分类选项
            $category_options = model('prescription_template_category')->getList([['site_id', '=', $this->site_id], ['status', '=', 1]], 'category_id,category_name', 'sort asc, category_id asc');
            $this->assign("category_options", $category_options);

            return $this->fetch('orderattr/edit_gudingattr');
        }
    }

    /*
     * 导出个性化说明
     * */
    public function printOrderr()
    {
        $class_id = input("class_id", 0);
        $type            = input('type', 0);
        //商品类型信息
        $attr_class_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id], ['site_id', '=', $this->site_id]], '*');
        $goods_model = new GoodsModel();

        $field = 'g.introduction,g.introd,g.goods_chi,g.category_id,g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $attr_class_info['sku_ids']], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $goods_sku = [];
        $goods_category = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            //获取分类
            $category_name='';
            if ($vo['category_id']) {
                $category_name = model('goods_category')->getColumn([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name');
                if (is_array($category_name)) {
                    $category_name = implode('/', $category_name);
                }
            }
            $vo['category_name'] = $category_name;
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_category = array_unique($goods_category);
        $goods_category = array_splice($goods_category,0,4);
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }

        $time = ($attr_class_info['earlymoning_time']?'晨起后':'').' '. ($attr_class_info['moning_time']?'早餐后':'').' '. ($attr_class_info['aftnoon_time']?'午餐后':'').' '. ($attr_class_info['canjian_time']?'餐间':'').' '.
            ($attr_class_info['night_time']?'晚餐后':'').' '. ($attr_class_info['sleep_time']?'晚睡前':'');
        $attr_class_info['earlymoning_time'] = substr($attr_class_info['earlymoning_time'],0,18);
        $attr_class_info['moning_time'] = substr($attr_class_info['moning_time'],0,18);
        $attr_class_info['aftnoon_time'] = substr($attr_class_info['aftnoon_time'],0,18);
        $attr_class_info['canjian_time'] = substr($attr_class_info['canjian_time'],0,18);
        $attr_class_info['night_time'] = substr($attr_class_info['night_time'],0,18);
        $attr_class_info['sleep_time'] = substr($attr_class_info['sleep_time'],0,18);
        //重新整理打印材料
        $earlymoning = isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[];
        $moning = isset($goods_list['moning'])?$goods_list['moning']:[];
        $aftnoon = isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[];
        $canjian = isset($goods_list['canjian'])?$goods_list['canjian']:[];
        $night = isset($goods_list['night'])?$goods_list['night']:[];
        $evening = isset($goods_list['evening'])?$goods_list['evening']:[];

        foreach ($earlymoning as $key=>$vo){
            $earlymoning[$key]['color'] = '#aacbeb';
            $earlymoning[$key]['type'] = 1;
        }
        foreach ($moning as $key=>$vo){
            $moning[$key]['color'] = '#c6b5e5';
            $moning[$key]['type'] = 2;
        }
        foreach ($aftnoon as $key=>$vo){
            $aftnoon[$key]['color'] = '#e1bda7';
            $aftnoon[$key]['type'] = 3;
        }
        foreach ($canjian as $key=>$vo){
            $canjian[$key]['color'] = '#ffea64';
            $canjian[$key]['type'] = 4;
        }
        foreach ($night as $key=>$vo){
            $night[$key]['color'] = '#96d4e1';
            $night[$key]['type'] = 5;
        }
        foreach ($evening as $key=>$vo){
            $evening[$key]['color'] = '#f7d1d8';
            $evening[$key]['type'] = 6;
        }
        $all = array_merge($earlymoning,$moning,$aftnoon,$canjian,$night,$evening);
        $pages = [];
        foreach ($all as $key=>$vo){
            if ($key<8){
                $pages[0][$vo['type']][] = $vo;
                continue;
            }
            if ($key>7&&$key<19){
                $pages[1][$vo['type']][] = $vo;
                continue;
            }
            if ($key>18&&$key<30){
                $pages[2][$vo['type']][] = $vo;
                continue;
            }
            if ($key>29&&$key<41){
                $pages[3][$vo['type']][] = $vo;
                continue;
            }
            if ($key>40&&$key<52){
                $pages[4][$vo['type']][] = $vo;
                continue;
            }
            if ($key>51&&$key<63){
                $pages[5][$vo['type']][] = $vo;
                continue;
            }

        }
        $this->assign("pages",$pages);
        $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
        $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
        $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
        $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
        $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
        $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("time",$time);
        $this->assign("goods_category",implode(' ',$goods_category));

        if ($type==1){
            return $this->fetch('orderattr/print_orderr_c');
        }else{
            return $this->fetch('orderattr/print_orderr');
        }

    }

    /**
     * 修改商品类型排序
     */
    public function modifySort()
    {
        if (request()->isAjax()) {

            $sort             = input('sort', 0);
            $class_id         = input('class_id', 0);
            $res = model('order_attr_class')->update(['sort' => $sort], [['class_id', '=', $class_id], ['site_id', '=', $this->site_id]]);
            return success(0, 'SUCCESS', $res);
        }
    }

    /**
     * 商品类型删除
     */
    public function deleteAttr()
    {
        if (request()->isAjax()) {
            $class_id         = input("class_id", 0);
            $res = model('order_attr_class')->delete([['class_id', '=', $class_id], ['site_id', '=', $this->site_id]]);
            return success(0, 'SUCCESS', $res);
        }
    }

    /**
     * 添加商品属性
     */
    public function addAttribute()
    {
        if (request()->isAjax()) {

            $attr_name       = input('attr_name', "");// 属性名称
            $attr_class_id   = input('attr_class_id', 0);// 商品类型id
            $attr_class_name = input('attr_class_name', "");// 商品类型名称
            $attr_value_list = input('attr_value_list', "");// 属性值列表（'',''隔开注意键值对）
            $attr_type       = input('attr_type', 0);// 属性类型  （1.单选 2.多选3. 输入 注意输入不参与筛选）
            $site_id         = $this->site_id;// 站点id

            $data = [
                'attr_name'       => $attr_name,
                'attr_class_id'   => $attr_class_id,
                'attr_class_name' => $attr_class_name,
                'attr_value_list' => $attr_value_list,
                'attr_type'       => $attr_type,
                'site_id'         => $site_id,
            ];

            $goods_attr_model = new GoodsAttributeModel();
            return $goods_attr_model->addAttribute($attr_class_id, $data);
        }

    }

    /**
     * 修改商品属性
     */
    public function editAttribute()
    {
        if (request()->isAjax()) {

            $attr_id         = input('attr_id', "");// 属性id
            $attr_name       = input('attr_name', "");// 属性名称
            $attr_class_id   = input('attr_class_id', 0);// 商品类型id
            $attr_class_name = input('attr_class_name', "");// 商品类型名称
            $attr_value_list = input('attr_value_list', "");// 属性值列表（'',''隔开注意键值对）
            $attr_type       = input('attr_type', 0);// 属性类型  （1.单选 2.多选3. 输入 注意输入不参与筛选）

            $data             = [
                'attr_id'         => $attr_id,
                'attr_name'       => $attr_name,
                'attr_class_id'   => $attr_class_id,
                'attr_class_name' => $attr_class_name,
                'attr_value_list' => $attr_value_list,
                'attr_type'       => $attr_type,
                'site_id'         => $this->site_id
            ];
            $goods_attr_model = new GoodsAttributeModel();
            return $goods_attr_model->editAttribute($attr_class_id, $data);
        }

    }

    /**
     * 删除属性、属性值
     * @return \multitype
     */
    public function deleteAttribute()
    {
        if (request()->isAjax()) {
            $attr_class_id    = input('attr_class_id', 0);// 商品类型id
            $attr_id          = input('attr_id', 0);// 属性id
            $goods_attr_model = new GoodsAttributeModel();
            $res              = $goods_attr_model->deleteAttribute($attr_class_id, $attr_id, $this->site_id);
            return $res;
        }
    }

    /**
     * 获取属性、属性值详情
     * @return \multitype
     */
    public function getAttributeDetail()
    {
        if (request()->isAjax()) {
            $attr_class_id = input('attr_class_id', 0);// 商品类型id
            $attr_id       = input('attr_id', 0);// 属性id

            $goods_attr_model = new GoodsAttributeModel();
            $attr_info        = $goods_attr_model->getAttributeInfo([['attr_class_id', '=', $attr_class_id], ['attr_id', '=', $attr_id], ['site_id', '=', $this->site_id]]);
            $attr_info        = $attr_info['data'];
            if (!empty($attr_info)) {
                $attr_value_list    = $goods_attr_model->getAttributeValueList([['attr_class_id', '=', $attr_class_id], ['attr_id', '=', $attr_id]]);
                $attr_value_list    = $attr_value_list['data'];
                $attr_info['value'] = $attr_value_list;
                return success(0, '', $attr_info);
            } else {
                return error(-1, "未查询到属性信息");
            }
        }
    }

    /**
     * 获取商品属性列表
     * @return \multitype
     */
    public function getAttributeList()
    {
        if (request()->isAjax()) {

            $attr_class_id    = input('attr_class_id', 0);// 商品类型id
            $goods_attr_model = new GoodsAttributeModel();
            return $goods_attr_model->getAttributeList([['attr_class_id', '=', $attr_class_id], ['site_id', '=', $this->site_id]]);
        }
    }

    /**
     * 添加属性值
     * @return \multitype
     */
    public function addAttributeValue()
    {

        if (request()->isAjax()) {
            $attr_class_id = input('attr_class_id', 0);// 商品类型id
            $value         = input('value', "");
            if (!empty($value)) {
                $value = json_decode($value, true);
                $data  = [];
                foreach ($value as $k => $v) {
                    $item   = [
                        'attr_value_name' => $v['attr_value_name'],
                        'attr_id'         => $v['attr_id'],
                        'attr_class_id'   => $v['attr_class_id'],
                        'sort'            => $v['sort']
                    ];
                    $data[] = $item;
                }
                $goods_attr_model = new GoodsAttributeModel();
                $res              = $goods_attr_model->addAttributeValue($attr_class_id, $data);
                return $res;
            }
        } else {
            return error(-1, "缺少value");
        }
    }

    /**
     * 修改商品属性值
     */
    public function editAttributeValue()
    {
        if (request()->isAjax()) {

            $attr_class_id = input('attr_class_id', 0);// 商品类型id
            $data          = input('data', "");// 属性值数组对象
            if (!empty($data)) {
                $data             = json_decode($data, true);
                $goods_attr_model = new GoodsAttributeModel();
                foreach ($data as $k => $v) {
                    $item = [
                        'attr_value_id'   => $v['attr_value_id'],
                        'attr_value_name' => $v['attr_value_name'],
                        'attr_id'         => $v['attr_id'],
                        'attr_class_id'   => $v['attr_class_id'],
                        'sort'            => $v['sort'],
                    ];
                    $res  = $goods_attr_model->editAttributeValue($attr_class_id, $item);
                }
                return $res;
            } else {
                return error(-1, "缺少data");
            }

        }

    }

    /**
     * 删除属性值
     * @return \multitype
     */
    public function deleteAttributeValue()
    {

        if (request()->isAjax()) {
            $attr_class_id     = input('attr_class_id', 0);// 商品类型id
            $attr_value_id_arr = input('attr_value_id_arr', 0);
            $goods_attr_model  = new GoodsAttributeModel();
            $res               = $goods_attr_model->deleteAttributeValue($attr_class_id, [['attr_value_id', 'in', $attr_value_id_arr]]);
            return $res;
        }
    }

    /**
     * 处方模板详情页面
     */
    public function templateDetail()
    {
        $class_id = input('class_id', 0);

        if ($class_id <= 0) {
            return error(-1, '模板ID无效');
        }

        // 获取模板信息
        $template_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id], ['site_id', '=', $this->site_id]], '*');

        if (empty($template_info)) {
            return error(-1, '模板不存在');
        }

        // 获取分类信息
        if ($template_info['category_id'] > 0) {
            $category_info = model('prescription_template_category')->getInfo([['category_id', '=', $template_info['category_id']]]);
            $template_info['category_name'] = $category_info['category_name'] ?? '未分类';
        } else {
            $template_info['category_name'] = '未分类';
        }

        // 处理头图URL
        if (!empty($template_info['header_image'])) {
            // 如果已经是完整URL，直接使用；否则拼接域名
            if (strpos($template_info['header_image'], 'http') === 0) {
                $template_info['header_image_url'] = $template_info['header_image'];
            } else {
                $template_info['header_image_url'] = request()->domain() . $template_info['header_image'];
            }
        } else {
            $template_info['header_image_url'] = '';
        }

        // 权限检查
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        if ($user_bpdotocr[$this->uid]['bp'] == $this->uid || $this->uid == 1) {
            $this->assign("bp", 1);
        } else {
            $this->assign("bp", 0);
        }

        $this->assign('template_info', $template_info);
        return $this->fetch('orderattr/template_detail');
    }

}