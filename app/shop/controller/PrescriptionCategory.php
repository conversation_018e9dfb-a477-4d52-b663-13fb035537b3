<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\prescription\PrescriptionCategory as PrescriptionCategoryModel;

/**
 * 处方模板分类管理控制器
 */
class PrescriptionCategory extends BaseShop
{
    /**
     * 分类列表
     */
    public function lists()
    {
        if (request()->isAjax()) {
            $page_index = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");
            $parent_id = input('parent_id', 0);
            
            $condition = [];
            $condition[] = ['site_id', '=', $this->site_id];

            if ($parent_id >= 0) {
                $condition[] = ['parent_id', '=', $parent_id];
            }

            if (!empty($search_keys)) {
                $condition[] = ['category_name', 'like', '%' . $search_keys . '%'];
            }

            try {
                $list = model('prescription_template_category')->pageList($condition, '*', 'sort asc, category_id asc', $page_index, $page_size);
            } catch (\Exception $e) {
                // 如果表不存在，返回空数据
                $list = [
                    'data' => [],
                    'count' => 0,
                    'page' => $page_index,
                    'page_size' => $page_size
                ];
            }

            // 获取每个分类的子分类数量和模板数量
            if (!empty($list['data']) && is_array($list['data'])) {
                foreach ($list['data'] as &$item) {
                    try {
                        $item['child_count'] = model('prescription_template_category')->getCount([['parent_id', '=', $item['category_id']], ['site_id', '=', $this->site_id]]);
                        $item['template_count'] = model('order_attr_class')->getCount([['category_id', '=', $item['category_id']], ['site_id', '=', $this->site_id]]);
                    } catch (\Exception $e) {
                        $item['child_count'] = 0;
                        $item['template_count'] = 0;
                    }

                    // 处理分类图片显示
                    if (!empty($item['category_image'])) {
                        $item['category_image_url'] = ns_img($item['category_image']);
                    } else {
                        $item['category_image_url'] = '';
                    }
                }
            }
            
            return success(0, 'SUCCESS', $list);
        } else {
            // 检查数据库表是否存在
            try {
                $table_exists = \think\facade\Db::query("SHOW TABLES LIKE 'ns_prescription_template_category'");
                if (empty($table_exists)) {
                    $this->assign('table_not_exists', true);
                } else {
                    $this->assign('table_not_exists', false);
                }
            } catch (\Exception $e) {
                $this->assign('table_not_exists', true);
            }

            return $this->fetch('orderattr/category_lists');
        }
    }

    /**
     * 添加分类
     */
    public function add()
    {
        if (request()->isAjax()) {
            $category_name = input('category_name', '');
            $parent_id = input('parent_id', 0);
            $category_image = input('category_image', '');
            $sort = input('sort', 0);
            $status = input('status', 1);

            if (empty($category_name)) {
                return error(-1, '分类名称不能为空');
            }

            // 检查同级分类名称是否重复
            $exist_count = model('prescription_template_category')->getCount([
                ['category_name', '=', $category_name],
                ['parent_id', '=', $parent_id],
                ['site_id', '=', $this->site_id]
            ]);

            if ($exist_count > 0) {
                return error(-1, '同级分类中已存在相同名称的分类');
            }

            $data = [
                'category_name' => $category_name,
                'parent_id' => $parent_id,
                'category_image' => $category_image,
                'sort' => $sort,
                'status' => $status,
                'site_id' => $this->site_id
            ];
            
            $category_model = new PrescriptionCategoryModel();
            $res = $category_model->addCategory($data);
            
            if ($res['code'] >= 0) {
                return success(0, '添加成功', $res['data']);
            } else {
                return error(-1, $res['message']);
            }
        }
    }

    /**
     * 编辑分类
     */
    public function edit()
    {
        if (request()->isAjax()) {
            $category_id = input('category_id', 0);
            $category_name = input('category_name', '');
            $parent_id = input('parent_id', 0);
            $category_image = input('category_image', '');
            $sort = input('sort', 0);
            $status = input('status', 1);

            if (empty($category_name)) {
                return error(-1, '分类名称不能为空');
            }

            if ($category_id <= 0) {
                return error(-1, '分类ID无效');
            }

            // 检查同级分类名称是否重复（排除自己）
            $exist_count = model('prescription_template_category')->getCount([
                ['category_name', '=', $category_name],
                ['parent_id', '=', $parent_id],
                ['category_id', '<>', $category_id],
                ['site_id', '=', $this->site_id]
            ]);

            if ($exist_count > 0) {
                return error(-1, '同级分类中已存在相同名称的分类');
            }

            $data = [
                'category_id' => $category_id,
                'category_name' => $category_name,
                'parent_id' => $parent_id,
                'category_image' => $category_image,
                'sort' => $sort,
                'status' => $status,
                'site_id' => $this->site_id
            ];
            
            $category_model = new PrescriptionCategoryModel();
            $res = $category_model->editCategory($data);
            
            if ($res['code'] >= 0) {
                return success(0, '编辑成功', $res['data']);
            } else {
                return error(-1, $res['message']);
            }
        } else {
            $category_id = input('category_id', 0);
            $category_info = model('prescription_template_category')->getInfo([['category_id', '=', $category_id], ['site_id', '=', $this->site_id]]);

            if (empty($category_info)) {
                return error(-1, '分类不存在');
            }

            $this->assign('category_info', $category_info);
            return $this->fetch('orderattr/category_edit');
        }
    }

    /**
     * 删除分类
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $category_id = input('category_id', 0);
            
            if ($category_id <= 0) {
                return error(-1, '分类ID无效');
            }
            
            $category_model = new PrescriptionCategoryModel();
            $res = $category_model->deleteCategory($category_id, $this->site_id);
            
            if ($res['code'] >= 0) {
                return success(0, '删除成功');
            } else {
                return error(-1, $res['message']);
            }
        }
    }

    /**
     * 修改排序
     */
    public function modifySort()
    {
        if (request()->isAjax()) {
            $sort = input('sort', 0);
            $category_id = input('category_id', 0);
            
            if ($category_id <= 0) {
                return error(-1, '分类ID无效');
            }
            
            $category_model = new PrescriptionCategoryModel();
            $res = $category_model->modifySort($sort, $category_id, $this->site_id);
            
            if ($res['code'] >= 0) {
                return success(0, '修改成功');
            } else {
                return error(-1, $res['message']);
            }
        }
    }

    /**
     * 获取分类选项（用于下拉框）
     */
    public function getOptions()
    {
        if (request()->isAjax()) {
            $level = input('level', 0);
            
            $category_model = new PrescriptionCategoryModel();
            $options = $category_model->getCategoryOptions($this->site_id, $level);
            
            return success(0, 'SUCCESS', $options);
        }
    }

    /**
     * 获取分类树形结构
     */
    public function getTree()
    {
        if (request()->isAjax()) {
            $category_model = new PrescriptionCategoryModel();
            $tree = $category_model->getCategoryTree($this->site_id);
            
            return success(0, 'SUCCESS', $tree);
        }
    }

    /**
     * 获取分类详情
     */
    public function getInfo()
    {
        if (request()->isAjax()) {
            $category_id = input('category_id', 0);
            
            if ($category_id <= 0) {
                return error(-1, '分类ID无效');
            }
            
            $category_info = model('prescription_template_category')->getInfo([['category_id', '=', $category_id], ['site_id', '=', $this->site_id]]);
            
            if (empty($category_info)) {
                return error(-1, '分类不存在');
            }
            
            return success(0, 'SUCCESS', $category_info);
        }
    }

    /**
     * 初始化数据库表
     */
    public function initDatabase()
    {
        if (request()->isAjax()) {
            try {
                // 创建分类表
                $sql = "CREATE TABLE IF NOT EXISTS `ns_prescription_template_category` (
                  `category_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级分类ID',
                  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
                  `category_image` varchar(255) NOT NULL DEFAULT '' COMMENT '分类图片',
                  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示(0:隐藏,1:显示)',
                  `level` tinyint(2) NOT NULL DEFAULT '1' COMMENT '分类级别',
                  `category_path` varchar(500) NOT NULL DEFAULT '' COMMENT '分类路径',
                  `site_id` int(11) NOT NULL COMMENT '站点ID',
                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  PRIMARY KEY (`category_id`),
                  KEY `idx_parent_id` (`parent_id`),
                  KEY `idx_site_id` (`site_id`),
                  KEY `idx_sort` (`sort`),
                  KEY `idx_level` (`level`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='处方模板分类表'";

                \think\facade\Db::execute($sql);

                // 检查并添加字段到处方模板表
                $columns = \think\facade\Db::query("SHOW COLUMNS FROM ns_order_attr_class LIKE 'category_id'");
                if (empty($columns)) {
                    \think\facade\Db::execute("ALTER TABLE `ns_order_attr_class` ADD COLUMN `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID' AFTER `class_name`");
                }

                $columns = \think\facade\Db::query("SHOW COLUMNS FROM ns_order_attr_class LIKE 'header_image'");
                if (empty($columns)) {
                    \think\facade\Db::execute("ALTER TABLE `ns_order_attr_class` ADD COLUMN `header_image` varchar(255) NOT NULL DEFAULT '' COMMENT '头图路径' AFTER `age_type`");
                }

                $columns = \think\facade\Db::query("SHOW COLUMNS FROM ns_order_attr_class LIKE 'rich_content'");
                if (empty($columns)) {
                    \think\facade\Db::execute("ALTER TABLE `ns_order_attr_class` ADD COLUMN `rich_content` text COMMENT '富文本详情内容' AFTER `header_image`");
                }



                // 插入默认分类数据
                $exist_count = \think\facade\Db::name('prescription_template_category')->where('site_id', $this->site_id)->count();
                if ($exist_count == 0) {
                    $default_categories = [
                        ['parent_id' => 0, 'category_name' => '默认分类', 'sort' => 0, 'status' => 1, 'level' => 1, 'category_path' => '1', 'site_id' => $this->site_id],
                        ['parent_id' => 0, 'category_name' => '养肝类', 'sort' => 10, 'status' => 1, 'level' => 1, 'category_path' => '2', 'site_id' => $this->site_id],
                        ['parent_id' => 0, 'category_name' => '养胃类', 'sort' => 20, 'status' => 1, 'level' => 1, 'category_path' => '3', 'site_id' => $this->site_id],
                        ['parent_id' => 0, 'category_name' => '补肾类', 'sort' => 30, 'status' => 1, 'level' => 1, 'category_path' => '4', 'site_id' => $this->site_id],
                        ['parent_id' => 0, 'category_name' => '清热类', 'sort' => 40, 'status' => 1, 'level' => 1, 'category_path' => '5', 'site_id' => $this->site_id],
                    ];

                    \think\facade\Db::name('prescription_template_category')->insertAll($default_categories);
                }

                return success(0, '数据库初始化成功');
            } catch (\Exception $e) {
                return error(-1, '数据库初始化失败：' . $e->getMessage());
            }
        }
    }

    /**
     * 测试数据库字段
     */
    public function testFields()
    {
        try {
            // 测试查询分类信息
            $category_info = model('prescription_template_category')->getInfo([['site_id', '=', $this->site_id]], '*');

            if (empty($category_info)) {
                return json(['code' => 0, 'message' => '暂无分类数据', 'data' => []]);
            }

            return json(['code' => 0, 'message' => '查询成功', 'data' => $category_info]);
        } catch (\Exception $e) {
            return json(['code' => -1, 'message' => '查询失败：' . $e->getMessage()]);
        }
    }


}
