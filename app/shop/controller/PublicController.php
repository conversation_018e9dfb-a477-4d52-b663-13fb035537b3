<?php

namespace app\shop\controller;

use think\facade\Db;

class PublicController extends \app\Controller
{
    /**
     * 问卷填写页面 (User Survey Filling Page)
     * 通过唯一token加载问卷模板结构和检查分配状态，并在服务器端渲染数据到视图
     *
     * @return \think\response\View
     */
    public function fill()
    {
        // 初始化视图变量，确保在所有情况下都能安全地在视图中使用它们
        $this->assign('error_msg', null);
        $this->assign('status_message', null);
        $this->assign('token', null);
        $this->assign('assignment', null);
        $this->assign('template', null);
        $this->assign('items', null);

        try {
            // 1. 获取 URL 中的 token 参数
            $token = input('token', '');
            $this->assign('token', $token); // 无论是否为空，都先赋值，方便视图获取

            // 2. 验证 token 是否为空
            if (empty($token)) {
                $this->assign('error_msg', '问卷链接无效');
                // Token 无效，设置错误信息后代码继续，由方法末尾统一渲染视图
                // return $this->fetch(...) 在这里不再需要
            } else {
                // token 不为空时的逻辑

                // 3. 根据 token 查找分配记录 (ns_survey_assignments)
                // 确保表名正确 'ns_survey_assignments'
                $assignment = Db::name('survey_assignments')->where([['unique_link_token', '=', $token]])->find();

                // 将分配记录赋值过去，无论是否找到，前端可能需要其信息（如Token）
                $this->assign('assignment', $assignment);

                // 4. 检查分配记录是否存在
                if (empty($assignment)) {
                    $this->assign('error_msg', '问卷链接无效或已失效');
                    // 分配记录不存在，设置错误信息后代码继续
                    // return $this->fetch(...) 在这里不再需要
                } else {
                    // 找到分配记录时的逻辑

                    // 5. 获取分配记录的状态
                    $status = $assignment['status'];

                    // 6. 检查状态是否不允许填写（即状态 2:已完成, 3:已过期, 4:已取消）
                    // 根据ns_survey_assignments表结构
                    if ($status == 2 || $status == 3 || $status == 4) {
                        // 根据状态设置不同的提示信息
                        $statusMessage = '';
                        if ($status == 2) $statusMessage = '该问卷已完成，感谢您的填写！';
                        else if ($status == 3) $statusMessage = '问卷已过期，无法填写。';
                        else if ($status == 4) $statusMessage = '问卷已取消，无法填写。';
                        else $statusMessage = '问卷状态异常，无法填写。'; // 处理其他未知不允许状态

                        // 将状态信息赋值到视图，前端模板将显示状态信息而不是问卷表单
                        $this->assign('status_message', $statusMessage);

                        // 这里不 return fetch，让代码继续到 try 块末尾统一返回视图
                        // 前端视图将根据是否存在 error_msg 或 status_message 来判断显示什么
                    } else {
                        // 状态允许填写 (0:待填写 或 1:已打开) 时的逻辑

                        // 7. 获取模板ID (从分配记录中获取)
                        $templateId = $assignment['template_id'];

                        // 8. 获取问卷模板基本信息 (ns_survey_templates)
                        // 确保表名正确 'ns_survey_templates'
                        $template = Db::name('survey_templates')->where([['id', '=', $templateId]])->find();

                        // 9. 检查问卷模板是否存在
                        if (empty($template)) {
                            // 理论上在分配时已经验证过模板，但这里为了健壮性再检查一次
                            $this->assign('error_msg', '问卷模板不存在或已删除');
                            // 这里不 return fetch，让代码继续到 try 块末尾统一返回视图
                            // 前端视图将显示 error_msg
                        } else {
                             // 将模板基本信息赋值到视图
                            $this->assign('template', $template);

                            // 10. 找到问卷模板，获取问卷模板项目（题目和章节） (ns_survey_template_items)
                            // 确保表名正确 'ns_survey_template_items'
                            // 获取所有必要字段，特别是 settings_json 用于题目的选项/设置
                            $items = Db::name('survey_template_items')
                                ->where([['template_id', '=', $templateId]])
                                ->order('item_order ASC') // 按顺序排序
                                ->select()
                                ->toArray(); // 转换为数组

                            // 11. 将项目列表赋值到视图
                            $this->assign('items', $items);

                            // 可以在这里遍历 $items，解析 settings_json，或者让前端解析
                            // 前端解析 settings_json 通常更灵活
                        }
                    }
                }
            }

            // 12. 在所有检查通过、数据赋值完成后，或者遇到不允许的状态/模板不存在的情况，最终渲染视图
            // 前端视图将根据是否存在 error_msg, status_message, template 和 items 来判断显示什么内容
            // 如果 error_msg 或 status_message 不为 null，则显示错误/状态信息
            // 如果 error_msg 和 status_message 都为 null 且 template 和 items 不为 null，则显示问卷表单
            return $this->fetch('journeysurvey/survey_fill');

        } catch (\Exception $e) {
            // 13. 处理异常，记录日志，并向视图赋值通用错误信息后渲染视图
            trace('加载问卷填写页面失败: ' . $e->getMessage(), 'error');
            $this->assign('error_msg', '系统错误，无法加载问卷');
            // 如果在抛出异常前已经获取到 assignment 信息，并且需要在错误页面显示，可以尝试赋值过去
             if (isset($assignment)) { $this->assign('assignment', $assignment); }
            return $this->fetch('journeysurvey/survey_fill');
        }
    }

    // 未来其他无需登录的方法可以添加到这里
}