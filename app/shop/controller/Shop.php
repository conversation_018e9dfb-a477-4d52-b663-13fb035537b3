<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\shop\Shop as ShopModel;
use app\model\system\Address as AddressModel;
use app\model\system\Site;
use app\model\system\User as UserModel;
use think\facade\Db;

/**
 * 店铺
 * Class Shop
 * @package app\shop\controller
 */
class Shop extends BaseShop
{


    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();
    }

    /**
     * 店铺设置
     * @return mixed
     */
    public function config()
    {
        $shop_model = new ShopModel();
        $site_model = new Site();
        $condition  = array(
            ["site_id", "=", $this->site_id]
        );
        if (request()->isAjax()) {
            $site_name       = input('site_name', '');
            $logo            = input("logo", '');//店铺logo
            $avatar          = input("avatar", '');//店铺头像（大图）
            $banner          = input("banner", '');//店铺条幅
            $seo_keywords    = input("seo_keywords", '');//店铺关键字
            $seo_description = input("seo_description", '');//店铺简介
            $qq              = input("qq", '');//qq
            $ww              = input("ww", '');//ww
            $telephone       = input("telephone", '');//联系电话
            $data_site       = array(
                "site_name"       => $site_name,
                "logo"            => $logo,
                "seo_keywords"    => $seo_keywords,
                "seo_description" => $seo_description
            );

            $work_week  = input("work_week", '');//工作日  例如 : 1,2,3,4,5,6,7
            $start_time = input("start_time", 0);//开始时间
            $end_time   = input("end_time", 0);//结束时间

            $data_shop = array(
                "avatar"     => $avatar,
                "banner"     => $banner,
                "qq"         => $qq,
                "ww"         => $ww,
                "telephone"  => $telephone,
                "work_week"  => $work_week,
                "start_time" => $start_time,
                "end_time"   => $end_time,
            );
            $site_model->editSite($data_site, $condition);
            $res = $shop_model->editShop($data_shop, $condition);
            return $res;
        } else {

            $shop_info_result = $shop_model->getShopInfo($condition);
            $site_info        = $site_model->getSiteInfo($condition);
            $shop_info        = array_merge($shop_info_result["data"], $site_info['data']);
            $this->assign("shop_info", $shop_info);
            return $this->fetch("shop/config");
        }

    }

    public function papers()
    {
        return $this->fetch("shop/papers");
    }

    /**
     * 联系方式
     * @return mixed
     */
    public function contact()
    {
        $sor_list = Db::connect('v3')->name('sor')->where('customer=2')->select()->toArray();
        $sell_list = Db::connect('v3')->name('sell')->where('customer=2')->select()->toArray();
        $stock_in = model('stock_in')->getList([],'*');
        $stock_in = array_column($stock_in,'package_no');
        $sor = [];
        $state = [0=>'未出库',1=>'部分出库',2=>'已出库',3=>'关闭'];
        foreach ($sor_list as $k=>$vo){
            $vo['timee'] = $vo['time'];
            $vo['time'] = date('Y-m-d',$vo['time']);
            $vo['state'] = $state[$vo['state']];
            $sor[$vo['id']] = $vo;
        }
        foreach($sell_list as $k=>$vo){
            if (isset($sor[$vo['source']])){
                $vo['timee'] = $vo['time'];
                $vo['time'] = date('Y-m-d',$vo['time']);
                $vo['ruku'] = in_array($vo['number'],$stock_in)?1:0;
                $sor[$vo['source']]['child'][]=$vo;
            }else{
                $sor[$vo['number']] = ['id'=>'-','time'=>'-','number'=>'-','state'=>'-','data'=>'-'];
                $vo['timee'] = $vo['time'];
                $vo['time'] = date('Y-m-d',$vo['time']);
                $vo['ruku'] = in_array($vo['number'],$stock_in)?1:0;
                $sor[$vo['number']]['child'][]=$vo;
            }
        }
        $user_model = new UserModel();
        foreach ($sor as $k=>$vo){
            if (isset($vo['child'])){
                $sorted = $user_model->array_orderby($vo['child'], 'timee', SORT_DESC);
                $sor[$k]['child'] = $sorted;
                $sor[$k]['timee'] = $sorted[0]['timee'];
            }else{
                $sor[$k]['child'] = [];
            }
        }
        $sorted = $user_model->array_orderby($sor, 'timee', SORT_DESC);
        $sor = $sorted;
        $this->assign("sor", $sor);
        return $this->fetch("shop/contact");

    }

    /**
     * 店铺推广
     * return
     */
    public function shopUrl()
    {
        //获取商品sku_id
        $shop_model = new ShopModel();
        $res        = $shop_model->qrcode($this->site_id);
        // dump($res);exit;
        return $res;
    }

    public function video1()
    {
        return $this->fetch("shop/video1");
    }

    public function video2()
    {
        return $this->fetch("shop/video2");
    }

    public function play()
    {
        $filepath = input('id', 1);
        $filepath = $filepath==1?'shiyon.mp4':'kaixiang.mp4';
        ini_set('memory_limit','512M');
        header('Pragma:public');
        header('Expires:0');
        header('Content-Type:application/octet-stream');
        ob_clean();
        flush();
        readfile($filepath);
    }

}