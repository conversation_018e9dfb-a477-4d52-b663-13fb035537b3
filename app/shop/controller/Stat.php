<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\system\Stat as StatModel;
use app\model\system\User as UserModel;
use app\model\order\OrderCommon as OrderCommonModel;


/**
 * 数据统计
 * Class Stat
 * @package app\shop\controller
 */
class Stat extends BaseShop
{

    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();

    }

    /**
     * 店铺统计
     * @return mixed
     */
    public function shop()
    {
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $search = input('search', '');
        $condition   = [];
        $condition[] = ['group_id','=',2];

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        $user_model = new UserModel();
        $datau      = $user_model->bpdoctordata();
        if ($user['group_id'] == 2){
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
            }else{
                $datu = [];
                $datu[] = $this->uid;
            }
            $condition[] = ["uid", "in", $datu];
        }

        if ($search){
            $usersearch = model('user')->getList([['username', "like", '%' . $search . '%']]);
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            $uidsearch=[];
            foreach ($usersearch as $key=>$vo){
                if (isset($vo['uid']) && isset($user_bpdotocr[$vo['uid']])){
                    if ($vo['type_id']==1){
                        if (isset($datau[$vo['uid']])){
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['bp']);
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['doctor']);
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['patient']);
                        }else{
                            $uidsearch[] = $vo['uid'];
                        }
                    }else{
                        $uidsearch[]=$vo['uid'];
                        $uidsearch[]=$user_bpdotocr[$vo['uid']]['bp'];
                    }

                }
            }
            if ($uidsearch){
                $condition[] = ["uid", "in", $uidsearch];
            }
        }

        $user_model = new UserModel();
        $list       = $user_model->getUserList($condition, '*', "create_time desc");
        $user_bpdotocr = $user_model->bpdoctorList();
        foreach ($list['data'] as $key =>$vo){
            $vo['group_name'] = '';
            $list['data'][$key]['agent_username'] = '';
            if ($user_bpdotocr[$vo['uid']]['bp']){
                $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['bp'] = $user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['bp'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['doctor']){
                $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['doctor'] = $user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['doctor'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['patient']){
                $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['patient'] = $user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['patient'] = '';
            }
            $list['data'][$key]['group_name'] = $vo['group_name'];
            $condition   = [];
            $member_condition   = [];
            if (isset($datau[$vo['uid']])){
                $list['data'][$key]['doctorcount'] =count(array_unique($datau[$vo['uid']]['doctor']))+0;
                $list['data'][$key]['patientcount'] =count(array_unique($datau[$vo['uid']]['patient']))+0;
                $datu       = array_merge($datau[$vo['uid']]['bp'],$datau[$vo['uid']]['doctor']);
                $datu       = array_merge($datu,$datau[$vo['uid']]['patient']);
                if ($datu && $list['data'][$key]['type_id'] == 1){
                    $condition[] = ["admin_uid", "in", $datu];
                    $member_condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $vo['uid']];
                    $member_condition[] = ["user_id", "=", $vo['uid']];
                }

            }
            $condition[]=['order_status','>','0'];
            $condition[]=['create_time','>',strtotime($reg_start_date)];
            $condition[]=['create_time','<',strtotime($reg_end_date)];
            $member_condition[]=['status','=','1'];
            $count = model('order')->getCount($condition);
            $sum = model('order')->getSum($condition, 'order_money');
            $list['data'][$key]['count']=$count;
            $list['data'][$key]['sum']=$sum;
            $list['data'][$key]['member_count'] = model('member')->getCount($member_condition);

        }
        $sorted = $list['data'];
        $sorted = $user_model->array_orderby($sorted, 'bp', SORT_DESC, 'doctor', SORT_ASC);
        $list = $sorted;
        $result = [];
        $count_all = $sum_all = $member_count_all = $doctor_all = $bp_all = 0;
        foreach ($list as $key=>$vo){
            if ($vo['bp']){
                if (isset($result[$vo['bp']])){
                    $result[$vo['bp']]['doctor'][]=$vo;
                    $doctor_all = $doctor_all+1;
                }else{
                    $result[$vo['bp']]=$vo;
                    $result[$vo['bp']]['doctor'] = [];
                    //     $result[$vo['bp']]['doctor'][]=$vo;
                    $bp_all = $bp_all+1;
                    $member_count_all = $member_count_all + $vo['member_count'];
                    $sum_all = $sum_all + $vo['sum'];
                    $count_all = $count_all + $vo['count'];

                }
            }else{
                $result['a'.$key]=$vo;
                $result['a'.$key]['doctor']=[];
                $result['a'.$key]['doctor'][]=$vo;
                $doctor_all = $doctor_all+1;
                $member_count_all = $member_count_all + $vo['member_count'];
                $sum_all = $sum_all + $vo['sum'];
                $count_all = $count_all + $vo['count'];
            }
        }
        //机构没有医生的情况
        foreach ($result as $key=>$vo){
            if (empty($vo['doctor'])){
                $result[$key]['doctor'][]=['username'=>$vo['bp'],'count'=>$vo['count'],'sum'=>$vo['sum'],'member_count'=>$vo['member_count']];
                $result[$key]['doctor_count']=0;
            }else {
                $result[$key]['doctor_count'] = count($vo['doctor']) + 0;
                if ($vo['bp']) {
                    $d_ordercount = array_column($vo['doctor'], 'count');
                    $d_pricesum = array_column($vo['doctor'], 'sum');
                    $d_patientnum = array_column($vo['doctor'], 'member_count');
                    $s_ordercount = $vo['count'] - array_sum($d_ordercount);
                    $s_pricesum = $vo['sum'] - array_sum($d_pricesum);
                    $s_patientnum = $vo['member_count'] - array_sum($d_patientnum);
                    array_unshift($result[$key]['doctor'], ['username' => $vo['bp'], 'count' => $s_ordercount, 'sum' => $s_pricesum, 'member_count' => $s_patientnum]);
                }
            }
        }
        $sorted = $user_model->array_orderby($result, 'count', SORT_DESC, 'sum', SORT_DESC);
        $result = $sorted;
        $this->assign("reg_start_date", strtotime($reg_start_date)*1000);
        $this->assign("reg_end_date", strtotime($reg_end_date)*1000);
        $this->assign("search", $search);
        $this->assign("doctor_all", $doctor_all);
        $this->assign("bp_all", $bp_all);
        $this->assign("sum_all", $sum_all);
        $this->assign("member_count_all", $member_count_all);
        $this->assign("count_all", $count_all);
        $this->assign("list", $result);

        return $this->fetch("stat/shop");

    }

    public function exportshop()
    {
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $search = input('search', '');
        $condition   = [];
        $condition[] = ['group_id','=',2];

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        $user_model = new UserModel();
        $datau      = $user_model->bpdoctordata();
        if ($user['group_id'] == 2){
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
            }else{
                $datu = [];
                $datu[] = $this->uid;
            }
            $condition[] = ["uid", "in", $datu];
        }

        if ($search){
            $usersearch = model('user')->getList([['username', "like", '%' . $search . '%']]);
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            $uidsearch=[];
            foreach ($usersearch as $key=>$vo){
                if (isset($vo['uid']) && isset($user_bpdotocr[$vo['uid']])){
                    if ($vo['type_id']==1){
                        if (isset($datau[$vo['uid']])){
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['bp']);
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['doctor']);
                            $uidsearch       = array_merge($uidsearch,$datau[$vo['uid']]['patient']);
                        }else{
                            $uidsearch[] = $vo['uid'];
                        }
                    }else{
                        $uidsearch[]=$vo['uid'];
                        $uidsearch[]=$user_bpdotocr[$vo['uid']]['bp'];
                    }

                }
            }
            if ($uidsearch){
                $condition[] = ["uid", "in", $uidsearch];
            }
        }

        $user_model = new UserModel();
        $list       = $user_model->getUserList($condition, '*', "create_time desc");
        $user_bpdotocr = $user_model->bpdoctorList();
        foreach ($list['data'] as $key =>$vo){
            $vo['group_name'] = '';
            $list['data'][$key]['agent_username'] = '';
            if ($user_bpdotocr[$vo['uid']]['bp']){
                $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['bp'] = $user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['bp'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['doctor']){
                $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['doctor'] = $user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['doctor'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['patient']){
                $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['patient'] = $user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['patient'] = '';
            }
            $list['data'][$key]['group_name'] = $vo['group_name'];
            $condition   = [];
            $member_condition   = [];
            if (isset($datau[$vo['uid']])){
                $list['data'][$key]['doctorcount'] =count(array_unique($datau[$vo['uid']]['doctor']))+0;
                $list['data'][$key]['patientcount'] =count(array_unique($datau[$vo['uid']]['patient']))+0;
                $datu       = array_merge($datau[$vo['uid']]['bp'],$datau[$vo['uid']]['doctor']);
                $datu       = array_merge($datu,$datau[$vo['uid']]['patient']);
                if ($datu && $list['data'][$key]['type_id'] == 1){
                    $condition[] = ["admin_uid", "in", $datu];
                    $member_condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $vo['uid']];
                    $member_condition[] = ["user_id", "=", $vo['uid']];
                }

            }
            $condition[]=['order_status','>','0'];
            $condition[]=['create_time','>',strtotime($reg_start_date)];
            $condition[]=['create_time','<',strtotime($reg_end_date)];
            $member_condition[]=['status','=','1'];
            $count = model('order')->getCount($condition);
            $sum = model('order')->getSum($condition, 'order_money');
            $list['data'][$key]['count']=$count;
            $list['data'][$key]['sum']=$sum;
            $list['data'][$key]['member_count'] = model('member')->getCount($member_condition);

        }
        $sorted = $list['data'];
        $sorted = $user_model->array_orderby($sorted, 'bp', SORT_DESC, 'doctor', SORT_ASC);
        $list = $sorted;
        $result=[];
        foreach ($list as $key=>$vo){
            if ($vo['bp']){
                if (isset($result[$vo['bp']])){
                    $result[$vo['bp']]['doctor'][]=$vo;
                }else{
                    $result[$vo['bp']]=$vo;
                    $result[$vo['bp']]['doctor'] = [];
                    //     $result[$vo['bp']]['doctor'][]=$vo;
                }
            }else{
                $result['a'.$key]=$vo;
                $result['a'.$key]['doctor']=[];
                $result['a'.$key]['doctor'][]=$vo;
            }
        }
        //机构没有医生的情况
        foreach ($result as $key=>$vo){
            if (empty($vo['doctor'])){
                $result[$key]['doctor'][]=['username'=>$vo['bp'],'count'=>$vo['count'],'sum'=>$vo['sum'],'member_count'=>$vo['member_count'],'pay_close'=>$vo['pay_close'],'isbalance'=>$vo['isbalance'],'balancediscount'=>$vo['balancediscount']];
                $result[$key]['doctor_count']=0;
            }else{
                $result[$key]['doctor_count'] = count($vo['doctor']) + 0;
                if ($vo['bp']) {
                    $d_ordercount = array_column($vo['doctor'], 'count');
                    $d_pricesum = array_column($vo['doctor'], 'sum');
                    $d_patientnum = array_column($vo['doctor'], 'member_count');
                    $s_ordercount = $vo['count'] - array_sum($d_ordercount);
                    $s_pricesum = $vo['sum'] - array_sum($d_pricesum);
                    $s_patientnum = $vo['member_count'] - array_sum($d_patientnum);
                    array_unshift($result[$key]['doctor'], ['username' => $vo['bp'], 'count' => $s_ordercount, 'sum' => $s_pricesum, 'member_count' => $s_patientnum,'pay_close'=>$vo['pay_close'],'isbalance'=>$vo['isbalance'],'balancediscount'=>$vo['balancediscount']]);
                }
            }
        }
        $sorted = $user_model->array_orderby($result, 'count', SORT_DESC, 'sum', SORT_DESC);
        $result = $sorted;

        $list = [];
        foreach($result as $key=>$vo){
            foreach ($vo['doctor'] as $kk=>$vv){
                if ($kk==0){
                    $list[]= [
                        'agent_username'=>$vo['agent_username'],
                        'bp'=>$vo['bp'],
                        'bp_count'=>$vo['count'],
                        'bp_sum'=>$vo['sum'],
                        'bp_member_count'=>$vo['member_count'],
                        'doctor_count'=>$vo['doctor_count'],
                        'pay_close'=>$vo['pay_close'],
                        'isbalance'=>$vo['isbalance'],
                        'balancediscount'=>$vo['balancediscount'],
                        'doctor'=>$vv['username'],
                        'sum'=>$vv['count'],
                        'pricesum'=>$vv['sum'],
                        'member_count'=>$vv['member_count'],
                        'dpay_close'=>$vv['pay_close'],
                        'disbalance'=>$vv['isbalance'],
                        'dbalancediscount'=>$vv['balancediscount']
                    ];
                }else{
                    $list[]= [
                        'agent_username'=>'',
                        'bp'=>'',
                        'bp_count'=>'',
                        'bp_sum'=>'',
                        'bp_member_count'=>'',
                        'doctor_count'=>'',
                        'pay_close'=>'',
                        'isbalance'=>'',
                        'balancediscount'=>'',
                        'doctor'=>$vv['username'],
                        'sum'=>$vv['count'],
                        'pricesum'=>$vv['sum'],
                        'member_count'=>$vv['member_count'],
                        'dpay_close'=>$vv['pay_close'],
                        'disbalance'=>$vv['isbalance'],
                        'dbalancediscount'=>$vv['balancediscount']
                    ];
                }

            }
        }

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("机构统计");
        $phpExcel->getProperties()->setSubject("机构统计");
        // 对单元格设置居中效果
        $phpExcel->getActiveSheet()->getStyle('A')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('B')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('C')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('D')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('E')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('F')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('G')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('H')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('I')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('J')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('K')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('L')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('M')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('N')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('O')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('P')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        $phpExcel->getActiveSheet()->setCellValue('A1', '产品经理');
        $phpExcel->getActiveSheet()->setCellValue('B1', '机构');//可以指定位置
        $phpExcel->getActiveSheet()->setCellValue('C1', '机构订单数量');
        $phpExcel->getActiveSheet()->setCellValue('D1', '机构订单金额');
        $phpExcel->getActiveSheet()->setCellValue('E1', '机构用户数量');
        $phpExcel->getActiveSheet()->setCellValue('F1', '医生数量');
        $phpExcel->getActiveSheet()->setCellValue('G1', '线下支付');
        $phpExcel->getActiveSheet()->setCellValue('H1', '余额支付');
        $phpExcel->getActiveSheet()->setCellValue('I1', '折扣');
        $phpExcel->getActiveSheet()->setCellValue('J1', '医生');
        $phpExcel->getActiveSheet()->setCellValue('K1', '订单数量');
        $phpExcel->getActiveSheet()->setCellValue('L1', '订单金额');
        $phpExcel->getActiveSheet()->setCellValue('M1', '用户数量');
        $phpExcel->getActiveSheet()->setCellValue('N1', '线下支付');
        $phpExcel->getActiveSheet()->setCellValue('O1', '余额支付');
        $phpExcel->getActiveSheet()->setCellValue('P1', '折扣');
        //循环添加数据（根据自己的逻辑）
        foreach ($list as $k => $v) {
            $i = $k + 2;
            $phpExcel->getActiveSheet()->setCellValue('A' . $i, $v['agent_username']);
            $phpExcel->getActiveSheet()->setCellValue('B' . $i, $v['bp']);
            $phpExcel->getActiveSheet()->setCellValue('C' . $i, $v['bp_count']);
            $phpExcel->getActiveSheet()->setCellValue('D' . $i, $v['bp_sum']);
            $phpExcel->getActiveSheet()->setCellValue('E' . $i, $v['bp_member_count']);
            $phpExcel->getActiveSheet()->setCellValue('F' . $i, $v['doctor_count']);
            $phpExcel->getActiveSheet()->setCellValue('G' . $i, $v['pay_close']);
            $phpExcel->getActiveSheet()->setCellValue('H' . $i, $v['isbalance']);
            $phpExcel->getActiveSheet()->setCellValue('I' . $i, $v['balancediscount']);
            $phpExcel->getActiveSheet()->setCellValue('J' . $i, $v['doctor']);
            $phpExcel->getActiveSheet()->setCellValue('K' . $i, $v['sum']);
            $phpExcel->getActiveSheet()->setCellValue('L' . $i, $v['pricesum']);
            $phpExcel->getActiveSheet()->setCellValue('M' . $i, $v['member_count']);
            $phpExcel->getActiveSheet()->setCellValue('N' . $i, $v['dpay_close']);
            $phpExcel->getActiveSheet()->setCellValue('O' . $i, $v['disbalance']);
            $phpExcel->getActiveSheet()->setCellValue('P' . $i, $v['dbalancediscount']);
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('机构统计');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('年度统计', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }

    /**
     * 店铺统计报表
     * */
    public function getShopStatList()
    {
        if (request()->isAjax()) {
            $date_type = input('date_type', 1);

            if ($date_type == 1) {
                $start_time = strtotime(date('Y-m-d', strtotime("-6 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 6;
            } else if ($date_type == 2) {
                $start_time = strtotime(date('Y-m-d', strtotime("-29 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 29;
            }

            $stat_model = new StatModel();

            $stat_list = $stat_model->getShopStatList($this->site_id, $start_time);

            //将时间戳作为列表的主键
            $shop_stat_list = array_column($stat_list['data'], null, 'day_time');

            $data = array();

            for ($i = 0; $i <= $day; $i++) {
                $time             = strtotime(date('Y-m-d', strtotime("-" . ($day - $i) . " day")));
                $data['time'][$i] = date('Y-m-d', $time);
                if (array_key_exists($time, $shop_stat_list)) {
                    $data['order_total'][$i]     = $shop_stat_list[$time]['order_total'];
                    $data['shipping_total'][$i]  = $shop_stat_list[$time]['shipping_total'];
                    $data['refund_total'][$i]    = $shop_stat_list[$time]['refund_total'];
                    $data['order_pay_count'][$i] = $shop_stat_list[$time]['order_pay_count'];
                    $data['goods_pay_count'][$i] = $shop_stat_list[$time]['goods_pay_count'];
                    $data['shop_money'][$i]      = $shop_stat_list[$time]['shop_money'];
                    $data['platform_money'][$i]  = $shop_stat_list[$time]['platform_money'];
                    $data['collect_shop'][$i]    = $shop_stat_list[$time]['collect_shop'];
                    $data['collect_goods'][$i]   = $shop_stat_list[$time]['collect_goods'];
                    $data['visit_count'][$i]     = $shop_stat_list[$time]['visit_count'];
                    $data['order_count'][$i]     = $shop_stat_list[$time]['order_count'];
                    $data['goods_count'][$i]     = $shop_stat_list[$time]['goods_count'];
                    $data['add_goods_count'][$i] = $shop_stat_list[$time]['add_goods_count'];
                    $data['member_count'][$i]    = $shop_stat_list[$time]['member_count'];
                } else {
                    $data['order_total'][$i]     = 0.00;
                    $data['shipping_total'][$i]  = 0.00;
                    $data['refund_total'][$i]    = 0.00;
                    $data['order_pay_count'][$i] = 0;
                    $data['goods_pay_count'][$i] = 0;
                    $data['shop_money'][$i]      = 0.00;
                    $data['platform_money'][$i]  = 0.00;
                    $data['collect_shop'][$i]    = 0;
                    $data['collect_goods'][$i]   = 0;
                    $data['visit_count'][$i]     = 0;
                    $data['order_count'][$i]     = 0;
                    $data['goods_count'][$i]     = 0;
                    $data['add_goods_count'][$i] = 0;
                    $data['member_count'][$i]    = 0;
                }
            }

            $data['time_range'] = $time_range;

            return $data;
        }
    }

    /**
     * 商品统计
     * @return mixed
     */
    public function goods()
    {
        /*$order_common_model = new OrderCommonModel();
        $order       = $order_common_model->getOrderPageList([["admin_uid", "=", 341]],1,100,'','*',0,'og.sku_id,og.order_id,og.sku_name,og.num,o.goods_chi');
        print_r($order);
        exit;
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctorList();
        print_r($user_bpdotocr);
        exit;*/
        $bp  = input('bp',0);
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $condition   = [];
        $condition[]=['o.order_status','>','0'];
        $condition[]=['o.pay_time','>',strtotime($reg_start_date)];
        $condition[]=['o.pay_time','<',strtotime($reg_end_date)];
        if ($bp){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$bp])){
                $datu       = array_merge($datau[$bp]['bp'],$datau[$bp]['doctor']);
                $datu       = array_merge($datu,$datau[$bp]['patient']);
            }else{
                $datu = [];
                $datu[] = $bp;
            }
            $condition[] = ["o.admin_uid", "in", $datu];
        }
        $order_common_model = new OrderCommonModel();
        $order       = $order_common_model->getOrderGoodsDetailGroupList($condition);

        $alias       = 'og';
        $join        = [
            [
                'goods_stock_log o',
                'o.goods_id = og.goods_id',
                'left'
            ]
        ];
        $order_goods_field = 'o.type,o.goods_id,sum(o.old_stock) as old_stock_total,sum(o.new_stock) as new_stock_total';
        $condition   = [];
        $condition[]=['o.time','>',strtotime($reg_start_date)];
        $condition[]=['o.time','<',strtotime($reg_end_date)];
        $list = model('goods')->getList($condition,   $order_goods_field, 'og.goods_id desc', $alias, $join,'o.goods_id,o.type');
        $goods_stock_log=[];
        foreach($list as $key=>$vo){
            $goods_stock_log[$vo['goods_id']][1]=0;
            $goods_stock_log[$vo['goods_id']][2]=0;
            $goods_stock_log[$vo['goods_id']][3]=0;
            $goods_stock_log[$vo['goods_id']][4]=0;
            $goods_stock_log[$vo['goods_id']][5]=0;
        }
        foreach($list as $key=>$vo){
            $goods_stock_log[$vo['goods_id']][$vo['type']]=($vo['new_stock_total']-$vo['old_stock_total'])<0?($vo['new_stock_total']-$vo['old_stock_total'])*-1:($vo['new_stock_total']-$vo['old_stock_total']);
        }
        $order_result=[];
        //填充统计库存纪录
        if ($bp==0){
            $goods_id_key = array_keys($goods_stock_log);
            $goods_id_ke = array_column($order['data'],'goods_id');
            foreach ($goods_id_key as $key=>$vo){
                if(in_array($vo,$goods_id_ke)){

                }else{
                    $order['data'][]=[
                        'goods_id'=>$vo,
                        'num_total'=>0
                    ];
                }
            }
        }
        $count_all = $sum_all = $member_count_all = $doctor_all = $bp_all = 0;
        if (!empty($order['data'])) {
            foreach ($order['data'] as $key=>$vo){
                $sum_all = $sum_all+$vo['num_total'];
                $count_all = $count_all + ($vo['num_total']>0?1:0);
                //品牌，中文名，库存
                $goods_info = model('goods')->getInfo([['goods_id', '=', $vo['goods_id']], ['site_id', '=', $this->site_id]], '*');
                if($goods_info){
                    $goods_info['num'] = $vo['num_total'];
                    $category_name='';
                    if ($goods_info['category_id']) {
                        $category_name = model('goods_category')->getColumn([['category_id', 'in', $goods_info['category_id']], ['pid', '=', 55]], 'category_name');
                        if (is_array($category_name)) {
                            $category_name = implode('/', $category_name);
                        }
                    }
                    $goods_info['category_name'] = $category_name;
                    $goods_info['1_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][1]:0;
                    $goods_info['2_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][2]:0;
                    $goods_info['3_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][3]:0;
                    $goods_info['4_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][4]:0;
                    $goods_info['5_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][5]:0;
                    $order_result[]=$goods_info;
                    $member_count_all = $member_count_all+$goods_info['3_type'];
                    $doctor_all = $doctor_all+$goods_info['4_type'];
                    $bp_all = $bp_all+$goods_info['5_type'];
                }

            }
        }
        $user_model = new UserModel();
        $sorted = $user_model->array_orderby($order_result, 'num', SORT_DESC, 'category_name', SORT_DESC);
        $result = $sorted;
        $this->assign("reg_start_date", strtotime($reg_start_date)*1000);
        $this->assign("reg_end_date", strtotime($reg_end_date)*1000);
        $this->assign("list", $result);

        $bp_list = model('user')->getList([["type_id", "=", 1]],   '*', 'uid asc');
        $this->assign("bp_list", $bp_list);
        $this->assign("bp", $bp);
        $this->assign("doctor_all", $doctor_all);
        $this->assign("bp_all", $bp_all);
        $this->assign("sum_all", $sum_all);
        $this->assign("member_count_all", $member_count_all);
        $this->assign("count_all", $count_all);
        return $this->fetch("stat/goods");
    }

    public function exportgoods()
    {
        $bp  = input('bp',0);
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $condition   = [];
        $condition[]=['o.order_status','>','0'];
        $condition[]=['o.pay_time','>',strtotime($reg_start_date)];
        $condition[]=['o.pay_time','<',strtotime($reg_end_date)];
        if ($bp){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$bp])){
                $datu       = array_merge($datau[$bp]['bp'],$datau[$bp]['doctor']);
                $datu       = array_merge($datu,$datau[$bp]['patient']);
            }else{
                $datu = [];
                $datu[] = $bp;
            }
            $condition[] = ["o.admin_uid", "in", $datu];
        }
        $order_common_model = new OrderCommonModel();
        $order       = $order_common_model->getOrderGoodsDetailGroupList($condition);

        $alias       = 'og';
        $join        = [
            [
                'goods_stock_log o',
                'o.goods_id = og.goods_id',
                'left'
            ]
        ];
        $order_goods_field = 'o.type,o.goods_id,sum(o.old_stock) as old_stock_total,sum(o.new_stock) as new_stock_total';
        $condition   = [];
        $condition[]=['o.time','>',strtotime($reg_start_date)];
        $condition[]=['o.time','<',strtotime($reg_end_date)];
        $list = model('goods')->getList($condition,   $order_goods_field, 'og.goods_id desc', $alias, $join,'o.goods_id,o.type');
        $goods_stock_log=[];
        foreach($list as $key=>$vo){
            $goods_stock_log[$vo['goods_id']][1]=0;
            $goods_stock_log[$vo['goods_id']][2]=0;
            $goods_stock_log[$vo['goods_id']][3]=0;
            $goods_stock_log[$vo['goods_id']][4]=0;
            $goods_stock_log[$vo['goods_id']][5]=0;
        }
        foreach($list as $key=>$vo){
            $goods_stock_log[$vo['goods_id']][$vo['type']]=($vo['new_stock_total']-$vo['old_stock_total'])<0?($vo['new_stock_total']-$vo['old_stock_total'])*-1:($vo['new_stock_total']-$vo['old_stock_total']);
        }
        $order_result=[];
        //填充统计库存纪录
        if ($bp==0){
            $goods_id_key = array_keys($goods_stock_log);
            $goods_id_ke = array_column($order['data'],'goods_id');
            foreach ($goods_id_key as $key=>$vo){
                if(in_array($vo,$goods_id_ke)){

                }else{
                    $order['data'][]=[
                        'goods_id'=>$vo,
                        'num_total'=>0
                    ];
                }
            }
        }
        $count_all = $sum_all = $member_count_all = $doctor_all = $bp_all = 0;
        if (!empty($order['data'])) {
            foreach ($order['data'] as $key=>$vo){
                $sum_all = $sum_all+$vo['num_total'];
                $count_all = $count_all + ($vo['num_total']>0?1:0);
                //品牌，中文名，库存
                $goods_info = model('goods')->getInfo([['goods_id', '=', $vo['goods_id']], ['site_id', '=', $this->site_id]], '*');
                $goods_infosku = model('goods_sku')->getInfo([['goods_id', '=', $vo['goods_id']], ['site_id', '=', $this->site_id]], '*');
                if($goods_info){
                    $goods_info['num'] = $vo['num_total'];
                    $category_name='';
                    if ($goods_info['category_id']) {
                        $category_name = model('goods_category')->getColumn([['category_id', 'in', $goods_info['category_id']], ['pid', '=', 55]], 'category_name');
                        if (is_array($category_name)) {
                            $category_name = implode('/', $category_name);
                        }
                    }
                    $goods_info['category_name'] = $category_name;
                    $goods_info['sku_daizhuang'] = $goods_infosku['daizhuang'];
                    $goods_info['price'] = $goods_infosku['price'];
                    $goods_info['1_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][1]:0;
                    $goods_info['2_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][2]:0;
                    $goods_info['3_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][3]:0;
                    $goods_info['4_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][4]:0;
                    $goods_info['5_type'] = isset($goods_stock_log[$vo['goods_id']])?$goods_stock_log[$vo['goods_id']][5]:0;
                    $order_result[]=$goods_info;
                    $member_count_all = $member_count_all+$goods_info['3_type'];
                    $doctor_all = $doctor_all+$goods_info['4_type'];
                    $bp_all = $bp_all+$goods_info['5_type'];
                }

            }
        }
        $user_model = new UserModel();
        $sorted = $user_model->array_orderby($order_result, 'num', SORT_DESC, 'category_name', SORT_DESC);
        $result = $sorted;

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("产品统计");
        $phpExcel->getProperties()->setSubject("产品统计");
        // 对单元格设置居中效果
        $phpExcel->getActiveSheet()->getStyle('A')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('B')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('C')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('D')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('E')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('F')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('G')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('H')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('I')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('J')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('K')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('L')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('M')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        $phpExcel->getActiveSheet()->setCellValue('A1', '品牌');
        $phpExcel->getActiveSheet()->setCellValue('B1', '中文名');//可以指定位置
        $phpExcel->getActiveSheet()->setCellValue('C1', '英文名');
        $phpExcel->getActiveSheet()->setCellValue('D1', '库存');
        $phpExcel->getActiveSheet()->setCellValue('E1', '购买数');
        $phpExcel->getActiveSheet()->setCellValue('F1', '订单扣减');
        $phpExcel->getActiveSheet()->setCellValue('G1', '订单回退');
        $phpExcel->getActiveSheet()->setCellValue('H1', '人工入库');
        $phpExcel->getActiveSheet()->setCellValue('I1', '人工报损');
        $phpExcel->getActiveSheet()->setCellValue('J1', '转移');
        $phpExcel->getActiveSheet()->setCellValue('K1', '单价');
        $phpExcel->getActiveSheet()->setCellValue('L1', '每瓶/袋数');
        $phpExcel->getActiveSheet()->setCellValue('M1', '购买瓶数');


        //循环添加数据（根据自己的逻辑）
        foreach ($result as $k => $v) {
            $i = $k + 2;
            $phpExcel->getActiveSheet()->setCellValue('A' . $i, $v['category_name']);
            $phpExcel->getActiveSheet()->setCellValue('B' . $i, $v['goods_chi']);
            $phpExcel->getActiveSheet()->setCellValue('C' . $i, $v['goods_name']);
            $phpExcel->getActiveSheet()->setCellValue('D' . $i, $v['goods_stock']);
            $phpExcel->getActiveSheet()->setCellValue('E' . $i, $v['num']);
            $phpExcel->getActiveSheet()->setCellValue('F' . $i, $v['1_type']);
            $phpExcel->getActiveSheet()->setCellValue('G' . $i, $v['2_type']);
            $phpExcel->getActiveSheet()->setCellValue('H' . $i, $v['3_type']);
            $phpExcel->getActiveSheet()->setCellValue('I' . $i, $v['4_type']);
            $phpExcel->getActiveSheet()->setCellValue('J' . $i, $v['5_type']);
            $phpExcel->getActiveSheet()->setCellValue('K' . $i, $v['price']);
            $phpExcel->getActiveSheet()->setCellValue('L' . $i, $v['sku_daizhuang']);
            $phpExcel->getActiveSheet()->setCellValue('M' . $i, round($v['num']/$v['sku_daizhuang'],2));
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('产品统计');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('产品统计', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }

    /**
     * 商品统计报表
     * */
    public function getGoodsStatList()
    {
        if (request()->isAjax()) {
            $date_type = input('date_type', 1);

            if ($date_type == 1) {
                $start_time = strtotime("-6 day");
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 6;
            } else if ($date_type == 2) {
                $start_time = strtotime("-29 day");
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 29;
            }

            $stat_model = new StatModel();

            $stat_list = $stat_model->getShopStatList($this->site_id, $start_time);

            //将时间戳作为列表的主键
            $shop_stat_list = array_column($stat_list['data'], null, 'day_time');

            $data = array();

            for ($i = 0; $i <= $day; $i++) {
                $time             = strtotime(date('Y-m-d', strtotime("-" . ($day - $i) . " day")));
                $data['time'][$i] = date('Y-m-d', $time);
                if (array_key_exists($time, $shop_stat_list)) {
                    $data['order_total'][$i]     = $shop_stat_list[$time]['order_total'];
                    $data['shipping_total'][$i]  = $shop_stat_list[$time]['shipping_total'];
                    $data['refund_total'][$i]    = $shop_stat_list[$time]['refund_total'];
                    $data['order_pay_count'][$i] = $shop_stat_list[$time]['order_pay_count'];
                    $data['goods_pay_count'][$i] = $shop_stat_list[$time]['goods_pay_count'];
                    $data['shop_money'][$i]      = $shop_stat_list[$time]['shop_money'];
                    $data['platform_money'][$i]  = $shop_stat_list[$time]['platform_money'];
                    $data['collect_shop'][$i]    = $shop_stat_list[$time]['collect_shop'];
                    $data['collect_goods'][$i]   = $shop_stat_list[$time]['collect_goods'];
                    $data['visit_count'][$i]     = $shop_stat_list[$time]['visit_count'];
                    $data['order_count'][$i]     = $shop_stat_list[$time]['order_count'];
                    $data['goods_count'][$i]     = $shop_stat_list[$time]['goods_count'];
                    $data['add_goods_count'][$i] = $shop_stat_list[$time]['add_goods_count'];
                    $data['member_count'][$i]    = $shop_stat_list[$time]['member_count'];
                } else {
                    $data['order_total'][$i]     = 0.00;
                    $data['shipping_total'][$i]  = 0.00;
                    $data['refund_total'][$i]    = 0.00;
                    $data['order_pay_count'][$i] = 0;
                    $data['goods_pay_count'][$i] = 0;
                    $data['shop_money'][$i]      = 0.00;
                    $data['platform_money'][$i]  = 0.00;
                    $data['collect_shop'][$i]    = 0;
                    $data['collect_goods'][$i]   = 0;
                    $data['visit_count'][$i]     = 0;
                    $data['order_count'][$i]     = 0;
                    $data['goods_count'][$i]     = 0;
                    $data['add_goods_count'][$i] = 0;
                    $data['member_count'][$i]    = 0;
                }
            }

            $data['time_range'] = $time_range;

            return $data;
        }
    }

    /**
     * 交易统计
     * @return mixed
     */
    public function order()
    {
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $search = input('search', '');
        $condition   = [];
        $condition[] = ['group_id','=',2];
        $condition[] = ['source_uid','=',0];

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
            }else{
                $datu = [];
                $datu[] = $this->uid;
            }
            $condition[] = ["uid", "in", $datu];
        }

        if ($search){
            $condition[] = ['username', "like", '%' . $search . '%'];
        }

        $user_model = new UserModel();
        $list       = $user_model->getUserList($condition, '*', "create_time desc");
        $user_bpdotocr = $user_model->bpdoctorList();
        $datau = $user_model->bpdoctordata();
        foreach ($list['data'] as $key =>$vo){
            $vo['group_name'] = '';
            $list['data'][$key]['agent_username'] = '';
            if ($user_bpdotocr[$vo['uid']]['bp']){
                $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['bp'] = $user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['bp'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['doctor']){
                $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['doctor'] = $user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['doctor'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['patient']){
                $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['patient'] = $user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['patient'] = '';
            }
            $list['data'][$key]['group_name'] = $vo['group_name'];
            if (!$list['data'][$key]['bp']){
                $list['data'][$key]['bp'] = $list['data'][$key]['doctor'];
            }
            $condition   = [];
            $member_condition   = [];
            $balance_condition   = [];
            if (isset($datau[$vo['uid']])){
                $datu       = array_merge($datau[$vo['uid']]['bp'],$datau[$vo['uid']]['doctor']);
                $datu       = array_merge($datu,$datau[$vo['uid']]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                    $member_condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $vo['uid']];
                    $member_condition[] = ["user_id", "=", $vo['uid']];
                }

            }
            $balance_condition[] = ["uid", "=", $vo['uid']];
            $balance_condition[] = ['time','>',strtotime($reg_start_date)];
            $balance_condition[] = ['time','<',strtotime($reg_end_date)];
            $balance_condition_type2=$balance_condition_type4=$balance_condition_type5=$balance_condition;
            $balance_condition_type2[]=["type", "=", 2];
            $balance_type2 = model('user_balance_log')->getSum($balance_condition_type2, 'balance');
            $balance_condition_type4[]=["type", "=", 4];
            $balance_type4 = model('user_balance_log')->getSum($balance_condition_type4, 'balance');
            $balance_condition_type5[]=["type", "=", 5];
            $balance_type5 = model('user_balance_log')->getSum($balance_condition_type5, 'balance');
            $list['data'][$key]['balance_type2']=$balance_type2;
            $list['data'][$key]['balance_type4']=$balance_type4;
            $list['data'][$key]['balance_type5']=$balance_type5;
            $condition[]=['order_status','>','0'];
            $condition[]=['create_time','>',strtotime($reg_start_date)];
            $condition[]=['create_time','<',strtotime($reg_end_date)];
            $member_condition[]=['status','=','1'];
            $condition_online = $condition_offline = $condition_balance = $condition;
            $condition_online[] = ['pay_type','in','WECHATPAY,ALIAPY'];
            $condition_offline[] = ['pay_type','=','OFFLINE_PAY'];
            $condition_balance[] = ['pay_type','=','BALANCE'];
            $sum = model('order')->getSum($condition, 'order_money');
            $list['data'][$key]['sum']=$sum;
            $list['data'][$key]['doctor']=[];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'在线支付',
                'ordermoney'=>model('order')->getSum($condition_online, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_online),
                'real_money'=>model('order')->getSum($condition_online, 'real_money'),
            ];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'余额支付',
                'ordermoney'=>model('order')->getSum($condition_balance, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_balance),
                'real_money'=>model('order')->getfuwufei($condition_balance),
            ];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'线下支付',
                'ordermoney'=>model('order')->getSum($condition_offline, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_offline),
                'real_money'=>0,
            ];

        }
        $sorted = $list['data'];
        $sorted = $user_model->array_orderby($sorted, 'sum', SORT_DESC, 'balance', SORT_DESC);
        $list = $sorted;
        $count_all = $sum_all = $member_count_all = $doctor_all = $bp_all = $fuwufei_all = $fandian_all = 0;
        foreach ($list as $key=>$vo){
            $list[$key]['invoicerate'] = array_sum(array_column($vo['doctor'], 'invoicerate'));
            $list[$key]['real_money'] = array_sum(array_column($vo['doctor'], 'real_money'));
            $fuwufei_all = $fuwufei_all + $list[$key]['invoicerate'];
            $member_count_all = $member_count_all + $list[$key]['real_money'];
            $doctor_all = $doctor_all + $vo['balance_type2'];
            $bp_all = $bp_all + $vo['balance_type4'];
            $fandian_all = $fandian_all + $vo['balance_type5'];
            $count_all = $count_all + $vo['balance'];
            $sum_all = $sum_all + $vo['sum'];

        }


        $this->assign("reg_start_date", strtotime($reg_start_date)*1000);
        $this->assign("reg_end_date", strtotime($reg_end_date)*1000);
        $this->assign("search", $search);
        $this->assign("doctor_all", $doctor_all);
        $this->assign("bp_all", $bp_all);
        $this->assign("sum_all", $sum_all);
        $this->assign("fandian_all", $fandian_all);
        $this->assign("member_count_all", $member_count_all);
        $this->assign("count_all", $count_all);
        $this->assign("fuwufei_all", $fuwufei_all);
        $this->assign("list", $list);

        return $this->fetch("stat/order");
    }

    public function exportorder(){
        $reg_start_date = input('reg_start_date', date('Y-m-d 00:00:00'));
        $reg_end_date = input('reg_end_date', date('Y-m-d 23:59:59'));
        $search = input('search', '');
        $condition   = [];
        $condition[] = ['group_id','=',2];
        $condition[] = ['source_uid','=',0];

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
            }else{
                $datu = [];
                $datu[] = $this->uid;
            }
            $condition[] = ["uid", "in", $datu];
        }

        if ($search){
            $condition[] = ['username', "like", '%' . $search . '%'];
        }

        $user_model = new UserModel();
        $list       = $user_model->getUserList($condition, '*', "create_time desc");
        $user_bpdotocr = $user_model->bpdoctorList();
        $datau = $user_model->bpdoctordata();
        foreach ($list['data'] as $key =>$vo){
            $vo['group_name'] = '';
            $list['data'][$key]['agent_username'] = '';
            if ($user_bpdotocr[$vo['uid']]['bp']){
                $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['bp'] = $user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['bp'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['doctor']){
                $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['doctor'] = $user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['doctor'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['patient']){
                $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['patient'] = $user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['agent_username'] = $user_bpdotocr[$vo['uid']]['agent_username'];
            }else{
                $list['data'][$key]['patient'] = '';
            }
            $list['data'][$key]['group_name'] = $vo['group_name'];
            if (!$list['data'][$key]['bp']){
                $list['data'][$key]['bp'] = $list['data'][$key]['doctor'];
            }
            $condition   = [];
            $member_condition   = [];
            $balance_condition   = [];
            if (isset($datau[$vo['uid']])){
                $datu       = array_merge($datau[$vo['uid']]['bp'],$datau[$vo['uid']]['doctor']);
                $datu       = array_merge($datu,$datau[$vo['uid']]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                    $member_condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $vo['uid']];
                    $member_condition[] = ["user_id", "=", $vo['uid']];
                }

            }
            $balance_condition[] = ["uid", "=", $vo['uid']];
            $balance_condition[] = ['time','>',strtotime($reg_start_date)];
            $balance_condition[] = ['time','<',strtotime($reg_end_date)];
            $balance_condition_type2=$balance_condition_type4=$balance_condition_type5=$balance_condition;
            $balance_condition_type2[]=["type", "=", 2];
            $balance_type2 = model('user_balance_log')->getSum($balance_condition_type2, 'balance');
            $balance_condition_type4[]=["type", "=", 4];
            $balance_type4 = model('user_balance_log')->getSum($balance_condition_type4, 'balance');
            $balance_condition_type5[]=["type", "=", 5];
            $balance_type5 = model('user_balance_log')->getSum($balance_condition_type5, 'balance');
            $list['data'][$key]['balance_type2']=$balance_type2;
            $list['data'][$key]['balance_type4']=$balance_type4;
            $list['data'][$key]['balance_type5']=$balance_type5;
            $condition[]=['order_status','>','0'];
            $condition[]=['create_time','>',strtotime($reg_start_date)];
            $condition[]=['create_time','<',strtotime($reg_end_date)];
            $member_condition[]=['status','=','1'];
            $condition_online = $condition_offline = $condition_balance = $condition;
            $condition_online[] = ['pay_type','in','WECHATPAY,ALIAPY'];
            $condition_offline[] = ['pay_type','=','OFFLINE_PAY'];
            $condition_balance[] = ['pay_type','=','BALANCE'];
            $sum = model('order')->getSum($condition, 'order_money');
            $list['data'][$key]['sum']=$sum;
            $list['data'][$key]['doctor']=[];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'在线支付',
                'ordermoney'=>model('order')->getSum($condition_online, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_online),
                'real_money'=>model('order')->getSum($condition_online, 'real_money'),
            ];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'余额支付',
                'ordermoney'=>model('order')->getSum($condition_balance, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_balance),
                'real_money'=>model('order')->getfuwufei($condition_balance),
            ];
            $list['data'][$key]['doctor'][]=[
                'pay_type'=>'线下支付',
                'ordermoney'=>model('order')->getSum($condition_offline, 'order_money'),
                'invoicerate'=>model('order')->getfuwufei($condition_offline),
                'real_money'=>0,
            ];

        }
        $sorted = $list['data'];
        $sorted = $user_model->array_orderby($sorted, 'sum', SORT_DESC, 'balance', SORT_DESC);
        $list = $sorted;
        $count_all = $sum_all = $member_count_all = $doctor_all = $bp_all = $fuwufei_all = $fandian_all = 0;
        foreach ($list as $key=>$vo){
            $list[$key]['invoicerate'] = array_sum(array_column($vo['doctor'], 'invoicerate'));
            $list[$key]['real_money'] = array_sum(array_column($vo['doctor'], 'real_money'));
            $fuwufei_all = $fuwufei_all + $list[$key]['invoicerate'];
            $member_count_all = $member_count_all + $list[$key]['real_money'];
            $doctor_all = $doctor_all + $vo['balance_type2'];
            $bp_all = $bp_all + $vo['balance_type4'];
            $fandian_all = $fandian_all + $vo['balance_type5'];
            $count_all = $count_all + $vo['balance'];
            $sum_all = $sum_all + $vo['sum'];

        }

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("机构统计");
        $phpExcel->getProperties()->setSubject("机构统计");
        // 对单元格设置居中效果
        $phpExcel->getActiveSheet()->getStyle('A')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('B')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('C')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('D')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('E')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('F')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('G')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('H')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('I')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('J')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        $phpExcel->getActiveSheet()->setCellValue('A1', '产品经理');
        $phpExcel->getActiveSheet()->setCellValue('B1', '机构/医生');//可以指定位置
        $phpExcel->getActiveSheet()->setCellValue('C1', '折扣');
        $phpExcel->getActiveSheet()->setCellValue('D1', '余额');
        $phpExcel->getActiveSheet()->setCellValue('E1', '充值');
        $phpExcel->getActiveSheet()->setCellValue('F1', '扣减');
        $phpExcel->getActiveSheet()->setCellValue('G1', '返点');
        $phpExcel->getActiveSheet()->setCellValue('H1', '订单金额');
        $phpExcel->getActiveSheet()->setCellValue('I1', '成本');
        $phpExcel->getActiveSheet()->setCellValue('J1', '实收');
        $phpExcel->getActiveSheet()->setCellValue('K1', '差额');
        $phpExcel->getActiveSheet()->setCellValue('L1', '支付类型');
        $phpExcel->getActiveSheet()->setCellValue('M1', '订单金额');
        $phpExcel->getActiveSheet()->setCellValue('N1', '成本');
        $phpExcel->getActiveSheet()->setCellValue('O1', '实收');
        $phpExcel->getActiveSheet()->setCellValue('P1', '差额');




        //循环添加数据（根据自己的逻辑）
        $i = 1;
        foreach ($list as $k => $v) {
            foreach($v['doctor'] as $kk=>$vo){
                $i = $i + 1;
                if ($kk==0){
                    $phpExcel->getActiveSheet()->setCellValue('A' . $i, $v['agent_username']);
                    $phpExcel->getActiveSheet()->setCellValue('B' . $i, $v['bp']);
                    $phpExcel->getActiveSheet()->setCellValue('C' . $i, $v['balancediscount']);
                    $phpExcel->getActiveSheet()->setCellValue('D' . $i, $v['balance']);
                    $phpExcel->getActiveSheet()->setCellValue('E' . $i, $v['balance_type2']);
                    $phpExcel->getActiveSheet()->setCellValue('F' . $i, $v['balance_type4']);
                    $phpExcel->getActiveSheet()->setCellValue('G' . $i, $v['balance_type5']);
                    $phpExcel->getActiveSheet()->setCellValue('H' . $i, $v['sum']);
                    $phpExcel->getActiveSheet()->setCellValue('I' . $i, $v['invoicerate']);
                    $phpExcel->getActiveSheet()->setCellValue('J' . $i, $v['real_money']);
                    $phpExcel->getActiveSheet()->setCellValue('K' . $i, ($v['real_money']-$v['invoicerate']));
                    $phpExcel->getActiveSheet()->setCellValue('L' . $i, $vo['pay_type']);
                    $phpExcel->getActiveSheet()->setCellValue('M' . $i, $vo['ordermoney']);
                    $phpExcel->getActiveSheet()->setCellValue('N' . $i, $vo['invoicerate']);
                    $phpExcel->getActiveSheet()->setCellValue('O' . $i, $vo['real_money']);
                    $phpExcel->getActiveSheet()->setCellValue('P' . $i, ($vo['real_money']-$vo['invoicerate']));

                }else{
                    $phpExcel->getActiveSheet()->setCellValue('A' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('B' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('C' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('D' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('E' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('F' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('G' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('H' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('I' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('J' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('K' . $i, '');
                    $phpExcel->getActiveSheet()->setCellValue('L' . $i, $vo['pay_type']);
                    $phpExcel->getActiveSheet()->setCellValue('M' . $i, $vo['ordermoney']);
                    $phpExcel->getActiveSheet()->setCellValue('N' . $i, $vo['invoicerate']);
                    $phpExcel->getActiveSheet()->setCellValue('O' . $i, $vo['real_money']);
                    $phpExcel->getActiveSheet()->setCellValue('P' . $i, ($vo['real_money']-$vo['invoicerate']));
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('交易统计');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('交易统计', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;

    }

    /**
     * 交易统计报表
     * */
    public function getOrderStatList()
    {
        if (request()->isAjax()) {
            $date_type = input('date_type', 1);

            if ($date_type == 1) {
                $start_time = strtotime(date('Y-m-d', strtotime("-6 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 6;
            } else if ($date_type == 2) {
                $start_time = strtotime(date('Y-m-d', strtotime("-29 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 29;
            }

            $stat_model = new StatModel();

            $stat_list = $stat_model->getShopStatList($this->site_id, $start_time);

            //将时间戳作为列表的主键
            $shop_stat_list = array_column($stat_list['data'], null, 'day_time');

            $data = array();

            for ($i = 0; $i <= $day; $i++) {
                $time             = strtotime(date('Y-m-d', strtotime("-" . ($day - $i) . " day")));
                $data['time'][$i] = date('Y-m-d', $time);
                if (array_key_exists($time, $shop_stat_list)) {
                    $data['order_total'][$i]     = $shop_stat_list[$time]['order_total'];
                    $data['shipping_total'][$i]  = $shop_stat_list[$time]['shipping_total'];
                    $data['refund_total'][$i]    = $shop_stat_list[$time]['refund_total'];
                    $data['order_pay_count'][$i] = $shop_stat_list[$time]['order_pay_count'];
                    $data['goods_pay_count'][$i] = $shop_stat_list[$time]['goods_pay_count'];
                    $data['shop_money'][$i]      = $shop_stat_list[$time]['shop_money'];
                    $data['platform_money'][$i]  = $shop_stat_list[$time]['platform_money'];
                    $data['collect_shop'][$i]    = $shop_stat_list[$time]['collect_shop'];
                    $data['collect_goods'][$i]   = $shop_stat_list[$time]['collect_goods'];
                    $data['visit_count'][$i]     = $shop_stat_list[$time]['visit_count'];
                    $data['order_count'][$i]     = $shop_stat_list[$time]['order_count'];
                    $data['goods_count'][$i]     = $shop_stat_list[$time]['goods_count'];
                    $data['add_goods_count'][$i] = $shop_stat_list[$time]['add_goods_count'];
                } else {
                    $data['order_total'][$i]     = 0.00;
                    $data['shipping_total'][$i]  = 0.00;
                    $data['refund_total'][$i]    = 0.00;
                    $data['order_pay_count'][$i] = 0;
                    $data['goods_pay_count'][$i] = 0;
                    $data['shop_money'][$i]      = 0.00;
                    $data['platform_money'][$i]  = 0.00;
                    $data['collect_shop'][$i]    = 0;
                    $data['collect_goods'][$i]   = 0;
                    $data['visit_count'][$i]     = 0;
                    $data['order_count'][$i]     = 0;
                    $data['goods_count'][$i]     = 0;
                    $data['add_goods_count'][$i] = 0;
                }
            }

            $data['time_range'] = $time_range;

            return $data;
        }
    }

    /**
     * 访问统计
     * @return mixed
     */
    public function visit()
    {
        if (request()->isAjax()) {
            $date_type = input('date_type', 0);

            if ($date_type == 0) {
                $start_time = strtotime("today");
                $time_range = date('Y-m-d', $start_time);
            } else if ($date_type == 1) {
                $start_time = strtotime(date('Y-m-d', strtotime("-6 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
            } else if ($date_type == 2) {
                $start_time = strtotime(date('Y-m-d', strtotime("-29 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
            }

            $stat_model = new StatModel();

            $shop_stat_sum = $stat_model->getShopStatSum($this->site_id, $start_time);

            $shop_stat_sum['data']['time_range'] = $time_range;

            return $shop_stat_sum;
        } else {
            return $this->fetch("stat/visit");
        }
    }

    /**
     * 访问统计报表
     * */
    public function getVisitStatList()
    {
        if (request()->isAjax()) {
            $date_type = input('date_type', 1);

            if ($date_type == 1) {
                $start_time = strtotime(date('Y-m-d', strtotime("-6 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 6;
            } else if ($date_type == 2) {
                $start_time = strtotime(date('Y-m-d', strtotime("-29 day")));
                $time_range = date('Y-m-d', $start_time) . ' 至 ' . date('Y-m-d', strtotime("today"));
                $day        = 29;
            }

            $stat_model = new StatModel();

            $stat_list = $stat_model->getShopStatList($this->site_id, $start_time);

            //将时间戳作为列表的主键
            $shop_stat_list = array_column($stat_list['data'], null, 'day_time');

            $data = array();

            for ($i = 0; $i <= $day; $i++) {
                $time             = strtotime(date('Y-m-d', strtotime("-" . ($day - $i) . " day")));
                $data['time'][$i] = date('Y-m-d', $time);
                if (array_key_exists($time, $shop_stat_list)) {
                    $data['order_total'][$i]     = $shop_stat_list[$time]['order_total'];
                    $data['shipping_total'][$i]  = $shop_stat_list[$time]['shipping_total'];
                    $data['refund_total'][$i]    = $shop_stat_list[$time]['refund_total'];
                    $data['order_pay_count'][$i] = $shop_stat_list[$time]['order_pay_count'];
                    $data['goods_pay_count'][$i] = $shop_stat_list[$time]['goods_pay_count'];
                    $data['shop_money'][$i]      = $shop_stat_list[$time]['shop_money'];
                    $data['platform_money'][$i]  = $shop_stat_list[$time]['platform_money'];
                    $data['collect_shop'][$i]    = $shop_stat_list[$time]['collect_shop'];
                    $data['collect_goods'][$i]   = $shop_stat_list[$time]['collect_goods'];
                    $data['visit_count'][$i]     = $shop_stat_list[$time]['visit_count'];
                    $data['order_count'][$i]     = $shop_stat_list[$time]['order_count'];
                    $data['goods_count'][$i]     = $shop_stat_list[$time]['goods_count'];
                    $data['add_goods_count'][$i] = $shop_stat_list[$time]['add_goods_count'];
                } else {
                    $data['order_total'][$i]     = 0.00;
                    $data['shipping_total'][$i]  = 0.00;
                    $data['refund_total'][$i]    = 0.00;
                    $data['order_pay_count'][$i] = 0;
                    $data['goods_pay_count'][$i] = 0;
                    $data['shop_money'][$i]      = 0.00;
                    $data['platform_money'][$i]  = 0.00;
                    $data['collect_shop'][$i]    = 0;
                    $data['collect_goods'][$i]   = 0;
                    $data['visit_count'][$i]     = 0;
                    $data['order_count'][$i]     = 0;
                    $data['goods_count'][$i]     = 0;
                    $data['add_goods_count'][$i] = 0;
                }
            }

            $data['time_range'] = $time_range;

            return $data;
        }
    }
}