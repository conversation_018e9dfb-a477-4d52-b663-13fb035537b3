<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\goods\GoodsEvaluate as GoodsEvaluateModel;
use app\model\system\Group;
use app\model\system\Menu;
use app\model\member\Member as MemberModel;
use app\model\system\User as UserModel;

/**
 * 用户
 * Class User
 * @package app\shop\controller
 */
class User extends BaseShop
{
    /**
     * 用户列表
     * @return mixed
     */
    public function user()
    {
        if (request()->isAjax()) {
            $page        = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $status      = input('status', '');
            $search_keys = input('search_keys', "");

            $condition   = [];
            $condition[] = ["site_id", "=", $this->site_id];
            $condition[] = ["app_module", "=", $this->app_module];
            if (!empty($search_keys)) {
                $condition[] = ['username', 'like', '%' . $search_keys . '%'];
            }
            if ($status != "") {
                $condition["status"] = ["status", "=", $status];
            }

            $user_model = new UserModel();
            $list       = $user_model->getUserPageList($condition, $page, $page_size, "create_time desc");
            return $list;
        } else {
            $this->forthMenu();
            return $this->fetch("user/user_list");
        }
    }

    public function userbalance()
    {
        $uid = input('uid', 0);
        if ($uid == 0 && $this->uid!=1){
            $uid = $this->uid;
        }

        if (request()->isAjax()) {
            $page_index   = input('page', 1);
            $page_size    = input('page_size', PAGE_LIST_ROWS);
            $explain_type = input('explain_type', ''); //1好评2中评3差评
            $search_text  = input('search_keys', ''); //搜索值
            $start_time   = input('start_time', '');
            $end_time     = input('end_time', '');
            $condition    = [
            ];
            //评分类型
            if ($explain_type != "") {
                $condition[] = ["type", "=", $explain_type];
            }
            if ($search_text != "") {
                $condition[] = ['username|user_uid_name', "like", '%' . $search_text . '%'];
            }
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["time", "<=", date_to_time($end_time)];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = ['time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
            }
            if ($uid) {
                $condition[] = ["uid", "=", $uid];
            }
            $user_model = new UserModel();
            return $user_model->getuserbalancePageList($condition, $page_index, $page_size, "id desc");
        } else {
            $this->assign("uid", $uid);
            return $this->fetch("user/userbalance");
        }

    }

    public function exportbalance(){
        $uid = input('uid', 0);
        if ($uid == 0 && $this->uid!=1){
            $uid = $this->uid;
        }
        //评分类型
        $explain_type = input('explain_type', ''); //1好评2中评3差评
        $search_text  = input('search_keys', ''); //搜索值
        $start_time   = input('start_time', '');
        $end_time     = input('end_time', '');
        $condition    = [
        ];
        //评分类型
        if ($explain_type != "") {
            $condition[] = ["type", "=", $explain_type];
        }
        if ($search_text != "") {
            $condition[] = ['username|user_uid_name', "like", '%' . $search_text . '%'];
        }
        if (!empty($start_time) && empty($end_time)) {
            $condition[] = ["time", ">=", date_to_time($start_time)];
        } elseif (empty($start_time) && !empty($end_time)) {
            $condition[] = ["time", "<=", date_to_time($end_time)];
        } elseif (!empty($start_time) && !empty($end_time)) {
            $condition[] = ['time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
        }
        if ($uid) {
            $condition[] = ["uid|user_uid", "=", $uid];
        }
        $user_model = new UserModel();

        $field = [
            'time'                 => '时间',
            'type'                 => '类型',
            'order_no'             => '订单编号',
            'username'             => '钱包账户',
            'user_uid_name'        => '使用者',
            'balance'              => '金额',
            'old_balance'          => '修改前库存',
            'new_balance'          => '修改后库存',
            'remark'               => '备注'
        ];
        //接收需要展示的字段
        $ordernum       = $user_model->getuserbalanceList($condition);

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'产品库存纪录'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        mb_convert_variables('GBK', 'UTF-8', $columns_title);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = $ordernum['data'];
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);
        for($i = 1; $i <= $pages; $i++) {
            $order       = $user_model->getuserbalancePageList($condition,$i,$perSize,'id desc','*');
            if(isset($order['data']['list'])){
                $order['data'] = $order['data']['list'];
            }else{
                $order['data'] = [];
            }

            if (!empty($order['data'])) {
                $types = [
                    1=>'订单扣减',
                    2=>'充值',
                    3=>'订单返回',
                    4=>'整瓶扣减'
                ];
                foreach ($order['data'] as $key=>$vo){
                    $order['data'][$key]['time'] = date('Y-m-d h:i:s',$vo['time']);
                    $order['data'][$key]['type'] = $types[$vo['type']];
                }
            }

            foreach($order['data'] as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
            unset($order);            //刷新输出缓冲到浏览器
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();
    }

    /**
     * 用户列表 用户给
     * @return mixed
     */
    public function invuser()
    {
        if (request()->isAjax()) {
            $page        = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $status      = input('status', '');
            $search_keys = input('search_keys', "");
            $type_id = input('type_id', "");

            $condition   = [];
            $condition[] = ["site_id", "=", $this->site_id];
            $condition[] = ["app_module", "=", $this->app_module];
            if (!empty($search_keys)) {
                $condition[] = ['username', 'like', '%' . $search_keys . '%'];
            }
            if ($status != "") {
                $condition[] = ["status", "=", $status];
            }
            if ($type_id != "") {
                $condition[] = ["type_id", "=", $type_id];
            }
            //医生组获取自己的订单
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                $user_model = new UserModel();
                $datau      = $user_model->bpdoctordata();
                if (isset($datau[$this->uid])){
                    $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                    $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                }else{
                    $datu = [];
                    $datu[] = $this->uid;
                }
                $condition[] = ["uid", "in", $datu];
            }

            //默认机构人员
            $condition[] = ["group_id", "=", 2];

            $user_model = new UserModel();
            $list       = $user_model->getUserPageList($condition, $page, $page_size, "create_time desc");
            $user_bpdotocr = $user_model->bpdoctorList();
            foreach ($list['data']['list'] as $key =>$vo){
                $vo['group_name'] = '';
                if ($user_bpdotocr[$vo['uid']]['bp']){
                    $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                }
                if ($user_bpdotocr[$vo['uid']]['doctor']){
                    $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                }
                if ($user_bpdotocr[$vo['uid']]['patient']){
                    $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                }
                $list['data']['list'][$key]['group_name'] = $vo['group_name'];

                //产品经理
                $list['data']['list'][$key]['agent_username'] = isset($user_bpdotocr[$vo['uid']])?$user_bpdotocr[$vo['uid']]['agent_username']:'';

            }
            return $list;
        } else {
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if ($user_bpdotocr[$this->uid]['bp'] == $this->uid || $this->uid==1 ){
                $this->assign("bp", 1);
            }else{
                $this->assign("bp", 0);
            }
            if ($this->uid==1 ){
                $this->assign("sysuser", 1);
            }else{
                $this->assign("sysuser", 0);
            }
            $this->forthMenu();
            return $this->fetch("user/invuser_list");
        }
    }

    public function statuser()
    {


        $condition   = [];
        $condition[] = ['group_id','=',2];

        $user_model = new UserModel();
        $list       = $user_model->getUserList($condition, '*', "create_time desc");
        $user_bpdotocr = $user_model->bpdoctorList();
        $datau = $user_model->bpdoctordata();
        foreach ($list['data'] as $key =>$vo){
            $vo['group_name'] = '';
            if ($user_bpdotocr[$vo['uid']]['bp']){
                $vo['group_name'] = $vo['group_name']. '(机构)'.$user_bpdotocr[$vo['uid']]['bp'];
                $list['data'][$key]['bp'] = $user_bpdotocr[$vo['uid']]['bp'];
            }else{
                $list['data'][$key]['bp'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['doctor']){
                $vo['group_name'] = $vo['group_name']. '-(医生)'.$user_bpdotocr[$vo['uid']]['doctor'];
                $list['data'][$key]['doctor'] = $user_bpdotocr[$vo['uid']]['doctor'];
            }else{
                $list['data'][$key]['doctor'] = '';
            }
            if ($user_bpdotocr[$vo['uid']]['patient']){
                $vo['group_name'] = $vo['group_name']. '-(健康师)'.$user_bpdotocr[$vo['uid']]['patient'];
                $list['data'][$key]['patient'] = $user_bpdotocr[$vo['uid']]['patient'];
            }else{
                $list['data'][$key]['patient'] = '';
            }
            $list['data'][$key]['group_name'] = $vo['group_name'];
            $condition   = [];
            $member_condition   = [];
            if (isset($datau[$vo['uid']])){
                $list['data'][$key]['doctorcount'] =count(array_unique($datau[$vo['uid']]['doctor']))+0;
                $list['data'][$key]['patientcount'] =count(array_unique($datau[$vo['uid']]['patient']))+0;
                $datu       = array_merge($datau[$vo['uid']]['bp'],$datau[$vo['uid']]['doctor']);
                $datu       = array_merge($datu,$datau[$vo['uid']]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                    $member_condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $vo['uid']];
                    $member_condition[] = ["user_id", "=", $vo['uid']];
                }

            }
            $condition[]=['order_status','>','0'];
            $condition[]=['create_time','>','**********'];
            $condition[]=['create_time','<','**********'];
            $member_condition[]=['status','=','1'];
            $count = model('order')->getCount($condition);
            $sum = model('order')->getSum($condition, 'order_money');
            $list['data'][$key]['count']=$count;
            $list['data'][$key]['sum']=$sum;
            $list['data'][$key]['member_count'] = model('member')->getCount($member_condition);

        }
        $sorted = $list['data'];
        $sorted = $user_model->array_orderby($sorted, 'bp', SORT_DESC, 'doctor', SORT_ASC);
        $list = $sorted;

        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("年度统计");
        $phpExcel->getProperties()->setSubject("年度统计");
        // 对单元格设置居中效果
        $phpExcel->getActiveSheet()->getStyle('A')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('B')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('C')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('D')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('E')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('F')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('G')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('H')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('I')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $phpExcel->getActiveSheet()->getStyle('J')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        $phpExcel->getActiveSheet()->setCellValue('A1', '机构');//可以指定位置
        $phpExcel->getActiveSheet()->setCellValue('B1', '医生');
        $phpExcel->getActiveSheet()->setCellValue('C1', '健康师');
        $phpExcel->getActiveSheet()->setCellValue('D1', '用户名');
        $phpExcel->getActiveSheet()->setCellValue('E1', '医生数量');
        $phpExcel->getActiveSheet()->setCellValue('F1', '健康师数量');
        $phpExcel->getActiveSheet()->setCellValue('G1', '用户数量');
        $phpExcel->getActiveSheet()->setCellValue('H1', '订单数量');
        $phpExcel->getActiveSheet()->setCellValue('I1', '订单金额');
        $phpExcel->getActiveSheet()->setCellValue('J1', '注册时间');

        //循环添加数据（根据自己的逻辑）
        foreach ($list as $k => $v) {
            $i = $k + 2;
            $phpExcel->getActiveSheet()->setCellValue('A' . $i, $v['bp']);
            $phpExcel->getActiveSheet()->setCellValue('B' . $i, $v['doctor']);
            $phpExcel->getActiveSheet()->setCellValue('C' . $i, $v['patient']);
            $phpExcel->getActiveSheet()->setCellValue('D' . $i, $v['username']);
            $phpExcel->getActiveSheet()->setCellValue('E' . $i, $v['doctorcount']);
            $phpExcel->getActiveSheet()->setCellValue('F' . $i, $v['patientcount']);
            $phpExcel->getActiveSheet()->setCellValue('G' . $i, $v['member_count']);
            $phpExcel->getActiveSheet()->setCellValue('H' . $i, $v['count']);
            $phpExcel->getActiveSheet()->setCellValue('I' . $i, $v['sum']);
            $phpExcel->getActiveSheet()->setCellValue('J' . $i, date('Y-m-d', $v['create_time']));
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('年度统计');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('年度统计', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;

    }

    /**
     * 添加用户
     * @return mixed
     */
    public function addUser()
    {
        if (request()->isAjax()) {
            $username = input("username", "");
            $password = input("password", "");
            $group_id = input("group_id", "");

            $user_model = new UserModel();
            $data       = array(
                "username"   => $username,
                "password"   => $password,
                'headimg'    => input('headimg', ''),
                "group_id"   => $group_id,
                "app_module" => $this->app_module,
                "site_id"    => $this->site_id
            );
            $result     = $user_model->addUser($data);
            if ($result['code'] < 0){
                return $result;
            }
            $mobile = input('mobile', '');
            if ($mobile){
                $member = model('member')->getInfo([['mobile', '=', $mobile]], '*');
                if ($member){
                    $mobile = '';
                }
            }

            $addressdata           = [
                'site_id'      => $this->site_id,
                'name'         => input('addressname', input('username')),
                'mobile'       => input('mobile', ''),
                'telephone'    => '',
                'province_id'  => input('province_id', ''),
                'city_id'      => input('city_id', ''),
                'district_id'  => input('district_id', ''),
                'community_id' => input('community_id', ''),
                'address'      => input('address', ''),
                'full_address' => input('full_address', ''),
                'longitude'    => input('longitude', ''),
                'latitude'     => input('latitude', ''),
                'is_default'   => 1,
            ];

            $data = [
                'site_id'           => $this->site_id,
                'user_id'           => $result['data'],
                'username'          => input('username', ''),
                'mobile'            => $mobile,
                'email'             => input('email', ''),
                'password'          => data_md5(input('password', '')),
                'status'            => input('status', 1),
                'headimg'           => input('headimg', ''),
                'member_level'      => input('member_level', '1'),
                'member_level_name' => input('member_level_name', '普通会员'),
                'nickname'          => input('username', ''),
                'sex'               => input('sex', 0),
                'birthday'          => input('birthday', '') ? strtotime(input('birthday', '')) : 0,
                'realname'          => input('realname', ''),
                'reg_time'          => time(),
            ];

            $member_model = new MemberModel();
            $member = $member_model->addMember($data,$addressdata);
            $condition = array(
                ["uid", "=", $result['data']],
                ["site_id", "=", $this->site_id],
            );
            $data      = array(
                "member_id" => $member['data']
            );
            $res = model("user")->update($data, $condition);

            return $result;
        } else {
            $group_model       = new Group();
            $group_list_result = $group_model->getGroupList([["site_id", "=", $this->site_id], ["app_module", "=", $this->app_module]]);
            $group_list        = $group_list_result["data"];
            $this->assign("group_list", $group_list);
            return $this->fetch("user/add_user");
        }
    }

    /**
     * 添加用户
     * @return mixed
     */
    public function addinvUser()
    {
        if (request()->isAjax()) {
            $username = input("username", "");
            $password = input("password", "");
            $type_id = input("group_id", "");
            $agent_uid = input("agent_uid", 0);
            $source_uid = input("aroup_id", "");
            $group_id = 2;

            $user_model = new UserModel();
            //判断上级级别必须大于下级
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                if (!$source_uid){
                    return error(-1,'请选择上级用户');
                }
            }
            if ($source_uid){
                $user_info = model('user')->getInfo(
                    [
                        ['uid', "=", $source_uid]
                    ]
                );
                if ($user_info['type_id']<$type_id){

                }else{
                    return error(-1,'用户上级级别必须大于该级别');
                }
            }
            $data       = array(
                "username"   => $username,
                "password"   => $password,
                'headimg'    => input('headimg', ''),
                "group_id"   => $group_id,
                "source_uid" => $source_uid,
                "type_id"    => $type_id,
                "agent_uid"  => $agent_uid,
                "app_module" => $this->app_module,
                "site_id"    => $this->site_id
            );
            $result     = $user_model->addUser($data);
            if ($result['code'] < 0){
                return $result;
            }
            $mobile = input('mobile', '');
            if ($mobile){
                $member = model('member')->getInfo([['mobile', '=', $mobile]], '*');
                if ($member){
                    $mobile = '';
                }
            }

            $addressdata           = [
                'site_id'      => $this->site_id,
                'name'         => input('addressname', input('username')),
                'mobile'       => input('mobile', ''),
                'telephone'    => '',
                'province_id'  => input('province_id', ''),
                'city_id'      => input('city_id', ''),
                'district_id'  => input('district_id', ''),
                'community_id' => input('community_id', ''),
                'address'      => input('address', ''),
                'full_address' => input('full_address', ''),
                'longitude'    => input('longitude', ''),
                'latitude'     => input('latitude', ''),
                'is_default'   => 1,
            ];

            $data = [
                'site_id'           => $this->site_id,
                'user_id'           => $result['data'],
                'username'          => input('username', ''),
                'mobile'            => $mobile,
                'email'             => input('email', ''),
                'password'          => data_md5(input('password', '')),
                'status'            => input('status', 1),
                'headimg'           => input('headimg', ''),
                'member_level'      => input('member_level', '1'),
                'member_level_name' => input('member_level_name', '普通会员'),
                'nickname'          => input('username', ''),
                'sex'               => input('sex', 0),
                'birthday'          => input('birthday', '') ? strtotime(input('birthday', '')) : 0,
                'realname'          => input('realname', ''),
                'reg_time'          => time(),
            ];

            $member_model = new MemberModel();
            $member = $member_model->addMember($data,$addressdata);
            $condition = array(
                ["uid", "=", $result['data']],
                ["site_id", "=", $this->site_id],
            );
            $data      = array(
                "member_id" => $member['data']
            );

            //同步关闭支付，余额支付，折扣设置
            $bp_error = 1;
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if ($user_bpdotocr[$result['data']]['bp'] && $type_id!=1) {
                $bp_user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$result['data']]['bp']]]);
                $data['pay_close']      = $bp_user['pay_close'];
                $data['isbalance']      = $bp_user['isbalance'];
                $data['ispaybalance']   = $bp_user['ispaybalance'];
                $data['balancediscount']      = $bp_user['balancediscount'];
                $bp_error=0;
            }
            if ($user_bpdotocr[$result['data']]['doctor'] && $bp_error == 1) {
                $bp_user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$result['data']]['doctor']]]);
                $data['pay_close']      = $bp_user['pay_close'];
                $data['isbalance']      = $bp_user['isbalance'];
                $data['ispaybalance']   = $bp_user['ispaybalance'];
                $data['balancediscount']      = $bp_user['balancediscount'];
            }

            $res = model("user")->update($data, $condition);

            return $result;
        } else {

            //根据等级显示用户标签
            $group_list  = [
            ];
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 1 ){
                $group_list  = [
                    ['group_id'=>1,'group_name'=>'机构'],
                    ['group_id'=>2,'group_name'=>'医生'],
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            if ($user['type_id'] == 1 ){
                $group_list  = [
                    ['group_id'=>2,'group_name'=>'医生'],
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            if ($user['type_id'] == 2 ){
                $group_list  = [
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            $this->assign("group_list", $group_list);

            //获取上级
            $aroup_list  = [
            ];
            if ($user['group_id'] == 1  ){
                $aroup_list  = [
                ];
                $userlist = model('user')->getList([['type_id','=',1]], '*');
                foreach ($userlist as $key=>$vo){
                    $auserlist = model('user')->getList([['source_uid','=',$vo['uid']],['type_id','=',2]], '*');
                    $aroup_list[]=['group_id'=>$vo['uid'],'group_name'=>'(机构)'.$vo['username']];
                    foreach ($auserlist as $k=>$v){
                        $aroup_list[]=['group_id'=>$v['uid'],'group_name'=>'(医生)'.$v['username']];
                    }
                }
            }
            if ($user['type_id'] == 1  ){
                $aroup_list  = [
                    ['group_id'=>$this->uid,'group_name'=>'(机构)'.$user['username']]
                ];
                $userlist = model('user')->getList([['source_uid','=',$this->uid],['type_id','=',2]], '*');
                foreach ($userlist as $key=>$vo){
                    $aroup_list[]=['group_id'=>$vo['uid'],'group_name'=>'(医生)'.$vo['username']];
                }
            }
            if ($user['type_id'] == 2  ){
                $aroup_list  = [
                    ['group_id'=>$this->uid,'group_name'=>'(医生)'.$user['username']]
                ];
            }
            $this->assign("aroup_list", $aroup_list);

            //产品经理
            $user_agent = model('user_agent')->getList();
            $this->assign("user_agent", $user_agent);

            return $this->fetch("user/add_invuser");
        }
    }

    /**
     * 编辑用户
     * @return mixed
     */
    public function editUser()
    {
        $user_model = new UserModel();
        if (request()->isAjax()) {
            $group_id = input("group_id", "");
            $status   = input("status", "");
            $uid      = input("uid", 0);

            $condition = array(
                ["uid", "=", $uid],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $data      = array(
                "group_id" => $group_id,
                "status"   => $status
            );

            $this->addLog("编辑用户:" . $uid);

            $result = $user_model->editUser($data, $condition);
            return $result;
        } else {
            $uid = input("uid", 0);
            $this->assign("uid", $uid);

            //用户信息
            $condition        = array(
                ["uid", "=", $uid],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $user_info_result = $user_model->getUserInfo($condition);
            $user_info        = $user_info_result["data"];
            $this->assign("edit_user_info", $user_info);

            //用户组
            $group_model       = new Group();
            $group_list_result = $group_model->getGroupList([["site_id", "=", $this->site_id], ["app_module", "=", $this->app_module]]);
            $group_list        = $group_list_result["data"];
            $this->assign("group_list", $group_list);

            return $this->fetch("user/edit_user");
        }

    }

    /**
     * 编辑用户
     * @return mixed
     */
    public function editinvUser()
    {
        $user_model = new UserModel();
        if (request()->isAjax()) {
            $type_id = input("group_id", "");
            $agent_uid = input("agent_uid", 0);
            $source_uid = input("aroup_id", "");
            $group_id = 2;
            $status   = input("status", "");
            $uid      = input("uid", 0);
            $username = input("username","");
            //判断上级级别必须大于下级
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                if (!$source_uid){
                    return error(-1,'请选择上级用户');
                }
            }
            if (!$username){
                if (!$source_uid){
                    return error(-1,'用户名不能为空');
                }
            }
            $exituser = model('user')->getInfo([['username', "=", $username],['uid', "<>", $uid]]);
            if ($exituser){
                return error(-1,'用户名已存在，请更换用户名');
            }
            if ($source_uid){
                $user_info = model('user')->getInfo(
                    [
                        ['uid', "=", $source_uid]
                    ]
                );
                if ($user_info['type_id']<$type_id){

                }else{
                    return error(-1,'用户上级级别必须大于该级别');
                }
            }
            $condition = array(
                ["uid", "=", $uid],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $data      = array(
                "group_id" => $group_id,
                "source_uid" => $source_uid,
                "type_id"    => $type_id,
                "status"   => $status,
                "username" => $username,
                "agent_uid" => $agent_uid
            );

            if ($this->uid==1){
                $data['pay_close']      = input("pay_close", 0);
                $data['isbalance']      = input("isbalance", 0);
                $data['ispaybalance']   = input("ispaybalance", 0);
                $data['headimg']        = input("default_headimg", 0);
                $data['pay_line']      = input("pay_line", 0);
                $data['balancediscount']      = input("balancediscount", 1);
                $data['pay_close']=$data['pay_close']==1?0:1;
            }

            //同步关闭支付，余额支付，折扣设置
            $bp_error = 1;
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if ($user_bpdotocr[$uid]['bp'] && $user_bpdotocr[$uid]['bp']!=$uid) {
                $bp_user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$uid]['bp']]]);
                $data['pay_close']      = $bp_user['pay_close'];
                $data['isbalance']      = $bp_user['isbalance'];
                $data['ispaybalance']   = $bp_user['ispaybalance'];
            //    $data['pay_line']      = $bp_user['pay_line'];
                $data['balancediscount']      = $bp_user['balancediscount'];
                $bp_error=0;
            }
            if ($user_bpdotocr[$uid]['doctor'] && $bp_error == 1 && $user_bpdotocr[$uid]['doctor']!=$uid) {
                $bp_user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$uid]['doctor']]]);
                $data['pay_close']      = $bp_user['pay_close'];
                $data['isbalance']      = $bp_user['isbalance'];
                $data['ispaybalance']   = $bp_user['ispaybalance'];
          //      $data['pay_line']      = $bp_user['pay_line'];
                $data['balancediscount']      = $bp_user['balancediscount'];
            }

            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if ($type_id > 0){
                if (isset($datau[$uid])){
                    $datu       = array_merge($datau[$uid]['patient'],$datau[$uid]['doctor']);

                }else{
                    $datu = [];
                    $datu[] = $uid;
                }

                $dataa      = array(
                    "pay_close" => $data['pay_close'],
                    "isbalance" => $data['isbalance'],
                    "ispaybalance" => $data['ispaybalance'],
                 //   "pay_line" => $data['pay_line'],
                    "balancediscount" => $data['balancediscount']
                );
                $pay_condition = array(
                    ["site_id", "=", $this->site_id],
                    ["app_module", "=", $this->app_module],
                );
                $pay_condition[] = ["uid", "in", $datu];
                $resultt = $user_model->editUser($dataa, $pay_condition);
            }


            $this->addLog("编辑用户:" . $uid);

            $result = $user_model->editUser($data, $condition);
            return $result;
        } else {
            $uid = input("uid", 0);
            $this->assign("uid", $uid);
            $this->assign("sys_uid", $this->uid);

            //用户信息
            $condition        = array(
                ["uid", "=", $uid],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $user_info_result = $user_model->getUserInfo($condition);
            $user_info        = $user_info_result["data"];
            //显示bp产品经理
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            if (isset($user_bpdotocr[$uid]['agent_uid']) && $user_bpdotocr[$uid]['agent_uid']){
                $user_info['agent_uid'] = $user_bpdotocr[$uid]['agent_uid'];
            }
            $this->assign("edit_user_info", $user_info);

            //根据等级显示用户标签
            $group_list  = [
            ];
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 1 ){
                $group_list  = [
                    ['group_id'=>1,'group_name'=>'机构'],
                    ['group_id'=>2,'group_name'=>'医生'],
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            if ($user['type_id'] == 1 ){
                $group_list  = [
                    ['group_id'=>2,'group_name'=>'医生'],
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            if ($user['type_id'] == 2 ){
                $group_list  = [
                    ['group_id'=>3,'group_name'=>'健康营养师']
                ];
            }
            $this->assign("group_list", $group_list);

            //获取上级
            $aroup_list  = [
            ];
            if ($user['group_id'] == 1  ){
                $aroup_list  = [
                ];
                $userlist = model('user')->getList([['type_id','=',1]], '*');
                foreach ($userlist as $key=>$vo){
                    $auserlist = model('user')->getList([['source_uid','=',$vo['uid']],['type_id','=',2]], '*');
                    $aroup_list[]=['group_id'=>$vo['uid'],'group_name'=>'(机构)'.$vo['username']];
                    foreach ($auserlist as $k=>$v){
                        $aroup_list[]=['group_id'=>$v['uid'],'group_name'=>'(医生)'.$v['username']];
                    }
                }
            }
            if ($user['type_id'] == 1  ){
                $aroup_list  = [
                    ['group_id'=>$this->uid,'group_name'=>'(机构)'.$user['username']]
                ];
                $userlist = model('user')->getList([['source_uid','=',$this->uid],['type_id','=',2]], '*');
                foreach ($userlist as $key=>$vo){
                    $aroup_list[]=['group_id'=>$vo['uid'],'group_name'=>'(医生)'.$vo['username']];
                }
            }
            if ($user['type_id'] == 2  ){
                $aroup_list  = [
                    ['group_id'=>$this->uid,'group_name'=>'(医生)'.$user['username']]
                ];
            }
            $this->assign("aroup_list", $aroup_list);

            //产品经理
            $user_agent = model('user_agent')->getList();
            $this->assign("user_agent", $user_agent);

            return $this->fetch("user/edit_invuser");
        }

    }

    /**
     * 删除用户
     */
    public function deleteUser()
    {
        if (request()->isAjax()) {
            $uid        = input("uid", 0);
            $user_model = new UserModel();
            $condition  = array(
                ["uid", "=", $uid],
                ["app_module", "=", $this->app_module],
                ["site_id", "=", $this->site_id],
            );
            $result     = $user_model->deleteUser($condition);
            return $result;
        }
    }

    public function deleteinvUser()
    {
        if (request()->isAjax()) {
            $uid        = input("uid", 0);
            $user_model = new UserModel();
            $condition  = array(
                ["uid", "=", $uid],
                ["app_module", "=", $this->app_module],
                ["site_id", "=", $this->site_id],
            );
            $result     = $user_model->deleteUser($condition);
            return $result;
        }
    }

    /**
     * 编辑管理员状态
     */
    public function modifyUserStatus()
    {
        if (request()->isAjax()) {
            $uid        = input('uid', 0);
            $status     = input('status', 0);
            $user_model = new UserModel();
            $condition  = array(
                ["uid", "=", $uid],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $result     = $user_model->modifyUserStatus($status, $condition);
            return $result;
        }
    }

    /**
     * 重置密码
     */
    public function modifyPassword()
    {
        if (request()->isAjax()) {
            $password   = input('password', '123456');
            $uid        = input('uid', 0);
            $site_id    = $this->site_id;
            $user_model = new UserModel();
            return $user_model->modifyUserPassword($password, [['uid', '=', $uid], ['site_id', '=', $site_id]]);
        }
    }

    public function modifybalance()
    {
        if (request()->isAjax()) {
            $balance   = input('balance', 0);
            $remark        = input('remark', '');
            $uid        = input('uid', 0);
            $site_id    = $this->site_id;
            $user_model = new UserModel();
            return $user_model->modifyUserbalance($balance,$remark,$uid, [['uid', '=', $uid], ['site_id', '=', $site_id]]);
        }
    }

    public function modifybalance_dec()
    {
        if (request()->isAjax()) {
            $balance   = input('balance_dec', 0);
            $remark        = input('remark_dec', '');
            $uid        = input('uid', 0);
            $site_id    = $this->site_id;
            $user_model = new UserModel();
            return $user_model->modifyUserbalance_dec($balance,$remark,$uid, [['uid', '=', $uid], ['site_id', '=', $site_id]]);
        }
    }

    public function modifybalance_non()
    {
        if (request()->isAjax()) {
            $balance   = input('balance_non', 0);
            $remark        = input('remark_non', '');
            $uid        = input('uid', 0);
            $site_id    = $this->site_id;
            $user_model = new UserModel();
            return $user_model->modifyUserbalance_non($balance,$remark,$uid, [['uid', '=', $uid], ['site_id', '=', $site_id]]);
        }
    }


    /**
     * 用户列表
     * @return mixed
     */
    public function group()
    {
        if (request()->isAjax()) {
            $page        = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");

            $condition = array(
                ['site_id', "=", $this->site_id],
                ["app_module", "=", $this->app_module]
            );
            if (!empty($search_keys)) {
                $condition[] = ['desc', 'like', '%' . $search_keys . '%'];
            }

            $group_model = new Group();
            $list        = $group_model->getGroupPageList($condition, $page, $page_size);
            return $list;
        } else {
            $this->forthMenu();
            return $this->fetch("user/group_list");
        }
    }

    /**
     * 添加用户组
     * @return mixed
     */
    public function addGroup()
    {
        if (request()->isAjax()) {
            $group_name  = input('group_name', '');
            $menu_array  = input('menu_array', '');
            $desc        = input('desc', '');
            $group_model = new Group();
            $data        = array(
                "group_name"   => $group_name,
                "site_id"      => $this->site_id,
                "app_module"   => $this->app_module,
                "group_status" => 1,
                "menu_array"   => $menu_array,
                "desc"         => $desc,
                "is_system"    => 0
            );
            $result      = $group_model->addGroup($data);
            return $result;
        } else {
            $menu_model = new Menu();
            $menu_list  = $menu_model->getMenuList([['app_module', '=', $this->app_module]], '*', 'sort ASC');
            $menu_tree  = list_to_tree($menu_list['data'], 'name', 'parent', 'child_list', '');
            $this->assign('tree_data', $menu_tree);
            return $this->fetch('user/add_group');
        }
    }

    /**
     * 编辑用户组
     * @return mixed
     */
    public function editGroup()
    {
        $group_model = new Group();
        if (request()->isAjax()) {
            $group_name = input('group_name', '');
            $menu_array = input('menu_array', '');
            $group_id   = input('group_id', 0);
            $desc       = input('desc', '');

            $data      = array(
                "group_name" => $group_name,
                "menu_array" => $menu_array,
                "desc"       => $desc,
            );
            $condition = array(
                ["group_id", "=", $group_id],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module]
            );
            $result    = $group_model->editGroup($data, $condition);
            return $result;
        } else {
            $group_id          = input('group_id', 0);
            $condition         = array(
                ["group_id", "=", $group_id],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module]
            );
            $group_info_result = $group_model->getGroupInfo($condition);
            $group_info        = $group_info_result["data"];
            $this->assign("group_info", $group_info);
            $this->assign("group_id", $group_id);

            //获取菜单权限
            $menu_model = new Menu();
            $menu_list  = $menu_model->getMenuList([['app_module', '=', $this->app_module]], '*', 'sort ASC');

            //处理选中数据
            $group_array   = $group_info['menu_array'];
            $checked_array = explode(',', $group_array);
            foreach ($menu_list['data'] as $key => $val) {
                if (in_array($val['name'], $checked_array)) {
                    $menu_list['data'][$key]['checked'] = true;
                } else {
                    $menu_list['data'][$key]['checked'] = false;
                }
            }
            $menu_tree = list_to_tree($menu_list['data'], 'name', 'parent', 'child_list', '');
            $this->assign('tree_data', $menu_tree);

            return $this->fetch('user/edit_group');
        }
    }

    /**
     * 删除用户组
     */
    public function deleteGroup()
    {
        if (request()->isAjax()) {
            $group_id    = input('group_id', '');
            $condition   = array(
                ["group_id", "=", $group_id],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $group_model = new Group();
            $result      = $group_model->deleteGroup($condition);
            return $result;
        }
    }

    /**
     * 用户组状态
     */
    public function modifyGroupStatus()
    {
        if (request()->isAjax()) {
            $group_id    = input('group_id', 0);
            $status      = input('status', 0);
            $group_model = new Group();
            $condition   = array(
                ["group_id", "=", $group_id],
                ["site_id", "=", $this->site_id],
                ["app_module", "=", $this->app_module],
            );
            $result      = $group_model->modifyGroupStatus($status, $condition);
            return $result;
        }
    }

    /**
     * 用户日志
     */
    public function userLog()
    {
        $user_model = new UserModel();
        if (request()->isAjax()) {
            $page      = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $uid       = input('uid', '0');

            $condition   = [];
            $condition[] = ["site_id", "=", $this->site_id];
            $search_keys = input('search_keys', "");
            if (!empty($search_keys)) {
                $condition[] = ['action_name', 'like', '%' . $search_keys . '%'];
            }
            if ($uid > 0) {
                $condition[] = ['uid', '=', $uid];
            }

            $list = $user_model->getUserlogPageList($condition, $page, $page_size, "create_time desc");
            return $list;
        } else {
            $this->forthMenu();

            //获取站点所有用户
            $condition        = [];
            $condition[]      = ["site_id", "=", $this->site_id];
            $condition[]      = ["app_module", "=", $this->app_module];
            $user_list_result = $user_model->getUserList($condition);
            $user_list        = $user_list_result["data"];
            $this->assign("user_list", $user_list);

            return $this->fetch('user/user_log');
        }
    }

    /**
     * 批量删除日志
     */
    public function deleteUserLog()
    {
        if (request()->isAjax()) {
            $user_model = new UserModel();
            $id         = input("id", "");
            $condition  = array(
                ["id", "in", $id],
                ["site_id", '=', $this->site_id],
            );
            $res        = $user_model->deleteUserLog($condition);
            return $res;
        }
    }

    public function defaultPicture()
    {
        if (request()->isAjax()) {
            $data = array(
                // "default_goods_img" => input("default_goods_img", ""),
                "headimg"   => input("default_headimg", ""),
            );
            $this->addLog("修改说明单机构标识");
            $condition = array(
                ["uid", "=", $this->uid]
            );
            $res = model("user")->update($data, $condition);

            return $res;
        } else {
            $user = model("user")->getInfo([["uid", "=", $this->uid]], "*");
            $this->assign("default_img", $user['headimg']);
            return $this->fetch('user/default_picture');
        }
    }
}