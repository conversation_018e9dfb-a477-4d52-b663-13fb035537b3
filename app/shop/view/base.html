<!DOCTYPE html>
<html>
<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title>{$menu_info['title']|default=""} - {$shop_info['site_name']|default=""}</title>
	<meta name="keywords" content="$shop_info['seo_keywords']}">
	<meta name="description" content="$shop_info['seo_description']}">
	<link rel="icon" type="image/x-icon" href="__STATIC__/img/shop_bitbug_favicon.ico" />
	<link rel="stylesheet" type="text/css" href="STATIC_CSS/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="SHOP_CSS/common.css" />
	<script src="__STATIC__/js/jquery-3.1.1.js"></script>
	<script src="__STATIC__/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function() {});

		window.ns_url = {
			baseUrl: "ROOT_URL/",
			route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
		};
	</script>
	<script src="__STATIC__/js/common.js"></script>
	<script src="SHOP_JS/common.js"></script>
	<style>
		.ns-calendar{background: url("__STATIC__/img/ns_calendar.png") no-repeat center / 16px 16px;}
		.layui-logo{height: 100%;display: flex;align-items: center;}
		.layui-logo a{display: flex;justify-content: center;align-items: center;width: 200px;height: 50px;}
		.layui-logo a img{max-height: 100%;max-width: 100%;}
		.goods-preview .qrcode-wrap {max-width: 130px; max-height: 130px; overflow: hidden;}
		.goods-preview .qrcode-wrap input {margin-top: 30px;}
		.marquee-container {
			width: 60%; /* 容器宽度 */
			overflow: hidden; /* 隐藏超出容器的部分 */
			white-space: nowrap; /* 文本不换行 */
			box-sizing: border-box;
			position: relative;
			border: none; /* 可选，仅为了清晰看到容器边界 */
		}

		.marquee {
			display: inline-block;
			padding-left: 100%; /* 初始时文本在容器右侧不可见 */
			animation: marquee-animation 30s linear infinite; /* 动画名称、持续时间、速度曲线、循环次数 */
			text-decoration: none; /* 去除下划线 */
			color: #000; /* 文本颜色 */
			line-height: 60px;
		}

		@keyframes marquee-animation {
			from {
				transform: translateX(0);
			}
			to {
				transform: translateX(-100%); /* 向左移动，直到文本完全移出容器 */
			}
		}

		/* 可选：鼠标悬停时停止动画 */
		.marquee:hover {
			animation-play-state: paused;
		}

		/* 模态框（背景） */
		.modal {
			display: none; /* 默认隐藏 */
			position: fixed; /* 停留位置 */
			z-index: 1; /* 位于顶层 */
			left: 0;
			top: 0;
			width: 100%; /* 全屏宽 */
			height: 100%; /* 全屏高 */
			overflow: auto; /* 启用滚动条如果需要 */
			background-color: rgb(0,0,0); /* 背景色 */
			background-color: rgba(0,0,0,0.4); /* 黑色背景且有透明度 */
		}

		/* 模态内容（前景） */
		.modal-content {
			background-color: #fefefe;
			margin: 15% auto; /* 15% 从顶部和 50% 自动边距 */
			padding: 20px;
			border: 1px solid #888;
			width: 70%; /* 宽度 */
		}

		/* 关闭按钮 */
		.close {
			color: #aaa;
			float: right;
			font-size: 28px;
			font-weight: bold;
		}

		.close:hover,
		.close:focus {
			color: black;
			text-decoration: none;
			cursor: pointer;
		}

		/* 可滑动内容区域 */
		.modal-body {
			max-height: 700px; /* 设置最大高度 */
			overflow-y: auto; /* 启用垂直滚动 */
		}
	</style>
	{block name="resources"}{/block}
</head>

<body>
{block name="body"}
<div class="layui-layout layui-layout-admin">
	{block name='head'}
	<div class="layui-header">
		<div class="layui-nav layui-layout-left marquee-container">
			<div class="marquee">
				{foreach $notice_back as $key => $vo}
				<a style="color: red;font-size: 18px;margin-right: 200px;" href="/shop/notice/detail.html?id={$vo['id']}">{$vo['title']}</a>
				{/foreach}
			</div>
		</div>
		<!-- 账号 -->
		<div class="ns-login-box layui-layout-right">
			<div class="ns-shop-ewm">
				<a href="/shop/shop/video1.html">
					<span style="color: red;font-size: 18px;">系统使用指南</span>
				</a>
			</div>
			<div class="ns-shop-ewm">
				<a href="/shop/shop/video2.html">
					<span style="color: red;font-size: 18px;">开箱使用指南</span>
				</a>
			</div>
			<div class="ns-shop-ewm">

				<a href="{:url('shop/goods/lists')}">
					{if $baojingnotice}
					<span style="color: red;font-size: 18px;">{$baojingnotice}</span>
					{/if}
				</a>
			</div>
		</div>
	</div>
	<div class="layui-header">
		<div class="layui-logo">
			<a href="{:url('shop/index/index')}">
				{notempty name='$shop_info.logo'}
				<img src="{:img($shop_info.logo)}" />
				{else/}
				<img src="SHOP_IMG/shop_logo.png">
				{/notempty}
			</a>
		</div>
		<ul class="layui-nav layui-layout-left">
			{foreach $menu as $menu_k => $menu_v}
			<li class="layui-nav-item">
				<a href="{$menu_v.url}" {if $menu_v.selected}class="active"{/if}>
				{if $menu_v.title=="AI方案助手"}
				<img src="/app/shop/view/public/img/menu_icon/ai.jpg" style="width: 90px;float: left;" />
				{else /}
				<span>{$menu_v.title}</span>
				{/if}
				</a>
			</li>
			{if $menu_v.selected}
			{php}
			$second_menu = $menu_v["child_list"];
			{/php}
			{/if}
			{/foreach}
		</ul>

		<!-- 账号 -->
		<div class="ns-login-box layui-layout-right">

			<div class="ns-shop-ewm">

				<a href="{:url('shop/notice/index')}"><img src="https://mianyangkeji.oss-cn-shanghai.aliyuncs.com/png/2022/02/WeChat_1644391504.png" style="width: 40px;" />
					{if $notice}
					<span style="color: red;font-size: 18px;">{$notice}</span>
					{/if}
				</a>
			</div>

			<ul class="layui-nav ns-head-account">
				<li class="layui-nav-item layuimini-setting">
					<a href="javascript:;">
						{$user_info['username']}</a>
					<dl class="layui-nav-child">
						<dd class="ns-reset-pass" onclick="resetPassword();">
							<a href="javascript:;">修改密码</a>
						</dd>
						{if $userr['balance_tixing_is']}
						<dd class="ns-balance-pass" onclick="resetBalance();">
							<a href="javascript:;">余额提醒</a>
						</dd>
						{/if}
						<dd class="ns-gexinghua-pass" onclick="resetGexinghua();">
							<a href="javascript:;">个性化说明单</a>
						</dd>
						{if $userr['source_uid']==0}
						<dd class="ns-gexinghua-passs">
                            <a href="/shop/user/defaultPicture.html">机构Logo</a>
                        </dd>
						{/if}
						<dd>
							<a onclick="clearCache()" href="javascript:;">清除缓存</a>
						</dd>
						<dd>
							<a href="{:addon_url('shop/login/logout')}" class="login-out">退出登录</a>
						</dd>
					</dl>
				</li>
			</ul>
		</div>
	</div>
	{/block}

	{block name='side_menu'}
	{notempty name='$second_menu'}
	<div class="layui-side ns-second-nav">
		<div class="layui-side-scroll">

			<!--二级菜单 -->
			<ul class="layui-nav layui-nav-tree">
				{foreach $second_menu as $menu_second_k => $menu_second_v}
				<li class="layui-nav-item {if $menu_second_v.selected}layui-this layui-nav-itemed{/if}">
					<a href="{empty name=" $menu_second_v.child_list"}{$menu_second_v.url}{else /}javascript:;{/empty}" class="layui-menu-tips">
					<div class="stair-menu{if $menu_v.selected} ative{/if}">
						<img src="__ROOT__/{$menu_second_v.icon}" alt="">
					</div>
					<span>{$menu_second_v.title}</span>
					</a>

					{notempty name="$menu_second_v.child_list"}
					<dl class="layui-nav-child">
						{foreach $menu_second_v["child_list"] as $menu_third_k => $menu_third_v}
						<dd class="{if $menu_third_v.selected} layui-this{/if}">
							<a href="{$menu_third_v.url}" class="layui-menu-tips">
								<span class="layui-left-nav">{$menu_third_v.title}</span>
							</a>
						</dd>
						{/foreach}
					</dl>
					{/notempty}
				</li>
				{/foreach}
			</ul>
		</div>
	</div>
	{/notempty}
	{/block}

	<!-- 面包屑 -->
	{block name='crumbs'}
	{notempty name="$second_menu"}
	<div class="ns-crumbs{notempty name='$second_menu'} submenu-existence{/notempty}">
			<span class="layui-breadcrumb" lay-separator="-">
				{foreach $crumbs as $crumbs_k => $crumbs_v}
				{if count($crumbs) >= 3}
					{if $crumbs_k == 1}
					<a href="{$crumbs_v.url}">{$crumbs_v.title}</a>
					{/if}
					{if $crumbs_k == 2}
					<a><cite>{$crumbs_v.title}</cite></a>
					{/if}
				{else/}
					{if $crumbs_k == 1}
					<a><cite>{$crumbs_v.title}</cite></a>
					{/if}
				{/if}
				{/foreach}
			</span>
	</div>
	{/notempty}
	{/block}

	{empty name="$second_menu"}
	<div class="ns-body layui-body" style="left: 0; top: 120px;">
		{else /}
		<div class="ns-body layui-body">
			{/empty}
			<!-- 内容 -->
			<div class="ns-body-content">
				<!-- 四级导航 -->
				{if condition="isset($forth_menu) && !empty($forth_menu)"}
				<div class="fourstage-nav layui-tab layui-tab-brief" lay-filter="edit_user_tab">
					<ul class="layui-tab-title">
						{volist name="$forth_menu" id="menu"}
						<li class="{$menu.selected == 1 ? 'layui-this' : ''}" lay-id="basic_info"><a href="{$menu.parse_url}">{$menu.title}</a></li>
						{/volist}
					</ul>
				</div>
				{/if}

				{block name="main"}{/block}
			</div>

			<!-- 版权信息 -->
			<div class="ns-footer">
				<a class="ns-footer-img" href="#"></a>
				<p>个性化营养素定制系统</p>
			</div>
		</div>
	</div>
</div>

<!-- 个性化说明html -->
<div class="layui-form" id="gexinghua_pass" style="display: none;">
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="1"  title="默认样式(中文/英文产品名＋图片＋剂量＋主要成分＋产品说明)" {$userr.gexinghua == 1 ? 'checked' : ''} />
	</div>
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="2"  title="个性化样式一(中文＋图片＋剂量＋主要成分＋产品说明)" {$userr.gexinghua == 2 ? 'checked' : ''}  />
	</div>
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="3"  title="个性化样式二(中文/英文产品名＋图片＋剂量＋主要成分)" {$userr.gexinghua == 3 ? 'checked' : ''}  />
	</div>
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="4"  title="个性化样式三(中文/英文产品名＋图片＋剂量＋产品说明)" {$userr.gexinghua == 4 ? 'checked' : ''}  />
	</div>
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="5"  title="个性化样式四(中文＋图片＋剂量)" {$userr.gexinghua == 5 ? 'checked' : ''}  />
	</div>
	<div class="layui-form-item">
		<input type="radio" name="gexinghua" value="6"  title="个性化样式五(中文/英文产品名＋图片(粉剂图片默认)＋剂量＋产品说明)" {$userr.gexinghua == 6 ? 'checked' : ''}  />
	</div>


	<div class="ns-form-row mid">
		<button class="layui-btn ns-bg-color" onclick="regexinghua()">确定</button>
		<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
	</div>
</div>
<!-- 个性化说明html -->
<div class="layui-form" id="fankui" style="display: none;">
	<div class="layui-form-item">
		<div style="padding: 20px;">
			<p style="color:#FF6A00;text-align: center;font-size: large">您的订单已提交成功!</p>
			<p style="color:#FF6A00;margin-top:10px;margin-bottom:10px;">如有意见，请提交反馈。如果没有请返回</p>
			<input type="text" id="confirmInput" class="layui-input" placeholder="请输入...">
		</div>
	</div>



	<div class="ns-form-row mid">
		<button class="layui-btn ns-bg-color" onclick="regefankui()">填写建议，提交反馈</button>
		<button class="layui-btn layui-btn-primary" onclick="closefankui()">无需反馈，返回列表</button>
	</div>
</div>
<!-- 重置密码弹框html -->
<div class="layui-form" id="reset_pass" style="display: none;">
	<div class="layui-form-item">
		<label class="layui-form-label mid"><span class="required">*</span>原密码</label>
		<div class="layui-input-block">
			<input type="password" id="old_pass" name="old_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			<!-- <span class="required"></span> -->
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
		<div class="layui-input-block">
			<input type="password" id="new_pass" name="new_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			<!-- <span class="required"></span> -->
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
		<div class="layui-input-block">
			<input type="password" id="repeat_pass" name="repeat_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">

		</div>
	</div>

	<div class="ns-form-row mid">
		<button class="layui-btn ns-bg-color" onclick="repass()">确定</button>
		<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
	</div>
</div>

<div class="layui-form" id="balance_pass" style="display: none;">
	<div class="layui-form-item">
		<label class="layui-form-label mid">当前余额</label>
		<div class="layui-input-block">
			{$userr['balance']}
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label mid"><span class="required">*</span>余额提醒</label>
		<div class="layui-input-block">
			<input type="number" id="balance_tixing" name="balance_tixing" value="{$userr['balance_tixing']}" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			<!-- <span class="required"></span> -->
		</div>
	</div>

	<div class="ns-form-row mid">
		<button class="layui-btn ns-bg-color" onclick="repass_balance()">确定</button>
		<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
	</div>
</div>

<div id="myModal" class="modal">

	<!-- 模态内容（前景） -->
	<div class="modal-content" style="margin-top: 200px;">
		<span class="close">&times;</span>
		<div class="modal-body" style="max-height: 700px; overflow-y: auto;">
			<h3 class="title" style="text-align: center;font-size: large;font-weight: 700;">{$notice_title}</h3>
			<!-- 这里是可滑动的内容 -->
			{:html_entity_decode($notice_content)}
			<!-- 添加更多内容... -->
		</div>
	</div>

</div>

<script type="text/javascript">
	layui.use('element',function () {
		var element = layui.element;
		element.render('breadcrumb');
	});

	function clearCache () {
		$.ajax({
			type: 'post',
			url: ns.url("shop/Login/clearCache"),
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				location.reload();
			}
		})
	}

	/**
	 * 个性化说明单
	 */
	function resetGexinghua() {
		index = layer.open({
			title:'个性化模板定制',
			type:1,
			content:$('#gexinghua_pass'),
			offset: 'auto',
			area: ['700px']
		});

		setTimeout(function() {
			$(".ns-gexinghua-pass").removeClass('layui-this');
		}, 1000);
	}

	function resetShouhuo() {
		index = layer.open({
			title:'是否固定收货地址',
			type:1,
			content:$('#gexinghua_pass'),
			offset: 'auto',
			area: ['700px']
		});

		setTimeout(function() {
			$(".ns-gexinghua-pass").removeClass('layui-this');
		}, 1000);
	}

	var repeat_flag = false;
	function regexinghua(){
		var gexinghua = $("input[name='gexinghua']:checked").val();



		if(repeat_flag)return;
		repeat_flag = true;

		$.ajax({
			type: "POST",
			dataType: 'JSON',
			url: ns.url("shop/login/modifygexinghua"),
			data: {"gexinghua": gexinghua},
			success: function(res) {
				layer.msg(res.message);
				repeat_flag = false;

				if (res.code == 0) {
					layer.close(index);
					location.reload();
				}
			}
		});
	}

	function regefankui(){
		var inputValue = $('#confirmInput').val();

		if (!inputValue){
			layer.msg('填写建议不能为空，感谢您的反馈。');
			return;
		}



		if(repeat_flag)return;
		repeat_flag = true;

		$.ajax({
			type: "POST",
			dataType: 'JSON',
			url: ns.url("shop/help/addHelp"),
			data: {"content": inputValue},
			success: function(res) {
				layer.msg(res.message);
				repeat_flag = false;

				if (res.code == 0) {
					layer.close(index);
					location.href = ns.url("shop/order/lists");
				}
			}
		});
	}

	function closefankui() {
		location.href = ns.url("shop/order/lists");
	}


	/**
	 * 重置密码
	 */
	var index;
	function resetPassword() {
		index = layer.open({
			type:1,
			content:$('#reset_pass'),
			offset: 'auto',
			area: ['500px']
		});

		setTimeout(function() {
			$(".ns-reset-pass").removeClass('layui-this');
		}, 1000);
	}

	function resetBalance() {
		index = layer.open({
			type:1,
			content:$('#balance_pass'),
			offset: 'auto',
			area: ['500px']
		});

		setTimeout(function() {
			$(".ns-balance-pass").removeClass('layui-this');
		}, 1000);
	}

	var repeat_flag = false;
	function repass(){
		var old_pass = $("#old_pass").val();
		var new_pass = $("#new_pass").val();
		var repeat_pass = $("#repeat_pass").val();

		if (old_pass == '') {
			$("#old_pass").focus();
			layer.msg("原密码不能为空");
			return;
		}

		if (new_pass == '') {
			$("#new_pass").focus();
			layer.msg("新密码不能为空");
			return;
		} else if (new_pass == old_pass) {
			$("#new_pass").focus();
			layer.msg("新密码不能与原密码一致");
			return;
		} else if ($("#new_pass").val().length < 6) {
			$("#new_pass").focus();
			layer.msg("密码不能少于6位数");
			return;
		}
		if (repeat_pass == '') {
			$("#repeat_pass").focus();
			layer.msg("密码不能为空");
			return;
		} else if ($("#repeat_pass").val().length < 6) {
			$("#repeat_pass").focus();
			layer.msg("密码不能少于6位数");
			return;
		}
		if (new_pass != repeat_pass) {
			$("#repeat_pass").focus();
			layer.msg("两次密码输入不一致，请重新输入");
			return;
		}

		if(repeat_flag)return;
		repeat_flag = true;

		$.ajax({
			type: "POST",
			dataType: 'JSON',
			url: ns.url("shop/login/modifypassword"),
			data: {"old_pass": old_pass,"new_pass": new_pass},
			success: function(res) {
				layer.msg(res.message);
				repeat_flag = false;

				if (res.code == 0) {
					layer.close(index);
					location.reload();
				}
			}
		});
	}

	function repass_balance(){
		var balance_tixing = $("#balance_tixing").val();

		if(repeat_flag)return;
		repeat_flag = true;

		$.ajax({
			type: "POST",
			dataType: 'JSON',
			url: ns.url("shop/login/modifybalance"),
			data: {"balance_tixing": balance_tixing},
			success: function(res) {
				layer.msg(res.message);
				repeat_flag = false;

				if (res.code == 0) {
					layer.close(index);
					location.reload();
				}
			}
		});
	}

	function closePass() {
		layer.close(index);
	}

	layui.use('element', function() {
		var element = layui.element;
		element.init();
	});
	var modal = document.getElementById("myModal");
	var span = document.getElementsByClassName("close")[0];
	// 当用户点击 <span> (x), 关闭模态框
	span.onclick = function() {
		modal.style.display = "none";
	}

	// 当用户点击模态框外部时，关闭它
	window.onclick = function(event) {
		if (event.target == modal) {
			modal.style.display = "none";
		}
	}
	function release(){
		{if !empty($notice_content) }
		modal.style.display = "block";
		{/if}
		}

		function releaseNow(){
			{if empty($base_weapp_config) || empty($base_weapp_config['appid']) }
			layer.open({
				title: '立即发布',
				skin: 'release-layer',
				type: 1,
				area: ['360px', '410px'],
				content: $('#weapp_release').html(),
			});
			{else/}
				location.href = "{:addon_url('weapp://shop/weapp/package')}";
				{/if}
				}
</script>
<!-- 轮训检查新订单 -->
<script type="text/javascript">
	function setCookie(name, value, days) {
		let expires = "";
		if (days) {
			const date = new Date();
			date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
			expires = "; expires=" + date.toUTCString();
		}
		document.cookie = name + "=" + (value || "") + expires + "; path=/";
	}

	function getCookie(name) {
		const nameEQ = name + "=";
		const ca = document.cookie.split(';');
		for (let i = 0; i < ca.length; i++) {
			let c = ca[i];
			while (c.charAt(0) === ' ') c = c.substring(1, c.length);
			if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
		}
		return null;
	}

	let latestOrderNumber = getCookie('latestOrderNumber') || ''; // 从Cookie中获取最新的订单号
	console.log(latestOrderNumber);
	let audioPlayer = null; // 用于播放音乐的Audio对象
	let isPlaying = false; // 标记音乐是否正在播放
    let playingnum = 0;
	// 播放音乐的函数
	function playAudio(url) {
		if (audioPlayer && !audioPlayer.paused) {
			audioPlayer.pause(); // 如果已经在播放，则先暂停
			audioPlayer.currentTime = 0; // 重置播放位置
		}
		playingnum = 0
		audioPlayer = new Audio(url);
		audioPlayer.addEventListener('ended', function onEnded() {
			playingnum = playingnum +1;
			if (playingnum>3){
			}else{
				audioPlayer.currentTime = 0; // 重置播放位置到开始
				audioPlayer.play();
			}
		});
		audioPlayer.play();
		isPlaying = true;
	}

	// 暂停音乐的函数
	function pauseAudio() {
		if (audioPlayer && isPlaying) {
			audioPlayer.pause();
			isPlaying = false;
		}
	}

	// 检查是否有新订单的函数
	function checkForNewOrders(apiUrl, audioUrl) {
		fetch(apiUrl)
				.then(response => response.json())
				.then(data => {
					const newOrderNumber = data.order_id; // 假设API返回的数据中有一个latestOrderNumber字段
					if (Number(newOrderNumber) > Number(latestOrderNumber)){
						// 如果订单号不同，说明有新订单
						latestOrderNumber = newOrderNumber; // 更新最新的订单号
						setCookie('latestOrderNumber', latestOrderNumber, 7); // 将最新的订单号保存到Cookie中，有效期为7天
						playAudio(audioUrl); // 播放音乐
						layer.confirm('您有新的待发货订单需要处理。', {
							title: '操作提示',
							btn: ['立即处理', '稍后处理'],
							yes: function() {
								location.href = ns.url("shop/order/lists");
							},
							btn2: function() {
								pauseAudio();
							}
						});
					}
				})
				.catch(error => console.error('Error fetching orders:', error));
	}


	// 页面加载时调用API检查新订单
	window.onload = function() {
		const apiUrl = ns.url("api/cart/order_devery"); // 替换为您的API端点
		const audioUrl = '/orderdelivery.mp3'; // 替换为您的音频文件路径
		{if $userr['uid']==1 || $userr['uid']==24 }
		checkForNewOrders(apiUrl, audioUrl);

		// 设置一个定时器定期调用API（可选）
		setInterval(() => checkForNewOrders(apiUrl, audioUrl), 60000); // 每60秒检查一次新订单
		{/if}

	};
</script>
<script type="text/javascript">
	$(document).ready(function(){
		release();
	});
</script>

<!-- 店铺预览 -->
<script type="text/html" id="h5_preview">
	<div class="h5_preview">
		<h3 class="title" style="text-align: center">{$notice_title}</h3>
		<div class="desc" style="padding-top: 20px;">{:html_entity_decode($notice_content)}</div>
	</div>
</script>

<script type="text/html" id="weapp_release">
	<div class="weapp-release">
		<h3 class="title">小程序发布</h3>
		<div class="desc">发布小程序需先配置小程序，请配置好之后再进行该操作</div>
		<div class="operation-btns">
			<div>
				<a href="{:addon_url('weapp://shop/weapp/config', [])}" class="layui-btn ns-bg-color">立即配置</a>
			</div>
			<div>
				<a href="https://mp.weixin.qq.com/" target="_blank" class="layui-btn layui-btn-primary">注册小程序账号</a>
			</div>
		</div>
	</div>
</script>
{/block}
{block name="script"}
{/block}
</body>

</html>