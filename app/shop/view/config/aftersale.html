{extend name="base"/}
{block name="resources"}
<style>
	.layui-colla-content li {
		line-height: 30px;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<div class="layui-colla-content layui-show">
			<ul>
				<li>售后保障会在商品详情页面，售后保障切换卡下方展示，内容不超过1000个字符</li>
			</ul>
		</div>
	</div>
</div>

<div class="layui-form ns-form">
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="required">*</span>协议内容：</label>
        <div class="layui-input-inline">
            <script id="editor" type="text/plain" class="ns-special-length" style="height:300px;"></script>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>

	<input type="hidden" name="content" id="setContent" value="{$content.data.content}" />
</div>
{/block}
{block name="script"}
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script>
	var ue = UE.getEditor('editor');
	ue.ready(function() {
		ue.setContent($('#setContent').val());
	});
	
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		// 提交
		form.on('submit(save)', function(data) {
			var html = '';
			ue.ready(function() {
				html = ue.getContent();
			});
			
			data.field.content = html;
			
			if(repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/config/aftersale"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
					layer.msg(res.message);
				}
			});
		});
	});
</script>
{/block}