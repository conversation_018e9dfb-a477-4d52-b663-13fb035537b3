{extend name="base"/}
{block name="resources"}
<style>
	.ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>请谨慎执行该操作，变更该配置会导致原H5和小程序请求失败，所以变更之后请及时更新H5端和小程序</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" value="1" lay-skin="switch" {if condition="$config.is_use == 1"} checked {/if} />
		</div>
		<div class="ns-word-aux">api安全功能开启之后调用前端api需要传输签名串</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">应用公钥：</label>
		<div class="layui-input-block">
			{if condition="$config.value"}
			<textarea name="public_key" class="layui-textarea ns-len-long" placeholder="请输入应用公钥" disabled>{$config.value.public_key}</textarea>
			{else/}
			<textarea name="public_key" class="layui-textarea ns-len-long" placeholder="请输入应用公钥" disabled></textarea>
			{/if}
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">应用私钥：</label>
		<div class="layui-input-block">
			{if condition="$config.value"}
			<textarea name="private_key" class="layui-textarea ns-len-long" placeholder="请输入应用私钥" disabled>{$config.value.private_key}</textarea>
			{else/}
			<textarea name="private_key" class="layui-textarea ns-len-long" placeholder="请输入应用私钥" disabled></textarea>
			{/if}
		</div>
		<div class="ns-word-aux">私钥设置关系系统中api调用传输签名串的编码规则，以及会员token解析，请慎重设置，注意设置之后对应会员要求重新登录获取token</div>
	</div>
	
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="generate">生成秘钥</button>
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form;
			repeat_flag = false; //防重复标识
		form.render();

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/config/api"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(data){
				    layer.msg(data.message);
					repeat_flag = false;
				}
			});
		});
		form.on('submit(generate)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/config/generateRSA"),
				dataType: 'JSON',
				type: 'POST',
				success: function(res){
					$("textarea[name='public_key']").val(res.data.public_key.replace(/(\n$)/g,""));
					$("textarea[name='private_key']").val(res.data.private_key.replace(/(\n$)/g,""));
					repeat_flag = false;
				}
			});
		});
		
	});
</script>
{/block}