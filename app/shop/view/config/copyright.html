{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<style>
	.selected-products, .sales-products, .order-products {
		width: 33.33%;
		float: left;
		padding: 20px;
		box-sizing: border-box;
	}
	.order-detail {
		padding: 15px;
	}
	.order-detail table {
		width: 100%;
		margin-bottom: 15px;
	}
	.order-detail th {
		text-align: right;
		padding-right: 10px;
		width: 30%;
	}
	.order-detail td {
		text-align: left;
	}
</style>
<div class="container">
	<div class="layui-form ns-form" lay-filter="storeform" >
	<!-- 左侧：已选择的商品列表 -->
	<div class="selected-products">
		<h2>报损</h2>
		<div class="layui-form-item">
			<label class="layui-form-label">单位：</label>
			<div class="layui-input-block" id="watermark_type">
				<input type="radio" name="watermark_type"  value="2" title="粒" checked >
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">报损原因：</label>
			<div class="layui-input-block" id="watermark">
				<input type="radio" name="watermark_t"  value="1" title="粘连"  >
				<input type="radio" name="watermark_t"  value="2" title="少粒"  >
				<input type="radio" name="watermark_t"  value="3" title="漏液"  >
				<input type="radio" name="watermark_t"  value="4" title="漏粉"  >
				<input type="radio" name="watermark_t"  value="5" title="破裂"  >
				<input type="radio" name="watermark_t"  value="6" title="变型"  >
				<input type="radio" name="watermark_t"  value="7" title="空壳"  >
				<input type="radio" name="watermark_t"  value="8" title="氧化"  >
				<input type="radio" name="watermark_t"  value="9" title="压药"  >
				<input type="radio" name="watermark_t"  value="10" title="污染"  >
			</div>
		</div>
		<div class="layui-form-item">
			<div>
				<button class="layui-btn ns-bg-color" onclick="addGoods()">添加商品</button>
				<button id="order" class="layui-btn ns-bg-color">发送报损单</button>
				<input id="scannerInput" class="layui-input" autofocus placeholder="请扫码" style="float: left;width: 50%">
			</div>
		</div>
		<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
			<colgroup>
				<col width="10%">
				<col width="10%">
				<col width="10%">
			</colgroup>
			<thead>
			<tr>
				<th>商品名称</th>
				<th>编号</th>
				<th>数量</th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td colspan="4">
					<div class="goods-empty">未添加商品</div>
				</td>
			</tr>
			</tbody>
		</table>
	</div>

	<!-- 中间：按销售量排序的商品列表 -->
	<div class="sales-products">
		<h2>近一月销售商品</h2>
		<div class="layui-form-item">
			<div>
				<input name="saleb" type="text" value="" class="layui-input" style="float: left;width: 85%">
				<button id="saleb" class="layui-btn ns-bg-color" onclick="saleb()">筛选</button>
			</div>
		</div>
		<table class="layui-table" id="goodssale" lay-skin="line" lay-size="lg">
			<colgroup>
				<col width="10%">
				<col width="10%">
				<col width="10%">
			</colgroup>
			<thead>
			<tr>
				<th>商品名称</th>
				<th>编号</th>
				<th>销售量</th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td colspan="4">
					<div class="goods-empty">未添加商品</div>
				</td>
			</tr>
			</tbody>
		</table>
	</div>
	<!-- 右侧：报损单记录表 -->
	<div class="order-products">
			<h2>报损单记录</h2>
			<div class="layui-form-item">
				<div>
					<input name="damage_search" type="text" value="" class="layui-input" style="float: left;width: 85%;">
					<button id="damage_search" class="layui-btn ns-bg-color" onclick="searchDamages()">搜索</button>
				</div>
			</div>
			<table class="layui-table" id="damages" lay-skin="line" lay-size="lg">
				<colgroup>
					<col width="10%">
					<col width="10%">
					<col width="10%">
				</colgroup>
				<thead>
				<tr>
					<th>报损单号</th>
					<th>创建时间</th>
					<th>状态</th>
				</tr>
				</thead>
				<tbody>
				<tr>
					<td colspan="4">
						<div class="goods-empty">暂无报损单</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>

	<!-- 报损单详情弹窗 -->
	<div id="damage-detail" style="display: none;">
			<div class="order-detail">
				<table>
					<tr>
						<th>报损单号：</th>
						<td id="detail-damage-no"></td>
					</tr>
					<tr>
						<th>创建时间：</th>
						<td id="detail-damage-time"></td>
					</tr>
					<tr>
						<th>状态：</th>
						<td id="detail-damage-status"></td>
					</tr>
					<tr>
						<th>报损单位：</th>
						<td id="detail-damage-unit"></td>
					</tr>
					<tr>
						<th>报损原因：</th>
						<td id="detail-damage-reason"></td>
					</tr>
					<tr>
						<th>备注：</th>
						<td id="detail-damage-remark"></td>
					</tr>
				</table>
				<h4>报损商品</h4>
				<table class="layui-table" lay-skin="line">
					<thead>
					<tr>
						<th>商品名称</th>
						<th>编号</th>
						<th>数量</th>
					</tr>
					</thead>
					<tbody id="detail-damage-goods">
					</tbody>
				</table>
			</div>
		</div>

    </div>
</div>
<script>
	var form;
	layui.use(['form'], function() {
		form = layui.form, repeat_flag = false; //防重复标识
		form.render();
	});
</script>
<script>
	var selectGoodsSkuId = []
	// 加载报损单记录
	function loadDamages(search) {
		$.ajax({
			url: '/api/cart/dlist',
			type: 'GET',
			dataType: 'json',
			data: { search: search },
			success: function(res) {
				$("#damages tbody").html('');
				if (res.data && res.data.length > 0) {
					var html = '';
					for (var i in res.data) {
						var item = res.data[i];
						html += "<tr onclick='showDamageDetail(\"" + item.damage_no + "\")' style='cursor:pointer;'>";
						html += "<td>" + item.damage_no + "</td>";
						html += "<td>" + item.create_time + "</td>";
						html += "<td>" + getDamageStatusText(item.status) + "</td>";
						html += "</tr>";
					}
					$("#damages tbody").html(html);
				} else {
					$("#damages tbody").html('<tr><td colspan="4"><div class="goods-empty">暂无报损单</div></td></tr>');
				}
			}
		});
	}

	function getDamageStatusText(status) {
		var statusMap = {
			'pending': '待审核',
			'approved': '已审核',
			'rejected': '已驳回',
			'completed': '已完成'
		};
		return statusMap[status] || status;
	}

	function searchDamages() {
		loadDamages($("input[name='damage_search']").val());
	}

	// 显示报损单详情
	function showDamageDetail(damageNo) {
		$.ajax({
			url: '/api/cart/ddetail',
			type: 'GET',
			dataType: 'json',
			data: { damage_no: damageNo },
			success: function(res) {
				if (res.code === 0) {
					var damage = res.data;
					$("#detail-damage-no").text(damage.damage_no);
					$("#detail-damage-time").text(damage.create_time);
					$("#detail-damage-status").text(getDamageStatusText(damage.status));
					$("#detail-damage-unit").text(damage.unit === '1' ? '瓶' : '粒');
					$("#detail-damage-reason").text(getDamageReasonText(damage.reason));
					$("#detail-damage-remark").text(damage.remark || '无');

					var goodsHtml = '';
					for (var i in damage.goods_list) {
						var goods = damage.goods_list[i];
						goodsHtml += "<tr>";
						goodsHtml += "<td>" + goods.sku_name + "</td>";
						goodsHtml += "<td>" + goods.sku_no + "</td>";
						goodsHtml += "<td>" + goods.quantity + "</td>";
						goodsHtml += "</tr>";
					}
					$("#detail-damage-goods").html(goodsHtml);

					layer.open({
						type: 1,
						title: '报损单详情',
						area: ['800px', '600px'],
						content: $('#damage-detail').html()
					});
				} else {
					layer.msg(res.message);
				}
			}
		});
	}

	function getDamageReasonText(reason) {
		var reasonMap = {
			'1': '粘连',
			'2': '少粒',
			'3': '漏液',
			'4': '漏粉',
			'5': '破裂',
			'6': '变型',
			'7': '空壳',
			'8': '氧化',
			'9': '压药',
			'10': '污染'
		};
		return reasonMap[reason] || reason;
	}

	// 提交报损单
	function submitDamage() {
		if (selectGoodsSkuId.length === 0) {
			layer.msg('请至少选择一个商品');
			return;
		}

		var unit = $('input[name="watermark_type"]:checked').val();
		var reason = $('input[name="watermark_t"]:checked').val();

		if (!unit) {
			layer.msg('请选择报损单位');
			return;
		}

		if (!reason) {
			layer.msg('请选择报损原因');
			return;
		}

		var goodsList = [];
		var hasEmptySkuNo = false;

		// 检查商品编码并收集商品数据
		$("#goods tbody tr[data-sk_id]").each(function() {
			var skuId = $(this).attr('data-sk_id');
			var skuName = $(this).find("td:eq(0)").text().trim();
			var skuNo = $(this).find("td:eq(1)").text().trim();
			var quantity = $(this).find(".goods-num").val();

			// 检查商品编码是否为空
			if (!skuNo || skuNo.trim() === '') {
				hasEmptySkuNo = true;
				$(this).find("td:eq(1)").css('background-color', '#fff2f0');
				layer.msg('商品"' + skuName + '"的编码为空，请完善后再提交');
			} else {
				$(this).find("td:eq(1)").css('background-color', '');
			}

			goodsList.push({
				sku_id: skuId,
				sku_name: skuName,
				sku_no: skuNo,
				quantity: quantity
			});
		});

		// 如果有商品编码为空，则不提交
		if (hasEmptySkuNo) {
			return;
		}

		layer.prompt({
			title: '请输入报损单备注（可选）',
			formType: 2
		}, function(remark, index) {
			layer.close(index);

			layer.confirm('确认提交报损单吗？', function() {
				$.ajax({
					url: '/api/cart/dcreate',
					type: 'POST',
					dataType: 'json',
					data: {
						goods_list: JSON.stringify(goodsList),
						unit: unit,
						reason: reason,
						remark: remark
					},
					success: function(res) {
						if (res.code === 0) {
							layer.msg('报损单提交成功');
							// 清空当前选择的商品
							selectGoodsSkuId = [];
							$("#goods tbody").html('<tr><td colspan="4"><div class="goods-empty">未添加商品</div></td></tr>');
							// 刷新报损单列表
							loadDamages();
							// 取消所有商品选择
							$(".choosee").prop('checked', false);
							// 重置表单
							$('input[name="watermark_type"]').prop('checked', false);
							$('input[name="watermark_t"]').prop('checked', false);
							form.render();
						} else {
							layer.msg(res.message);
						}
					}
				});
			});
		});
	}
	// 加载按销售量排序的商品列表
	function loadSalesProducts(saleb,goodsb) {
		$.ajax({
			url: '/api/cart/salesLists.html',
			type: 'GET',
			dataType: 'json',
			data: {
				goodsb: goodsb,
				saleb:saleb
			},
			success: function(res) {
				$("#goodssale tbody").html('');
				var html = '';
				var earlymoning  = res.salelist;
				for (var i in earlymoning) {
					var item = earlymoning[i];
					html += "<tr data-sku_id='" + item.sku_id + "' data-sku_no='"+ item.sku_no +"' data-sku_name='"+item.sku_name+ "'>";
					html += "<td><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' lay-skin='primary' name='goods_service_ids' id='sale_"+item.sku_id+"' title='"+item.sku_name+"' />" + item.sku_name + "</td>";
					html += "<td>" + item.sku_no + "</td>";
					html += "<td class='price-one'>" + item.sales  + "</td>";
					html += "</tr>";
				}
				$("#goodssale tbody").html(html);

				earlymoning  = res.salelist;
				for (var i in earlymoning) {
					var item = earlymoning[i];
					if (selectGoodsSkuId.includes(String(item.sku_id))){
						$('#sale_'+String(item.sku_id)).prop('checked', true);
					}
				}
			}
		});
	}

	function saleb(){
		loadSalesProducts($("input[name='saleb']").val(),'');
	}


	function editSort(checkbox){
		var td = checkbox.parentElement;
		// 获取 <td> 的父元素 <tr>
		var tr = td.parentElement;
		// 获取 <tr> 的 data-sku_id 属性值
		var skuId = tr.getAttribute('data-sku_id');
		var skuNo = tr.getAttribute('data-sku_no');
		var skuName = tr.getAttribute('data-sku_name');
		if (checkbox.checked) {
			if (selectGoodsSkuId.includes(skuId)){

			}else{
				var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
				html += "<tr data-sk_id='" + skuId + "'>";
				html += "<td><input lay-ignore onchange='editS(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' lay-skin='primary' name='goods_service_ids' checked id='select_"+skuId+"' title='"+skuName+"'/>" + skuName + "</td>";
				html += "<td>" + skuNo + "</td>";
				html += "<td><input type='number' min='1' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'></td>";
				html += "</tr>";
				selectGoodsSkuId.push(skuId);
				$("#goods tbody").html(html);
			}
		}else{
			if (selectGoodsSkuId.includes(skuId)){
				var index = selectGoodsSkuId.indexOf(skuId);
				selectGoodsSkuId.splice(index, 1);
				var tr = document.querySelector('tr[data-sk_id="' + skuId + '"]');
				if (tr) {
					tr.parentNode.removeChild(tr);
				}
			}else{

			}
		}
	}
	function editS(checkbox){
		var td = checkbox.parentElement;
		// 获取 <td> 的父元素 <tr>
		var tr = td.parentElement;
		// 获取 <tr> 的 data-sku_id 属性值
		var skuId = tr.getAttribute('data-sk_id');
		if (checkbox.checked) {

		}else{
			if (selectGoodsSkuId.includes(skuId)){
				var index = selectGoodsSkuId.indexOf(skuId);
				selectGoodsSkuId.splice(index, 1);
				var tr = document.querySelector('tr[data-sk_id="' + skuId + '"]');
				if (tr) {
					tr.parentNode.removeChild(tr);
				}
				$('#goods_'+skuId).prop('checked', false);
				$('#sale_'+skuId).prop('checked', false);
			}else{

			}
		}
	}

	function addGoods() {
		goodsSelect(function (res) {
			if (!res.length) return false;
			var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
			for (var i = 0; i < res.length; i++) {
				for (var k = 0; k < res[i].selected_sku_list.length; k++) {
					var item = res[i].selected_sku_list[k];
					html += "<tr data-sk_id='" + item.sku_id + "'>";
					html += "<td><input lay-ignore onchange='editS(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' lay-skin='primary' name='goods_service_ids' checked id='select_"+item.sku_id+"' title='"+item.sku_name+"'/>" + item.sku_name + "</td>";
					html += "<td>" + item.sku_no + "</td>";
					html += "<td><input type='number' min='1' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'></td>";
					html += "</tr>";

					selectGoodsSkuId.push(String(item.sku_id));
					$('#goods_'+String(item.sku_id)).prop('checked', true);
					$('#sale_'+String(item.sku_id)).prop('checked', true);
				}
			}
			console.log(selectGoodsSkuId);
			$("#goods tbody").html(html);
		}, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 0});
	}


	// 初始化页面
	$(document).ready(function() {
		loadSalesProducts('','');
		loadDamages();

		// 绑定提交按钮
		$('#order').click(function() {
			submitDamage();
		});
		$('#scannerInput').on('input', function() {
			var scannedValue = $(this).val(); // 获取原始扫码值
			scannedValue = scannedValue.replace(/[\r\n\t]/g, '').trim(); // 移除隐藏字符
			scannedValue = String(scannedValue); // 确保是字符串格式
			if (scannedValue) {
				$.ajax({
					url: '/api/cart/goodsLists.html',
					type: 'GET',
					dataType: 'json',
					data: {
						goodsb: scannedValue,
						saleb:scannedValue
					},
					success: function(res) {
						var html = $("#goods tbody").html();
						var earlymoning  = res.salelist;
						for (var i in earlymoning) {
							var item = earlymoning[i];
							html += "<tr data-sk_id='" + item.sku_id + "'>";
							html += "<td><input lay-ignore onchange='editS(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' lay-skin='primary' name='goods_service_ids' checked id='select_"+item.sku_id+"' title='"+item.sku_name+"'/>" + item.sku_name + "</td>";
							html += "<td>" + item.sku_no + "</td>";
							html += "<td><input type='number' min='1' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'></td>";
							html += "</tr>";
							selectGoodsSkuId.push(String(item.sku_id));
							$('#goods_'+String(item.sku_id)).prop('checked', true);
							$('#sale_'+String(item.sku_id)).prop('checked', true);
						}
						$("#goods tbody").html(html);

						earlymoning  = res.salelist;
						for (var i in earlymoning) {
							var item = earlymoning[i];
							if (selectGoodsSkuId.includes(String(item.sku_id))){
								$('#sale_'+String(item.sku_id)).prop('checked', true);
							}
						}
					}
				});

				// 清空输入框以便下一次扫码
				$('#scannerInput').val('');
			}
		});
	});
</script>

{/block}
{block name="script"}

{/block}