{extend name="base"/}
{block name="resources"}
<style>
	.layui-input-inline.js-pid a{
		margin-left: 20px;
	}
	.ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form">
	
	<div class="layui-form-item">
		<label class="layui-form-label">参考图片：</label>
		<div class="layui-input-inline">
			<div class="upload-img-block">
				<div class="upload-img-box" id="imgUploadGoods">
					<a src="/upload/1/common/images/20250412/20250412114652174447281239513.png" target="_blank"><img src="/upload/1/common/images/20250412/20250412114652174447281239513.png" alt=""></a>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">机构Logo：</label>
		<input type="hidden" name="default_headimg" {if $default_img} value="{$default_img}"{/if}>
		<div class="layui-input-inline">
			<div class="upload-img-block">
				<div class="upload-img-box" id="imgUploadMember">
					{if empty($default_img)}
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
					{else/}
					<img src="{:img($default_img)}" alt="">
					{/if}
				</div>
			</div>
		</div>
	</div>
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'upload'], function() {
		var form = layui.form,
			upload = layui.upload,
			repeat_flag = false;//防重复标识
		form.render();

        form.on('submit(save)', function(data) {
            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                type: "POST",
                url: ns.url("shop/config/defaultpicture"),
                data: data.field,
                dataType: 'JSON',
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.msg('设置成功');
                        location.reload();
                    }else{
						layer.msg('设置成功');
						location.reload();
                    }
                }
            });
        });

		//普通图片上传
		var uploadInst = upload.render({
			elem: '#imgUploadShop'
			, url: ns.url("shop/upload/image")
			, done: function (res) {
				if (res.code >= 0) {
					$("#imgUploadShop").html("<img src=" + ns.img(res.data.pic_path) + " >");
					$("input[name='default_shop_img']").val(res.data.pic_path);
				}
				return layer.msg(res.message);
			}
		});

		//普通图片上传
		var uploadInst = upload.render({
			elem: '#imgUploadGoods'
			, url: ns.url("shop/upload/image")
			, done: function (res) {
				if (res.code >= 0) {
					$("#imgUploadGoods").html("<img src=" + ns.img(res.data.pic_path) + " >");
					$("input[name='default_goods_img']").val(res.data.pic_path);
				}
				return layer.msg(res.message);
			}
		});

		//普通图片上传
		var uploadInst = upload.render({
			elem: '#imgUploadMember'
			, url: ns.url("shop/upload/image")
			, done: function (res) {
				if (res.code >= 0) {
					$("#imgUploadMember").html("<img src=" + ns.img(res.data.pic_path) + " >");
					$("input[name='default_headimg']").val(res.data.pic_path);
				}
				return layer.msg(res.message);
			}
		});

	});
</script>
{/block}