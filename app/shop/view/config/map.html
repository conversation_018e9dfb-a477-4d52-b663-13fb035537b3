{extend name="base"/}

{block name="resources"}
<style type="text/css">
	.inline-block{display: inline-block;}
</style>
{/block}

{block name="main"}
<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label">腾讯地图密钥：</label>
		<div class="layui-input-block">
			<input type="text" name="tencent_map_key" autocomplete="off" value="{$info.tencent_map_key ?? ''}" class="layui-input ns-len-long inline-block">
			<a href="https://lbs.qq.com/dev/console/key/manage" class="ns-text-color" target="_blank" style="margin-left:5px;">获取密钥</a>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>
</div>
{/block}

{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form;
			repeat_flag = false; //防重复标识
			form.render();

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/config/map"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(data){
				    layer.msg(data.message);
					repeat_flag = false;
				}
			});
		});
	});
</script>
{/block}