{extend name="base"/}
{block name="resources"}
<style>
	.layui-form-switch{margin-top: 0;}
	.layui-card-body{display: flex;justify-content: space-between;align-items: center;}
	.layui-card-body p{color: #666666;}
	.ns-btn-box{flex-shrink: 0;margin-left: 20px;}
	.ns-btn-box a{cursor: pointer;}
	.ns-card-common:first-child{margin-top: 0;}
	.ns-card-common{border: 1px solid #f1f1f1;}
</style>
{/block}
{block name="main"}

<div class="layui-form">
	<div class="layui-card ns-card-common">
		<div class="layui-card-header">
			<span class="ns-card-title">快递发货</span>
			<input type="checkbox" name="" value="1" lay-skin="switch" {if $express_config.is_use == 1} checked {/if} lay-filter="logistics" />
		</div>
		<div class="layui-card-body">
			<p>启用快递发货后，买家下单可以选择快递发货，由您安排快递送货上门。</p>
			<div class="ns-btn-box">
				<a class="default ns-text-color" href="{:addon_url('shop/express/template')}">运费模板</a>
			</div>
		</div>
	</div>

	<div class="layui-card ns-card-common">
		<div class="layui-card-header">
			<span class="ns-card-title">门店自提</span>
			<input type="checkbox" name="" value="1" lay-skin="switch" {if $store_config.is_use == 1} checked {/if} lay-filter="SelfMention" />
		</div>
		<div class="layui-card-body">
			<p>启用上门自提后，买家可以就近选择商品自提门店，买家下单后，您需要确保买家指定的自提门店商品库存充足。</p>
			<div class="ns-btn-box">
				<a class="default ns-text-color" href="{:addon_url('shop/store/lists')}">查看自提门店</a>
			</div>
		</div>
	</div>

	<div class="layui-card ns-card-common">
		<div class="layui-card-header">
			<span class="ns-card-title">外卖配送</span>
			<input type="checkbox" name="" value="1" lay-skin="switch" {if $local_delivery_config.is_use == 1} checked {/if}  lay-filter="cityDistribution"/>
		</div>
		<div class="layui-card-body">
			<p>启用外卖配送后，在配送范围内的买家可以选择外卖配送，您可以接入第三方配送，也可以自己配送。</p>
			<div class="ns-btn-box" >
				<a class="default ns-text-color" href="{:addon_url('shop/local/local')}">编辑外卖配送</a>
			</div>
		</div>
	</div>
</div>
{/block}
{block name="script"}
<script>
    layui.use('form', function() {
        var form = layui.form;
		form.render();

        //物流配置开关
        form.on('switch(logistics)', function(data){
            data.value = data.elem.checked ? data.value : 0;

            $.ajax({
                dataType: "JSON",
                type: "POST",
                data:{"is_use": data.value},
                url: ns.url("shop/delivery/modifyExpressStatus"),
                success: function(res){
                    layer.msg(res.message);
                }
            })
        });

        //门店自提开关
        form.on('switch(SelfMention)', function(data){
            data.value = data.elem.checked ? data.value : 0;

            $.ajax({
                dataType: "JSON",
                type: "POST",
                data:{"is_use": data.value},
                url: ns.url("shop/delivery/modifyStoreStatus"),
                success: function(res){
                    layer.msg(res.message);
                }
            })
        });

        //外卖配送开关
        form.on('switch(cityDistribution)', function(data){
            data.value = data.elem.checked ? data.value : 0;

            $.ajax({
                dataType: "JSON",
                type: "POST",
                data:{"is_use": data.value},
                url: ns.url("shop/delivery/modifyLocalStatus"),
                success: function(res){
                    layer.msg(res.message);
                }
            })
        });
    });
</script>
{/block}