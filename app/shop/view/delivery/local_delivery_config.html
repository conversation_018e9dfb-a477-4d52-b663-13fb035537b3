{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-form">
	<div class="layui-form-item">
		<label class="layui-form-label">是否开启外卖配送</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" value="1" lay-skin="switch" {if condition="$config.is_use == 1"} checked {/if} />
		</div>
	</div>
	
	<!-- 表单操作 -->
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();
		
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;
		
			$.ajax({
				url: ns.url("shop/express/localDeliveryConfig"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
				}
			});
		});
	});
</script>
{/block}