{extend name="base"/}
{block name="resources"}
<style>
    *{margin: 0;padding: 0;}
    li{list-style: none;}
    .template-list{
        display: flex;
        flex-wrap: wrap;
        padding: 20px;
    }
    .template-list .template-item{
        overflow: hidden;
        position: relative;
        padding: 12px;
        margin-left: 32px;
        margin-bottom: 32px;
        width: 270px;
        height: 440px;
        border: 1px solid #e9e9e9;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .template-list .template-item .item-img{
        overflow: hidden;
        width: 244px;
        height: 355px;
    }
    .template-list .template-item .item-img img{
        max-width: 100%;
    }
    .template-list .template-item .item-hide{
        display: none;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0,0,0,.6);
        text-align: center;
    }
    .template-list .template-item .item-name{
        display: block;
        padding-top: 7px;
        line-height: 22px;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .template-list .item-hide .item-btn-box{
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        transform: translate(-50%, -50%);
    }
    .template-list .item-hide button{
        border: 1px solid #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 33px;
        width: 100px;
        color: #fff;
        background: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .template-list .template-item:hover .item-hide{
        display: block;
    }

    .template-box{
        position: relative;
        height: 100%;
    }
    .template-box .template-content{
        display: flex;
        justify-content: space-between;
        width: 530px;
        margin: auto;
    }
    .template-box .template-right{
        position: relative;
        height: 500px;
        width: 180px;
    }
    .template-box .template-right .right-list li {
        width: 82px;
        padding: 8px 0px;
        text-align: center;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .template-box .template-right .right-list li.active{
        color: #fff;
    }
    .template-box .template-right .right-code{
        position: absolute;
        bottom: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    .template-box .template-right .right-code .img{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 150px;
        height: 150px;
    }
    .template-box .template-right .right-code .img img{
        max-width: 100%;
        max-height: 100%;
    }
    .template-list .item-hide button ~ button{
        margin-top: 15px;
    }
    .template-box .template-right .right-code span{
        display: block;
        margin-top: 20px;
        line-height: 12px;
        font-size: 12px;
        text-align: center;
        color: #666;
        margin-bottom: 20px;
    }
    .template-box .template-img{
        padding: 37px 13px 17px;
        width: 250px;
        height: 500px;
        background: url('SHOP_IMG/iphone_shell.png') no-repeat center / cover;
        box-sizing: border-box;
    }
    .template-box .template-img .template-img-box{
        width: 100%;
        height: 100%;
        border-bottom-left-radius: 25px;
        border-bottom-right-radius: 25px;
        overflow: hidden;
    }
    .template-box .template-img img{
        max-width: 100%;
    }
    .template-box .template-operation{
        position: absolute;
        bottom: -12px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        border-top: 1px solid #e9e9e9;
        right: 0;
        left: 0;
        cursor: pointer;
    }
    .template-box .right-desc{
        position: absolute;
        bottom: 0;
    }
    .template-box .right-desc .desc-content{
        display: block;
        margin-top: 5px;
        text-indent: 2em;
        font-size: 12px;
        line-height: 1.5;
        color: #999;
    }

</style>
{/block}
{block name="main"}
<ul class="template-list"></ul>
{/block}
{block name="script"}
<script type="text/html" id="templateShow">
    <div class="template-box">
        <div class="template-content">
            <div class="template-img">
                <div class="template-img-box">
                    <img src={{ns.img(d.image)}} alt="">
                </div>
            </div>
            <div class="template-right">
                <ul class="right-list">
                    <li class="ns-bg-color active">首页</li>
                </ul>
                <div class="right-desc">
                    <span class="desc-name">模板描述:</span>
                    <span class="desc-content">{{d.desc}}</span>
                </div>
            </div>
        </div>
        <div class="template-operation ns-text-color">立即使用</div>
    </div>
</script>
<script>
    initData();
    function initData(){
        var html = '';
        $.ajax({
            type: 'post',
            url: ns.url("shop/diy/template"),
            dataType: 'JSON',
            success: function(res) {
                var data =  res.data.list;
                if (res.code >= 0){
                    for (var i = 0; i< data.length; i++){
                      html += `<li class="template-item">`;
                        html += `<div class="item-img">`;
                            html += `<img src="${ns.img(data[i].image)}" alt="">`;
                        html += `</div>`;
                        html += `<span class="item-name">${data[i].title}</span>`;
                        html += `<div class="item-hide">`;
                            html += `<div class="item-btn-box">`;
                                html += `<button class="immediate-use">立即使用</button>`;
                                html += `<button class="preview">预览</button>`;
                            html += `</div>`;
                        html += `</div>`;
                        html += `<input type="hidden" name="temple_cotent" value='${JSON.stringify(data[i])}'>`;
                      html += `</li>`;
                    }

                    $(".template-list").html(html);
                }else
                    layer.msg(res.message);
            }
        });    
    }



    $("body").on('click','.template-item .preview',function () {
        var data = JSON.parse($(this).parents('.template-item').find('input[name="temple_cotent"]').val());
        data.value = JSON.parse(data.value);
        layui.use('laytpl', function(){
            var laytpl = layui.laytpl;
            laytpl($("#templateShow").html()).render(data,function (html) {
               layer.open({
                   type:1,
                   title:name,
                   area:['800px','653px'],
                   content:html,
               })
            })
        });

        $(".template-operation").click(function () {
            immediateUse(data);
        })

    });



    $("body").on('click','.template-item .immediate-use',function () {
        var data = JSON.parse($(this).parents('.template-item').find('input[name="temple_cotent"]').val());
        data.value = JSON.parse(data.value);

        immediateUse(data);
    });

    //立即使用
    function immediateUse(data) {
        $.ajax({
            type: 'post',
            url: ns.url("shop/diy/create"),
            data:{
                name: data.type,
                title: data.title,
                value: JSON.stringify(data.value),
                id: data.id
            },
            dataType: 'JSON',
            success: function(res) {
                if (res.code >= 0){
                    layer.msg(res.message)
                    location.href = ns.url("shop/diy/index");
                }
                else
                    layer.msg(res.message)
            }
        });
    }
</script>

{/block}