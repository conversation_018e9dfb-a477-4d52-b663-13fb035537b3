{extend name="base"/}
{block name="resources"}
<style>
	.contraction{display: inline-block;margin-right: 5px;}
	.contraction span{cursor: pointer;display: inline-block;width: 17px;height: 17px;text-align: center;line-height: 14px;/* border: 1px solid #e9e9e9;*/user-select: none;/* background: #fff;*/}
	.sku-list{display: none;border-bottom: 1px solid #e6e6e6;}
	.sku-list td:nth-of-type(2){display: flex;align-items: center;}
	.sku-list td{border-bottom: 0;padding: 5px 15px !important;}
	.select-goods .ns-screen .layui-colla-content{padding-left: 15px;}
	.select-goods{display: flex;border-bottom: 1px solid #f2f2f2;height: 615px;}
	.select-goods .select-goods-left{width: 165px;margin: 20px;padding-right: 10px;border-right: 1px solid #f2f2f2;box-sizing: border-box;overflow-y: auto;}
	/* 滚动条整体部分*/.select-goods .select-goods-left::-webkit-scrollbar{width:5px;background-color:#d0cdc7;}
	/* scroll轨道背景*/.select-goods .select-goods-left::-webkit-scrollbar-track{-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);border-radius: 50%;background-color:#d0cdc7;}
	/* 滚动条中能上下移动的小块*/.select-goods .select-goods-left::-webkit-scrollbar-thumb{border-radius: 10px;-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);background-color:#a09d9d;}
	.select-goods .select-goods-left dl{height: auto;overflow: hidden;}
	.select-goods .select-goods-left dt,.select-goods .select-goods-left dd{position: relative;height: 32px;font-size: 12px;line-height: 32px;cursor: pointer;}
	.select-goods .select-goods-left dt{padding-left: 13px;}
	.select-goods .select-goods-left dd{padding-left: 20px;}
	.select-goods .select-goods-left dt:after{content: '';position: absolute;border: 4px solid transparent;border-right-color: #333;border-bottom-color: #333;left: 0;top: 50%;transform: translateY(-50%);transition: all .3s;}
	.select-goods .select-goods-left dl.fold{height: 32px;}
	.select-goods .select-goods-left .fold dt:after{transform:translateY(-50%) rotate(-45deg);}
	.select-goods .select-goods-left .active{background-color: #f7f7f7;}
	.select-goods .select-goods-left .ns-text-color{background-color: transparent;}
	.select-goods .select-goods-right{flex: 1;margin-top: 20px;margin-right: 20px;}
	.select-goods .select-goods-right .layui-table-body{height: 425px;}
	.select-goods .select-goods-right .ns-single-filter-box{padding: 0;}
	.select-goods .select-goods-right .ns-single-filter-box .layui-form{margin: inherit;}
	.select-goods .select-goods-right .ns-single-filter-box .layui-form div{margin: 0;}

	.select-goods .select-goods-classification{border: 0;}
	.select-goods .select-goods-classification .layui-colla-title{height: 32px;background-color: #fff;border: 0;line-height: 32px;font-size: 12px;padding-left: 15px; }
	.select-goods .select-goods-classification .layui-colla-title i{font-size: 0;}
	.select-goods .select-goods-classification .layui-colla-title.arrow.active:after{transform: rotate(0) translateY(-50%);}
	.select-goods .select-goods-classification .layui-colla-title.arrow:after{content: '';border: 4px solid #333;position: absolute;border-top-color: transparent !important;border-left-color: transparent !important;left: 3px;top: 50%;transition: all .3s;transform: rotate(-45deg) translateY(-50%);}
	.select-goods .select-goods-classification .layui-colla-content{padding: 0;font-size: 12px;border-top: 0;}
	.select-goods .select-goods-classification .layui-colla-item{border: 0;font-size: 12px;}
	.select-goods .select-goods-classification .classification-item{cursor: pointer;border: 0;font-size: 12px;}
	.select-goods .select-goods-classification .select-goods-classification .select-goods-classification .select-goods-classification .classification-item{padding-left: 48px;}
	.select-goods .select-goods-classification .select-goods-classification .select-goods-classification .classification-item{padding-left: 37px;}
	.select-goods .select-goods-classification .select-goods-classification .classification-item{padding-left: 26px;}
	.select-goods .select-goods-classification .select-goods-classification .select-goods-classification .classification-item.arrow:after{left: 21px;}
	.select-goods .select-goods-classification .select-goods-classification .classification-item.arrow:after{left: 15px;}
	.select-goods .select-goods-classification .select-goods-classification .layui-colla-content.classification-item{padding-left: 38px;}
	.select-goods .select-goods-classification .classification-item:hover{background-color: #f7f7f7;}
	/*.select-goods .select-goods-classification .classification-item{min-height: 32px;line-height: 32px;}*/
	/*.select-goods .select-goods-classification > .layui-colla-item > .layui-colla-content{padding-left: 13px;min-height: 32px;line-height: 32px;}*/
</style>
{/block}
{block name="body"}
<div class="select-goods">
	<div class="select-goods-left">

		{if $promotion}
		<dl class="marketing-campaign">
			<dt>商品</dt>
			{if $promotion &&  $promotion == "all"}
			<dd class="ns-text-color" data-type="">全部商品</dd>
			{/if}
			{foreach $promotion_type as $k => $v}
			{if $promotion && $promotion == $v.type || !$promotion}
			<dd class="{if $promotion && $promotion == $v.type}ns-text-color{/if}" data-type="{$v.type}">{$v.name}</dd>
			{/if}
			{/foreach}
		</dl>
		{/if}
		<!--{if !$promotion}-->
		<!--<dl class="commodity-group fold">-->
			<!--<dt>商品分组</dt>-->
			<!--<dd class="ns-text-color" data-group-id="">全部分组</dd>-->
			<!--{foreach $label_list as $k => $v}-->
			<!--<dd data-group-id="{$v.id}">{$v.label_name}</dd>-->
			<!--{/foreach}-->
		<!--</dl>-->
		<!--{/if}-->
		{if !$promotion}
		<div class="select-goods-classification layui-collapse" lay-accordion lay-filter="oneCategory">
			<div class="layui-colla-item">
				<h2 class="layui-colla-title classification-item ns-text-color" data-category_id="">全部分类</h2>
			</div>
			{foreach $category_list as $category_one_item}
			<div class="layui-colla-item">
				<h2 class="layui-colla-title classification-item {notempty name="$category_one_item.children"}arrow{/notempty}" data-category_id="{$category_one_item.category_id}">{$category_one_item.title}</h2>
				{notempty name="category_one_item.children"}
				{foreach $category_one_item.children as $category_two_item}
				<div class="layui-colla-content">
					<div class="select-goods-classification layui-collapse" lay-accordion lay-filter="twoCategory">
						<div class="layui-colla-item">
							<h2 class="layui-colla-title classification-item {notempty name="category_two_item.children"}arrow{/notempty}" data-category_id="{$category_two_item.category_id}">{$category_two_item.title}</h2>
							{notempty name="category_two_item.children"}
							{foreach $category_two_item.children as $category_three_item}
							<div class="layui-colla-content">
								<div class="select-goods-classification layui-collapse" lay-accordion lay-filter="threeCategory">
									<div class="layui-colla-item">
										<h2 class="layui-colla-title classification-item {notempty name="category_three_item.children"}arrow{/notempty}" data-category_id="{$category_three_item.category_id}">{$category_three_item.title}</h2>
										{notempty name="category_three_item.children"}
										{foreach $category_three_item.children as $category_four_item}
										<div class="layui-colla-content classification-item" data-category_id="{$category_four_item.category_id}">{$category_four_item.title}</div>
										{/foreach}
										{/notempty}
									</div>
								</div>
							</div>
							{/foreach}
							{/notempty}
						</div>
					</div>
				</div>
				{/foreach}
				{/notempty}
			</div>
			{/foreach}
		</div>
		{/if}
	</div>

	<div class="select-goods-right">
		<!-- 筛选面板 -->
		<div class="ns-single-filter-box">
			<div class="layui-form">
				<div class="layui-input-inline">
					<input type="text" name="goods_name" placeholder="请输入{if $promotion == 'pintuan'}活动名称{else}商品名称{/if}" autocomplete="off" class="layui-input">
					<button id="tijiao" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
						<i class="layui-icon">&#xe615;</i>
					</button>
				</div>
			</div>
		</div>
		<!-- 筛选面板 可以删除 -->
		<div class="ns-screen layui-collapse layui-hide">
			<form class="layui-colla-content layui-form layui-show">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label sm">
							{if $promotion == "pintuan"}
							活动名称：
							{else/}
							商品名称：
							{/if}
						</label>
						<div class="layui-input-inline">
							<input type="text" name="goods_name" placeholder="请输入{if $promotion == 'pintuan'}活动名称{else}商品名称{/if}" autocomplete="off" class="layui-input">
						</div>
					</div>

					{if $promotion != "pintuan" &&  $promotion != "groupbuy"}
					<div class="layui-inline">
						<label class="layui-form-label sm">商品分类：</label>
						<div class="layui-input-inline">
							{include file="goods/category_select" /}
						</div>
					</div>
						{if $promotion != "fenxiao"}
						<div class="layui-inline">
							<label class="layui-form-label sm">商品类型：</label>
							<div class="layui-input-inline">
								<select name="goods_class" lay-filter="goods_class">
									<option value="">全部</option>
									<option value="1">实物商品</option>
									<option value="2">虚拟商品</option>
								</select>
							</div>
						</div>
						{/if}
					{/if}
				</div>

				{if $promotion == ""}
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label sm">价格：</label>
						<div class="layui-input-inline">
							<input type="number" name="min_price" id="start_sale" lay-verify="int" placeholder="最低价格" class="layui-input" autocomplete="off">
						</div>
						<div class="layui-form-mid">-</div>
						<div class="layui-input-inline">
							<input type="number" name="max_price" id="end_sale" lay-verify="int" placeholder="最高价格" class="layui-input" autocomplete="off">
						</div>
					</div>
				</div>
				{/if}

				<div class="ns-form-row sm">
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
					<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
			</form>
		</div>

		<!-- 列表 -->
		<table id="goods_list" lay-filter="goods_list"></table>
	</div>
</div>
{/block}
{block name="script"}
<script type="text/html" id="checkbox">
	{{# if('{$promotion}'  == "pintuan"){ }}
	<div class="layui-hide">{{ d.goods_name = d.pintuan_name }}</div>
	{{# } }}
	{{# if('{$promotion}'  == "bargain"){ }}
	<div class="layui-hide">{{ d.goods_id = d.id }}</div>
	{{# } }}

	{{# if(mode == "spu" && $.inArray(d.goods_id.toString(), selected_id_arr) != -1){ }}
		{{# if(disabled == 1){ }}
			<input type="checkbox" data-goods-id="{{d.goods_id}}" name="goods_checkbox" lay-skin="primary" lay-filter="goods_checkbox" checked disabled>
		{{# }else{ }}
		<input type="checkbox" data-goods-id="{{d.goods_id}}" name="goods_checkbox" lay-skin="primary" lay-filter="goods_checkbox" checked>
		{{# } }}
	{{# }else{ }}
		<input type="checkbox" data-goods-id="{{d.goods_id}}" name="goods_checkbox" lay-skin="primary" lay-filter="goods_checkbox">
	{{# } }}
	<input type="hidden" data-goods-id="{{d.goods_id}}" name="goods_json" value='{{ JSON.stringify(d) }}' />
	<input type="hidden" data-goods-id="{{d.goods_id}}" name="goods_sku_list_json" value='{{ JSON.stringify(d.sku_list) }}' />
</script>

<!-- 商品预览 -->
<script type="text/html" id="goods_preview">
	<div class="goods-preview">
		{{ d.goods_name }}
	</div>
</script>

<!-- 商品信息 -->
<script type="text/html" id="goods_info">
	<div class="ns-table-title">
		{{# if('{$promotion}'  == "bargain" || '{$promotion}'  == "pintuan"){ }}
		<div class="layui-hide">{{ d.goods_name = d.sku_name }}{{ d.goods_image = d.sku_image }}</div>
		{{# } }}

		{{# if(mode == "sku"){ }}
		<div class="contraction" data-goods-id="{{d.goods_id}}" data-open="0">
			<span>+</span>
		</div>
		{{# } }}

		<div class="ns-title-pic" id="goods_img_{{d.goods_id}}">
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
		</div>

		<div class="ns-title-content">
			<a href="javascript:;" onclick="preview({{d.goods_id}})" class="ns-multi-line-hiding ns-text-color" title="成分：{{d.introduction}} -----功效：{{d.introd}}">{{d.goods_name}}</a>
			{{# if('{$promotion}' != 'pintuan' && '{$promotion}' != 'groupbuy' && '{$promotion}' != 'fenxiao'){ }}
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" >￥{{d.price}}</a>
			{{# } }}
		</div>
	</div>
</script>

<!-- 商品信息详情 -->
<script type="text/html" id="goods_detail">
	<div class="ns-table-title">

		<div class="ns-title-content">
			<a href="javascript:;" onclick="preview({{d.goods_id}})" class="ns-multi-line-hiding ns-text-color" title="查看详情">查看详情</a>
		</div>
	</div>
</script>

<!-- SKU商品列表 -->
<script type="text/html" id="skuList">
	{{# for(var i=0;i<d.list.length;i++){ }}
	<tr class="sku-list js-sku-list-{{d.list[i].goods_id}}" id="sku_img_{{d.list[i].goods_id}}">
		<td></td>
		<td>
			{{# if(d.checked || (mode == "sku" && $.inArray(d.list[i].sku_id.toString(), selected_id_arr) != -1) ){ }}
			{{# if(disabled == 1){ }}
			<input type="checkbox" data-goods-id="{{d.list[i].goods_id}}" data-sku-id="{{d.list[i].sku_id}}" name="goods_sku_checkbox" lay-skin="primary" lay-filter="goods_sku_checkbox" checked disabled>
			{{# }else{ }}
			<input type="checkbox" data-goods-id="{{d.list[i].goods_id}}" data-sku-id="{{d.list[i].sku_id}}" name="goods_sku_checkbox" lay-skin="primary" lay-filter="goods_sku_checkbox" checked>
			{{# } }}
			{{# }else{ }}
			<input type="checkbox" data-goods-id="{{d.list[i].goods_id}}" data-sku-id="{{d.list[i].sku_id}}" name="goods_sku_checkbox" lay-skin="primary" lay-filter="goods_sku_checkbox">
			{{# } }}
			<input type="hidden" data-sku-id="{{d.list[i].sku_id}}" value='{{ JSON.stringify(d.list[i]) }}' name="goods_sku_json" />
			<div class="ns-table-title">
				<div class="ns-title-pic" id="sku_img_{{d.sku_id}}">
					<img layer-src src="{{ns.img(d.list[i].sku_image)}}"/>
				</div>
				<div class="ns-title-content">
					<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" title="{{d.list[i].sku_name}}" lay-event="preview">{{d.list[i].sku_name}}</a>
					<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" >￥{{d.list[i].price}}</a>
				</div>
			</div>
		</td>
		<td>{{d.list[i].stock}}</td>
		<td>{{d.list[i].goods_class_name}}</td>
	</tr>
	{{# } }}
</script>

<script>
	var table, form,laytpl,element,
		select_id = "{$select_id}", //选中商品id
		selected_id_arr = select_id.length ? select_id.split(',') : [],
		// select_list = window.parent.select_goods_list ? window.parent.select_goods_list : [], //选中商品所有数据
		select_list = [], //选中商品所有数据
		mode = "{$mode}", //商品类型
		max_num = "{$max_num}", //最大商品数量
		min_num = "{$min_num}", //最小商品数量
		disabled = "{$disabled}", //不可选中
		promotion = "{$promotion}",// 营销标识
		goodsCols = [], //数据源
		goodsIdArr = [],
		skuIdAll = [],
		filterData = {promotion_type: '',label_id: '',goods_name:''}; //筛选数据

	if(mode == 'spu'){
		goodsIdArr = selected_id_arr;
	}else{
		skuIdAll = selected_id_arr;
	}

	if (!promotion || promotion == 'all'){
		goodsCols = [
			[
				{
					unresize: 'false',
					width: '8%',
					templet: '#checkbox'
				},
				{
					title: '商品',
					unresize: 'false',
					width: '52%',
					templet: '#goods_info'
				},
				{
					title: '详情',
					unresize: 'false',
					width: '10%',
					templet: '#goods_detail'
				},
				{
					field: 'goods_stock',
					title: '库存',
					unresize: 'false',
					width: '15%'
				},
				{
					field: 'goods_class_name',
					title: '商品类型',
					unresize: 'false',
					width: '15%'
				}
			]
		];
	}else if(promotion == "pintuan"){
		goodsCols = [
			[{
				unresize: 'false',
				width: '8%',
				templet: '#checkbox'
			},
			{
				field: 'pintuan_name',
				title: '拼团活动',
				unresize: 'false',
				width: '20%',
			},{
				title: '拼团商品',
				unresize: 'false',
				width: '33%',
				templet: '#goods_info'
			},{
				field: 'pintuan_num',
				title: '参团人数',
				unresize: 'false',
				width: '13%'
			},{
				field: 'group_num',
				title: '开团团队',
				unresize: 'false',
				width: '13%'
			},{
				field: 'order_num',
				title: '购买人数',
				unresize: 'false',
				width: '13%'
			}]
		];
	}else if(promotion == "groupbuy"){
		goodsCols = [
			[{
				unresize: 'false',
				width: '8%',
				templet: '#checkbox'
			},{
				title: '团购商品',
				unresize: 'false',
				width: '47%',
				templet: '#goods_info'
			}, {
				field: 'price',
				title: '商品原价',
				unresize: 'false',
				width: '15%',
				templet: function(data) {
					return '￥'+ data.price;
				}
			}, {
				field: 'groupbuy_price',
				title: '团购价格',
				unresize: 'false',
				width: '15%',
				templet: function(data) {
					return '￥'+ data.groupbuy_price;
				}
			}, {
				field: 'buy_num',
				title: '起购量',
				unresize: 'false',
				width: '15%'
			}]
		]
	}else if(promotion == "fenxiao"){
		goodsCols = [
			[
				{
					unresize: 'false',
					width: '8%',
					templet: '#checkbox'
				},
				{
					title: '商品名称',
					unresize: 'false',
					width: '47%',
					templet: '#goods_info'
				},
				{
					field: 'price',
					title: '价格',
					unresize: 'false',
					width: '15%'
				},
				{
					field: 'goods_stock',
					title: '库存',
					unresize: 'false',
					width: '15%'
				},
				{
					field: 'sale_num',
					title: '销量',
					unresize: 'false',
					width: '15%'
				},
			]
		]
	}else if(promotion == "bargain"){


		goodsCols = [
			[
				{
					unresize: 'false',
					width: '8%',
					templet: '#checkbox'
				},
				{
					title: '商品名称',
					unresize: 'false',
					width: '47%',
					templet: '#goods_info'
				},
				{
					field: 'price',
					title: '价格',
					unresize: 'false',
					width: '15%'
				},
				{
					field: 'stock',
					title: '库存',
					unresize: 'false',
					width: '15%'
				}
			]
		]
	}

	$(function () {
		layui.use(['form','laytpl'], function () {
			form = layui.form;
			laytpl = layui.laytpl;

			table = new Table({
				elem: '#goods_list',
				url: '{:addon_url("shop/goods/goodsselect")}',
				where: {is_virtual: "{$is_virtual}",promotion},
				cols: goodsCols,
				callback : function (res) {
					if(mode == "sku") {
						//存储这sku的具体id
						$("input[name='goods_checkbox'][data-goods-id]").each(function () {
							var goods_id = $(this).attr("data-goods-id");
							var tr = $(this).parent().parent().parent();
							var data = getGoodsSkuData(goods_id);
							laytpl(data.sku_list).render(data, function (html) {
								tr.after(html);
								form.render();
								layer.photos({
									photos: '.img-wrap',
									anim: 5
								});

							});
						});
					}
					// 更新商品复选框状态
					if(mode == 'spu') {
						for (var i=0;i<goodsIdArr.length;i++) {
							var selected_goods = $("input[name='goods_checkbox'][data-goods-id='" + goodsIdArr[i] + "']");
							if (selected_goods.length) {
								$("input[name='goods_checkbox'][data-goods-id='" + goodsIdArr[i] + "']").prop("checked", true);
								if (disabled == 1) {
									$("input[name='goods_checkbox'][data-goods-id='" + goodsIdArr[i] + "']").attr("disabled", "disabled");
								}
							}
						}
					}
					if(mode == 'sku'){
						for (var i=0;i<skuIdAll.length;i++) {
							var selected_goods_sku = $("input[name='goods_sku_checkbox'][data-sku-id='" + skuIdAll[i] + "']");
							$("input[name='goods_sku_checkbox'][data-sku-id='" + skuIdAll[i] + "']").prop("checked", true);
							if (selected_goods_sku.length) {
								$("input[name='goods_checkbox'][data-goods-id='" + selected_goods_sku.attr("data-goods-id") + "']").prop("checked", true);
								if (disabled == 1) {
									$("input[name='goods_checkbox'][data-goods-id='" + selected_goods_sku.attr("data-goods-id") + "']").attr("disabled", "disabled");
								}
							}
						}
					}
					form.render();
					// initData();
				}

			});


			/**
			 * 监听搜索
			 */
			form.on('submit(search)', function (data) {
				filterData.goods_name = data.field.goods_name;
				table.reload({
					page: {
						curr: 1
					},
					where: filterData
				});
				return false;
			});


			// 勾选商品
			form.on('checkbox(goods_checkbox)', function(data) {
				var goods_id = $(data.elem).attr("data-goods-id"),
					json = {};


				$("input[name='goods_sku_checkbox'][data-goods-id='" + goods_id + "']").prop("checked",data.elem.checked);
				form.render();

				var spuLen = $("input[name='goods_checkbox'][data-goods-id="+ goods_id +"]:checked").length;

				if (spuLen){
					json = JSON.parse($("input[name='goods_json'][data-goods-id="+ goods_id +"]").val());

					json.selected_sku_list = $("input[name='goods_sku_list_json'][data-goods-id="+ goods_id +"]").val() == "undefined" ? [] : JSON.parse($("input[name='goods_sku_list_json'][data-goods-id="+ goods_id +"]").val());
					delete json.LAY_INDEX;
					delete json.LAY_TABLE_INDEX;
					delete json.create_time;
					select_list.push(json);

					goodsIdArr.push(goods_id);
				}
				else{
					select_list.remove(goods_id);

					goodsIdArr.remove(goods_id);
				}

				var skuLen = $("input[name='goods_sku_checkbox'][data-goods-id="+ goods_id +"]").length;
				if(!skuLen){
					skuIdAll = [];

				}
				for (var i = 0; i < skuLen; i++){
					var skuId = $("input[name='goods_sku_checkbox'][data-goods-id="+ goods_id +"]").eq(i).attr("data-sku-id");
					if (spuLen)
						skuIdAll.push(skuId);
					else{
						skuIdAll.remove(skuId);
					}
				}
			});


			// 勾选商品SKU
			form.on('checkbox(goods_sku_checkbox)', function(data) {

				var goods_id = $(data.elem).attr("data-goods-id"),
						sku_id = $(data.elem).attr("data-sku-id"),
						json = {};

				if($("input[name='goods_sku_checkbox'][data-goods-id='" + goods_id + "']:checked").length) {

					if(!$("input[name='goods_checkbox'][data-goods-id='"+ goods_id +"']:checked").length){
						goodsIdArr.push(goods_id);

						json = JSON.parse($("input[name='goods_json'][data-goods-id="+ goods_id +"]").val());
						delete json.LAY_INDEX;
						delete json.LAY_TABLE_INDEX;
						delete json.create_time;
						json.selected_sku_list = [];
						select_list.push(json);
					}

					$("input[name='goods_checkbox'][data-goods-id='" + goods_id + "']").prop("checked", true);
				}else{
					$("input[name='goods_checkbox'][data-goods-id='" + goods_id + "']").prop("checked", false);
					goodsIdArr.remove(goods_id);
					select_list.remove(goods_id);
				}


				if (!select_list.length) {
					skuIdAll = [];
				}
				for (var i = 0; i < select_list.length; i++){
					if (select_list[i].goods_id == goods_id){
						// var skuLen = $("input[name='goods_sku_checkbox'][data-sku-id="+ sku_id +"]:checked").length;
						if (data.elem.checked){
							skuIdAll.push(sku_id);
							var skuVal = $("input[name='goods_sku_json'][data-sku-id="+ sku_id +"]").val()
							select_list[i].selected_sku_list.push(JSON.parse(skuVal));
						}else{
							skuIdAll.remove(sku_id);
							select_list[i].selected_sku_list.remove(sku_id);
						}
						break;
					}
				}
				form.render();
			});

			//初始化数据
			function initData(){
				var goodsLen = $("input[name='goods_checkbox'][data-goods-id]:checked").length;

				//父级
				for (var i = 0; i < goodsLen; i++){
					var goodsId = $("input[name='goods_checkbox'][data-goods-id]:checked").eq(i).attr("data-goods-id");
					var ident = false;
					for (var k = 0; k < select_list.length; k++){
						if(select_list[k].goods_id == goodsId){
							ident = true;
							break;
						}
					}

					if (ident) return;

					json = JSON.parse($("input[name='goods_json'][data-goods-id="+ goodsId +"]").val());
					delete json.LAY_INDEX;
					delete json.LAY_TABLE_INDEX;
					delete json.create_time;

					//子级
					var goodsSkuLen = $("input[name='goods_sku_checkbox'][data-goods-id="+ goodsId +"]:checked").length;
					json.selected_sku_list = [];
					for (var j = 0; j < goodsSkuLen; j++){
						var goodsSkuId = $("input[name='goods_sku_checkbox'][data-goods-id="+ goodsId +"]:checked").eq(j).attr("data-sku-id");
						var skuVal = $("input[name='goods_sku_json'][data-sku-id="+ goodsSkuId +"]").val();
						json.selected_sku_list.push(JSON.parse(skuVal));
					}
					select_list.push(json);
				}

				// if (!select_list.length) return false;
				// // select_list
				// // selected_id_arr
				// // for (var i)
				// if(mode == 'spu'){
				//
				// }else if(mode == 'sku'){
				//
				// }
			}

		});


		$("body").on("click", ".contraction", function () {
			var goods_id = $(this).attr("data-goods-id");
			var open = $(this).attr("data-open");
			if (open == 1) {
				$(this).children("span").text("+");
				$(".js-sku-list-" + goods_id).hide();
			} else {
				$(this).children("span").text("-");
				$(".js-sku-list-" + goods_id).show();
			}
			$(this).attr("data-open", (open == 0 ? 1 : 0));
		});

	});

	$(document).ready(function() {
		layui.use(['element'], function () {

			//加载完表格再处理折叠面板
			console.log('ddo');
			element = layui.element;
			//修改一级分类箭头切换
			element.on('collapse(oneCategory)', function(data){
				$(".layui-colla-title").removeClass("active");
				if (data.show){
					$(data.title).addClass("active");
				}
			});
			//修改二级分类箭头切换
			element.on('collapse(twoCategory)', function(data){
				$(".select-goods-classification .select-goods-classification .layui-colla-title").removeClass("active");
				if (data.show){
					$(data.title).addClass("active");
				}
			});
			//修改三级分类箭头切换
			element.on('collapse(threeCategory)', function(data){
				$(".select-goods-classification .select-goods-classification .select-goods-classification .layui-colla-title").removeClass("active");
				if (data.show){
					$(data.title).addClass("active");
				}
			});

			layui.element.render('collapse');
		});
	});
	var isOpenGoodsPreviewPopup = false;//防止重复弹出商品预览框
	function preview(goods_id){
		if (isOpenGoodsPreviewPopup) return;
		isOpenGoodsPreviewPopup = true;
		$.ajax({
			type: "POST",
			url: ns.url("shop/goods/goodsPreview"),
			data: {
				'goods_id': goods_id
			},
			dataType: 'JSON',
			success: function (res) {
				if (res.data.path.h5.status == 1) {
					res.data.goods_id = goods_id;

					laytpl($("#goods_preview").html()).render(res.data, function (html) {
						var layerIndex = layer.open({
							title: '商品预览',
							skin: 'layer-tips-class',
							type: 0,
							fix:false,
							shadeClose:true,
							area: ['700px', '500px'],
							content: html,
							success: function () {
								isOpenGoodsPreviewPopup = false;
							}
						});
					});
				} else {
					layer.msg(res.data.path.h5.message);
				}
			}
		});
	}

	function getGoodsSkuData(goods_id) {
		var list = JSON.parse($("input[name='goods_sku_list_json'][data-goods-id='" + goods_id + "']").val().toString());
		var sku_list = $("#skuList").html();
		var checked = $("input[name='goods_checkbox'][data-goods-id='" + goods_id + "']:checked").length ? true : false;
		var data = {
			checked : checked,
			sku_list : sku_list,
			list: list
		};
		return data;
	}


	function selectGoods(callback) {
		var res = select_list;
		var num = 0;
		if (mode == "spu") {
			// for (var i = 0; i < res.length; i++) {
			// 	num++;
			// }
			num = goodsIdArr.length;
			if (promotion){
				res = goodsIdArr;
			}
		} else if (mode == "sku") {
			// for (var i = 0; i < res.length; i++) {
			// 	for (var k = 0; k < res[i].selected_sku_list.length; k++) {
			// 		num++;
			// 	}
			// }
			num = skuIdAll.length;
			if (promotion){
				res = skuIdAll;
			}
		}

		if (max_num && max_num > 0 && num > max_num) {
			layer.msg("所选商品数量不能超过" + max_num + '件');
			return;
		}

		if (min_num && min_num > 0 && num < min_num) {
			layer.msg("所选商品数量最少不能少于" + min_num + '件');
			return;
		}
		callback(res);

	}

	Array.prototype.indexOf = function(val) {

		for (var i = 0; i < this.length; i++) {
			if (this[i].goods_id && !this[i].sku_id){
				if (this[i].goods_id == parseInt(val)) return i;
			}
			else if(this[i].sku_id){
				if (this[i].sku_id == parseInt(val)) return i;
			}
			else{
				if (this[i] == val) return i;
			}

		}
		return -1;
	};

	Array.prototype.remove = function(val) {
		var index = this.indexOf(val);
		if (index > -1) {
			this.splice(index, 1);
		}
	};

	select_list.__proto__ = Array.prototype;


	function removeDuplicates(arr){
		if (!Array.isArray(arr)) {
			console.log('type error!');
			return
		}
		var array = [];
		for (var i = 0; i < arr.length; i++) {
			if (array.indexOf(arr[i]) === -1) {
				array.push(arr[i])
			}
		}
		return array;
	};


	$(".select-goods .select-goods-left dd").hover(function () {
		$(this).addClass("active");
	},function () {
		$(this).removeClass("active");
	})

	$("body").on("click",".select-goods-left .marketing-campaign dd",function () {
		$(this).addClass("ns-text-color").siblings().removeClass("ns-text-color");
		filterData.promotion_type = $(this).attr("data-type");
		table.reload({
			page: {
				curr: 1
			},
			where: filterData
		});
	});

	$("body").on("click",".select-goods-left .commodity-group dd",function () {
		$(this).addClass("ns-text-color").siblings().removeClass("ns-text-color");
		filterData.label_id = $(this).attr("data-group-id");
		table.reload({
			page: {
				curr: 1
			},
			where: filterData
		});
	});



	$("body").on("click",".select-goods-left dl",function () {
		if ($(this).hasClass("fold")){
			$(this).removeClass("fold");
		}else{
			$(this).addClass("fold");
		}
	});

	$("body").on("click",".select-goods-left dd",function (event) {
		$(this).parents("dl").removeClass("fold");
		$(this).parents("dl").siblings().addClass("fold");
		event.stopPropagation();
	});

	//分类切换
	$("body").on("click",".classification-item",function (event) {

		var categoryId = $(this).attr("data-category_id");
		$(".classification-item").removeClass("ns-text-color ns-border-after-color");
		$(this).addClass("ns-text-color ns-border-after-color");
		table.reload({
			page: {
				curr: 1
			},
			where: {category_id: categoryId},
		});
		event.stopPropagation();
	});

	$("body").on('keydown', function (event) {

		if (event.keyCode == 13) {
			$("#tijiao").trigger("click");

			return false
		}

	});



</script>
{/block}