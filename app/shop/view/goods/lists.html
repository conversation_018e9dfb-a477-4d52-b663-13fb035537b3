{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" href="SHOP_CSS/goods_lists.css">
<style>
	/* 购物车按钮样式 */
	.cart-button {
		position: fixed;
		right: 30px;
		bottom: 30px;
		width: 50px;
		height: 50px;
		background-color: #FF5722;
		border-radius: 50%;
		color: white;
		text-align: center;
		line-height: 50px;
		cursor: pointer;
		box-shadow: 0 2px 5px rgba(0,0,0,0.2);
		z-index: 999;
	}

	.cart-button i {
		font-size: 24px;
		line-height: 50px;
	}

	.cart-count {
		position: absolute;
		top: -5px;
		right: -5px;
		background-color: #FFB800;
		color: white;
		border-radius: 50%;
		width: 20px;
		height: 20px;
		line-height: 20px;
		font-size: 12px;
	}

	/* 购物车面板样式 */
	.cart-panel {
		position: fixed;
		right: -400px;
		bottom: 100px;
		width: 380px;
		height: 500px;
		background-color: white;
		border-radius: 4px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.2);
		transition: right 0.3s;
		z-index: 998;
		display: flex;
		flex-direction: column;
	}

	.cart-panel.show {
		right: 30px;
	}

	.cart-header {
		padding: 15px;
		border-bottom: 1px solid #eee;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.cart-body {
		flex: 1;
		overflow-y: auto;
		padding: 10px;
	}

	.cart-footer {
		padding: 15px;
		border-top: 1px solid #eee;
		text-align: right;
	}

	/* 操作按钮样式调整 */
	.operation-wrap .ns-table-btn {
		display: flex;
		flex-direction: column;
	}

	.operation-wrap .ns-table-btn a {
		margin-bottom: 5px;
	}
</style>
{/block}
{block name="main"}
<!-- 按钮容器 -->
<div class="ns-single-filter-box">
</div>

<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title"></h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="search_text" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">商品分类：</label>
					<div class="layui-input-inline">
						{include file="goods/category_select" /}
					</div>
				</div>

			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">销量：</label>
					<div class="layui-input-inline">
						<input type="number" name="start_sale" id="start_sale" lay-verify="int" placeholder="最低销量" class="layui-input" autocomplete="off">
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="number" name="end_sale" id="end_sale" lay-verify="int" placeholder="最高销量" class="layui-input" autocomplete="off">
					</div>
				</div>
			</div>

			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品类型：</label>
					<div class="layui-input-inline">
						<select name="goods_class" lay-filter="goods_class">
							<option value="">全部</option>
							<option value="1">实物商品</option>
							<option value="2">虚拟商品</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">商品分组：</label>
					<div class="layui-input-inline">
						<select name="label_id" lay-filter="label_id">
							<option value="">全部</option>
							{foreach name="$label_list" item="vo"}
							<option value="{$vo['id']}">{$vo['label_name']}</option>
							{/foreach}
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">营销活动：</label>
					<div class="layui-input-inline">
						<select name="promotion_type" lay-filter="promotion_type">
							<option value="">全部</option>
							{foreach name="$promotion_type" item="vo"}
							<option value="{$vo['type']}">{$vo['name']}</option>
							{/foreach}
						</select>
					</div>
				</div>
			</div>

			<input type="hidden" name="goods_state" />
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				<button class="layui-btn layui-btn-primary" lay-submit lay-filter="batch_exportattr" >导出成分</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="goods_list_tab">
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="goods_list" lay-filter="goods_list"></table>
	</div>

</div>
<!-- 购物车按钮 -->
<div class="cart-button">
	<i class="layui-icon layui-icon-cart"></i>
	<span class="cart-count">0</span>
</div>

<!-- 购物车面板 -->
<div class="cart-panel">
	<div class="cart-header">
		<h3>购物车</h3>
		<i class="layui-icon layui-icon-close close-cart"></i>
	</div>
	<div class="cart-body">
		<table id="cart-list" lay-filter="cart-list"></table>
	</div>
	<div class="cart-footer">
		<button class="layui-btn layui-btn-primary remove-selected">删除选中</button>
		<button class="layui-btn ns-bg-color confirm-selected">确定</button>
	</div>
</div>
<!-- 商品信息 -->
<script type="text/html" id="goods_info">
	<div class="ns-table-title">
		<div class="contraction" data-goods-id="{{d.goods_id}}" data-open="0">
			<span>+</span>
		</div>
		<div class="ns-title-pic" id="goods_img_{{d.goods_id}}">
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
		</div>
		<div class="ns-title-content">
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" title="{{d.goods_name}}" lay-event="preview">{{d.goods_name}}</a>
			{{# if(d.promotion_addon){ }}
			<div class="promotion-addon">
				{{# for(var i=0;i<d.promotion_addon_list.length;i++){ }}
				<a href="{{ns.url( d.promotion_addon_list[i].url )}}"><span class="ns-bg-color" title="{{d.promotion_addon_list[i].name}}">{{ d.promotion_addon_list[i].short }}</span></a>
				{{# } }}
			</div>
			{{# } }}
		</div>
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-goods-id="{{d.goods_id}}">
		<div class="popup-qrcode-wrap"><img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif" /></div>
		<div class="ns-table-btn">


			{{# if(d.goods_state == 1){ }}
			<!--a class="layui-btn" lay-event="select">推广</a>-->
			<!-- <a class="layui-btn" lay-event="preview">预览</a> -->
			<a class="layui-btn" lay-event="add_to_cart">加入购物车</a>
			{{# }else{ }}
			{{# } }}
		</div>
	</div>
</script>

<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
	<button class="layui-btn layui-btn-primary" lay-event="add_to_cart">批量加入购物车</button>
</script>

<!-- SKU商品列表 -->
<script type="text/html" id="skuList">
	<tr class="js-sku-list-{{d.index}}" id="sku_img_{{d.index}}">
		<td></td>
		{{#  if (d.member_price_is_exit == 1) {  }}
		<td colspan="9">
			{{#  } else {  }}
		<td colspan="8">
			{{#  }  }}
			<ul class="sku-list">
				{{# for(var i=0;i<d.list.length;i++){ }}
				<li>
					<div class="img-wrap">
						<img layer-src src="{{ns.img(d.list[i].sku_image, 'small')}}">
					</div>
					<div class="info-wrap">
						<span class="sku-name">{{d.list[i].sku_name}}</span>
						<span class="price">价格：￥{{d.list[i].price}}</span>
						<span class="stock">库存：{{d.list[i].stock}}</span>
						<span class="sale_num">销量：{{d.list[i].sale_num}}</span>
					</div>
				</li>
				{{# } }}
			</ul>
		</td>
	</tr>
</script>

<!-- 商品推广 -->
<script type="text/html" id="goods_url">
	{{# if(d.path.h5.status == 1){ }}
	<img src="{{ ns.img(d.path.h5.img) }}" alt="推广二维码">
	<p class="qrcode-item-description">扫码后直接访问商品</p>
	<a class="ns-text-color" href="javascript:ns.copy('h5_url_{{ d.goods_id }}');">复制链接</a>
	<a class="ns-text-color" href="{{ ns.img(d.path.h5.img) }}" download>下载二维码</a>
	<input class="layui-input nc-len-mid" type="text" value="{{ d.path.h5.url }}" id="h5_url_{{ d.goods_id }}" readonly>
	{{# } }}
</script>

<!-- 商品预览 -->
<script type="text/html" id="goods_preview">
	<div class="goods-preview">
		{{ d.goods_name }}
	</div>
</script>

<script type="text/html" id="selectAddGoods">
	<div class="goods-type">
		<div class="item-type" onclick="location.href = ns.url('shop/goods/addGoods')">
			<div class="item-img"><img src="SHOP_IMG/goods_icon.png" alt=""></div>
			<div class="item-content">
				<p class="name">实物商品</p>
				<p class="description">（提供店铺线上服务商品的交易）</p>
			</div>
		</div>

		<div class="item-type" onclick="location.href = ns.url('shop/virtualgoods/addGoods')">
			<div class="item-img"><img src="SHOP_IMG/virtual_goods_icon.png" alt=""></div>
			<div class="item-content">
				<p class="name">虚拟商品</p>
				<p class="description">（虚拟商品支持核销管理）</p>
			</div>
		</div>
	</div>
</script>
{/block}

{block name="script"}
<!-- 编辑库存html -->
<script type="text/html" id="edit_stock">
	<div class="layui-form" id="edit_stock_block" lay-filter="form">
		<table class="layui-table" lay-skin="line">
			<colgroup>
				<col width="15%">
				<col width="15%">
				<col width="10%">
				<col width="10%">
				<col width="10%">
				<col width="10%">
				<col width="30%">
			</colgroup>
			<thead>
			<tr>
				<th>sku名称</th>
				<th>出入库</th>
				<th>当前库存</th>
				<th>每瓶/袋数量</th>
				<th>瓶数</th>
				<th>粒数</th>
				<th>备注</th>

			</tr>
			</thead>
			<tbody>
			{{#  layui.each(d, function(index, item){ }}
			<tr>
				<td><input type="hidden" name="sku_list[{{index}}][sku_id]" value="{{ item.sku_id }}" class="layui-input">{{ item.sku_name }}</td>
				<td><input type="radio" name="sku_list[{{index}}][stock_type]"  value="0" title="入库"  checked>
					<input type="radio" name="sku_list[{{index}}][stock_type]"  value="1" title="损耗">
					<input type="radio" name="sku_list[{{index}}][stock_type]"  value="2" title="调配">
				</td>
				<td><input type="hidden" name="sku_list[{{index}}][stock]" value="{{ item.stock }}" class="layui-input">{{ item.stock }}</td>
				<td><input type="number" min="0" onchange="change_stock({{index}})" name="sku_list[{{index}}][daizhuang]" value="{{ item.daizhuang }}" class="layui-input" lay-verify="int"></td>
				<td><input type="number" min="0" onchange="change_stock({{index}})" name="sku_list[{{index}}][num]" value="" class="layui-input" lay-verify="int"></td>
				<td><input type="number" name="sku_list[{{index}}][new_stock]" value="" class="layui-input" lay-verify="int"></td>
				<td><input type="text" name="sku_list[{{index}}][remark]" value="" class="layui-input" ></td>
			</tr>
			{{#  }); }}
			</tbody>
		</table>
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="edit_stock">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closeStock()">返回</button>
		</div>
	</div>
</script>


<!-- 批量操作 -->
<script type="text/html" id="batchSet">
	<div class="batch-set-wrap">
		<div class="tips">每次仅能设置一项，点击保存成功之后生效</div>
		<div class="set-wrap">
			<div class="tab-wrap">
				<ul>
					<li class="active" data-type="group">商品分组</li>
					<li data-type="service">商品服务</li>
					<li data-type="sale">虚拟销量</li>
					<li data-type="purchase_limit">商品限购</li>
					<li data-type="shipping">包邮设置</li>
				</ul>
			</div>
			<div class="content-wrap">

				<div class="tab-item tab-show group">
					<div class="layui-form">
						<div class="layui-form-item">
							<label class="layui-form-label">商品分组：</label>
							<div class="layui-input-block">
								<div class="layui-input-inline">
									<select name="batch_goods_label" lay-search="">
										<option value="0">请选择商品分组</option>
										{foreach name="$label_list" item="vo"}
										<option value="{$vo['id']}">{$vo['label_name']}</option>
										{/foreach}
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="tab-item service">
					<div class="layui-form">
						<div class="layui-form-item">
							<label class="layui-form-label">商品服务：</label>
							<div class="layui-input-block">
								<div class="layui-input-inline">
									{foreach name="$service_list" item="vo"}
									<input type="checkbox" name="batch_goods_service" value="{$vo.id}" title="{$vo.service_name}" lay-skin="primary">
									{/foreach}
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="tab-item sale">
					<div class="layui-form">
						<div class="layui-form-item">
							<label class="layui-form-label">已售出数：</label>
							<div class="layui-input-block">
								<input type="number" name="batch_virtual_sale" placeholder="0" value="0" lay-verify="virtual_sale" class="layui-input ns-len-short" autocomplete="off">
								<div class="layui-form-mid">/件</div>
							</div>
							<div class="ns-word-aux">该设置不计入商品统计数据</div>
						</div>
					</div>
				</div>

				<div class="tab-item purchase_limit">
					<div class="layui-form">
						<div class="layui-form-item">
							<label class="layui-form-label">商品限购：</label>
							<div class="layui-input-block">
								<input type="number" name="batch_max_buy" value="0" lay-verify="max_buy" class="layui-input ns-len-short" autocomplete="off">
								<div class="layui-form-mid">/件</div>
							</div>
							<div class="ns-word-aux">该限购为终身限购，0为不限购</div>
						</div>
					</div>
				</div>

				<div class="tab-item shipping">
					<div class="layui-form">
						<div class="layui-form-item">
							<label class="layui-form-label">是否包邮：</label>
							<div class="layui-input-block">
								<div class="layui-input-inline">
									<input type="radio" name="is_free_shipping" value="1" title="是" checked>
									<input type="radio" name="is_free_shipping" value="0" title="否">
								</div>
							</div>
						</div>
						<div class="layui-form-item hide shipping_template">
							<label class="layui-form-label">运费模板：</label>
							<div class="layui-input-block">
								<div class="layui-input-inline">
									<select name="batch_shipping_template" lay-search="">
										<option value="0">请选择运费模板</option>
										{foreach name="$express_template_list" item="vo"}
										<option value="{$vo['template_id']}">{$vo['template_name']}</option>
										{/foreach}
									</select>
								</div>
							</div>
						</div>
						<div class="ns-word-aux">该设置仅对实物商品有效</div>
					</div>
				</div>

				<div class="tab-item result">
					<img src="SHOP_IMG/success.png">
					<div class="text">设置成功</div>
				</div>
			</div>
		</div>
		<div class="footer-wrap">
			<button class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
			<button class="layui-btn ns-bg-color" onclick="batchSetting()">保存</button>
		</div>
	</div>
</script>

<script>
	var member_price_is_exit  ="{$memberprice_is_exit}";
</script>
<script>
	function change_stock(event){
		var daizhuang = Number($("input[name*='sku_list[" + event + "][daizhuang]']").val());
		var num = Number($("input[name*='sku_list[" + event + "][num]']").val());
		if (num){
			console.log(daizhuang*num);
			$("input[name*='sku_list[" + event + "][new_stock]']").val(daizhuang*num)
		}

	}
</script>
<script src="SHOP_JS/goods_edit_category.js"></script>
<script src="SHOP_JS/goods_list.js"></script>
{/block}