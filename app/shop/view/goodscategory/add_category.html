{extend name="base"/}
{block name="resources"}
<style>
	.layui-input-inline.js-pid a{
		margin-left: 20px;
	}
	.ns-form {
		margin-top: 0;
	}
</style>
{/block}
{block name="main"}
<form class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分类名称：</label>
		<div class="layui-input-block ns-len-long">
			<input name="category_name" type="text" placeholder="请输入分类名称" maxlength="30" lay-verify="required" class="layui-input" autocomplete="off">
		</div>
		<div class="ns-word-aux">
			<p>分类名称最长不超过30个字符</p>
		</div>
	</div>
	
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">简称：</label>
		<div class="layui-input-block ns-len-long">
			<input name="short_name" type="text" placeholder="请输入简称" maxlength="20" class="layui-input" autocomplete="off">
		</div>
		<div class="ns-word-aux">
			<p>分类名过长设置简称方便显示，字数设置为不超过20个字符</p>
		</div>
	</div>
	 -->
	<div class="layui-form-item">
		<label class="layui-form-label">上级分类：</label>
		<div class="layui-input-block js-pid">
			<span class="ns-input-text">顶级分类</span>
			<input type="hidden" name="pid" value="0">
			<input type="hidden" name="level" value="1">
			<input type="hidden" name="category_id_1" value="0">
			<input type="hidden" name="category_id_2" value="0">
			<input type="hidden" name="category_id_3" value="0">
			<a class="ns-text-color" href="javascript:selectedCategoryPopup();">选择分类</a>
		</div>
		<div class="ns-word-aux">
			<p>如果选择上级分类，那么新增的分类则为被选择上级分类的子分类，不选择上级分类默认为顶级分类</p>
		</div>
	</div>
	
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">商品属性：</label>
		<div class="layui-input-block ns-len-mid">
			<select name="attr_class_id" lay-filter="attr_class_id">
				<option value=""></option>
				{foreach name="$attr_class_list" item="vo"}
				<option value="{$vo.class_id}">{$vo.class_name}</option>
				{/foreach}
			</select>
		</div>

	</div> -->

	<div class="layui-form-item">
		<label class="layui-form-label">分类图标：</label>
		<div class="layui-input-block">
			<div class="upload-img-block">
				<div class="upload-img-box" id="imgUpload">
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
				</div>
			</div>
		</div>
		<div class="ns-word-aux">
			<p>建议图片尺寸：100px * 100px。图片格式：jpg、png、jpeg。</p>
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label">分类广告图：</label>
        <div class="layui-input-block">
            <div class="upload-img-block">
                <div class="upload-img-box" id="imgUploadAdv">
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
                </div>
            </div>
        </div>
	    <div class="ns-word-aux">
		    <p>建议图片尺寸：650px * 450px。图片格式：jpg、png、jpeg。</p>
	    </div>
    </div>
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">是否显示：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_show" lay-skin="switch" value="1" checked>
		</div>
		<div class="ns-word-aux">
			<p>用于控制前台是否展示</p>
		</div>
	</div> -->
	
	<div class="layui-form-item">
		<label class="layui-form-label">关键字：</label>
		<div class="layui-input-block">
			<input name="keywords" type="text" placeholder="请输入关键字" class="layui-input ns-len-long" autocomplete="off">
		</div>
		<div class="ns-word-aux">
			<p>用于网站搜索引擎的优化，关键字之间请用英文逗号分隔</p>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">分类描述：</label>
		<div class="layui-input-block ns-len-long">
			<textarea name="description" placeholder="请输入分类描述" class="layui-textarea"></textarea>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-block">
			<input name="sort" type="number" value="0" placeholder="请输入排序" lay-verify="num" class="layui-input ns-len-short" autocomplete="off">
		</div>
		<div class="ns-word-aux">
			<p>排序值必须为整数</p>
		</div>
	</div>
	
	<input type="hidden" name="image_adv" value="">
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</form>

<script type="text/html" id="selectedCategory">
	
	<form class="layui-form">
		
		<div class="layui-form-item">
			<label class="layui-form-label sm">一级分类</label>
			<div class="layui-input-block ns-len-mid">
				<select name="category_id_1" lay-filter="category_id_1">
					<option value="0" data-level="0">顶级分类</option>
					{{# for(var i=0;i<d.category_list_1.length;i++){ }}
						{{# if(d.category_id_1 ==d.category_list_1[i].category_id){ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}" selected>{{ d.category_list_1[i].category_name }}</option>
						{{# }else{ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}">{{ d.category_list_1[i].category_name }}</option>
						{{# } }}
					{{# } }}
				</select>
			</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label sm">二级分类</label>
			<div class="layui-input-block ns-len-mid">
				<select name="category_id_2" lay-filter="category_id_2"></select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label sm">三级分类</label>
			<div class="layui-input-block ns-len-mid">
				<select name="category_id_3" lay-filter="category_id_3"></select>
			</div>
		</div>
		
		<div class="ns-form-row sm">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="save_pid">保存</button>
		</div>
		
	</form>
</script>
{/block}
{block name="script"}
<script src="SHOP_JS/goods_edit_category.js"></script>
{/block}