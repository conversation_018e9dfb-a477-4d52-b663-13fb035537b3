{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<!--<div class="layui-collapse ns-tips">-->
	<!--<div class="layui-colla-item">-->
		<!--<h2 class="layui-colla-title">操作提示</h2>-->
		<!--<ul class="layui-colla-content layui-show">-->
			<!--<li>商品类型用在添加或者编辑商品选择类型然后配置对应的商品属性。</li>-->
			<!--<li>商品配置好对应的类型和属性，前台用户可以根据分类关联的类型在搜索分类商品之后根据属性进行进一步的搜索。</li>-->
			<!--<li>商家也可添加自己店铺的类型和属性，平台端的商品类型和属性才能参与前台搜索。</li>-->
		<!--</ul>-->
	<!--</div>-->
<!--</div>-->
<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="addService()">添加商品服务</button>
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入商品服务名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="attr_class_list" lay-filter="attr_class_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<script type="text/html" id="addService">

	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>商品服务：</label>
			<div class="layui-input-block">
				<input name="service_name" type="text" placeholder="请输入商品服务名称" lay-verify="required" class="layui-input ns-len-mid">
			</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label mid">描述：</label>
			<div class="layui-input-block">
				<textarea name="desc" class="layui-textarea ns-len-mid"></textarea>
			</div>
		</div>
		
		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="closeAttrLayer()">返回</button>
		</div>
	</div>
	
</script>

<script type="text/html" id="editService">

	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>商品服务：</label>
			<div class="layui-input-block">
				<input name="service_name" type="text" value="{{ d.service_name }}" placeholder="请输入商品服务名称" lay-verify="required" class="layui-input ns-len-mid">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid">描述：</label>
			<div class="layui-input-block">
				<textarea name="desc" class="layui-textarea ns-len-mid">{{ d.desc }}</textarea>
			</div>
		</div>
		<input type="hidden" name="id" value="{{ d.id }}">
		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="edit_save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="closeAttrLayer()">返回</button>
		</div>
	</div>

</script>

{/block}
{block name="script"}
<script>
	var laytpl, add_attr_index = -1,
		form, table;
	layui.use(['form', 'laytpl'], function() {
		var repeat_flag = false; //防重复标识
		laytpl = layui.laytpl;
		form = layui.form;
		form.render();

		table = new Table({
			elem: '#attr_class_list',
			url: ns.url("shop/goodsservice/lists"),
			cols: [
				[ {
					field: 'service_name',
					title: '服务名称',
					unresize: 'false',
					width: '20%'
				}, {
					field:'desc',
					unresize: 'false',
					title: '描述',
					width: '45%'
				}, {
                    unresize: 'false',
                    title: '创建时间',
					width: '20%',
					templet:function(data){
                        return ns.time_to_date(data.create_time);
					}
                }, {
					title: '操作',
					width: '15%',
					toolbar: '#operation',
					unresize: 'false'
				}]
			]
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit':
                    editService(data);
					break;
				case 'delete':
                    deleteService(data.id);
					break;
			}
		});

		/**
		 * 删除
		 */
		function deleteService(id) {
			layer.confirm('确认删除该服务吗？', function() {
				$.ajax({
					url: ns.url("shop/goodsservice/delete"),
					data: {
                        id:id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		form.on('submit(save)', function(data) {

			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				url: '{:addon_url("shop/goodsservice/add")}',
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(data) {
					layer.msg(data.message);
					if (data.code == 0) {
						table.reload();
						layer.close(add_attr_index);
					}
					repeat_flag = false;
				}
			});
			return false;
		});

        form.on('submit(edit_save)', function(data) {

            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: '{:addon_url("shop/goodsservice/edit")}',
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(data) {
                    layer.msg(data.message);
                    if (data.code == 0) {
                        table.reload();
                        layer.close(add_attr_index);
                    }
                    repeat_flag = false;
                }
            });
            return false;
        });

		/**
		 * 表单验证
		 */
		form.verify({
			num: function(value) {
				if (value == '') {
					return;
				}
				if (value % 1 != 0) {
					return '排序数值必须为整数';
				}
				if (value < 0) {
					return '排序数值必须为大于0';
				}
			}
		});
	});

	function addService() {
		var add_attr = $("#addService").html();
		laytpl(add_attr).render({}, function(html) {
			add_attr_index = layer.open({
				title: '添加商品服务',
				skin: 'layer-tips-class',
				type: 1,
				area: ['500px'],
				content: html
			});
		});

	}

    function editService(data) {
        var add_attr = $("#editService").html();
        laytpl(add_attr).render(data, function(html) {
            add_attr_index = layer.open({
                title: '编辑商品服务',
                skin: 'layer-tips-class',
                type: 1,
                area: ['500px'],
                content: html
            });
        });

    }
	
	function closeAttrLayer() {
		layer.close(add_attr_index);
	}
</script>
{/block}