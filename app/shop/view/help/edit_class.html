{extend name="base"/}
{block name="resources"}
<style>
	.ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form" >
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>类型名称：</label>
		<div class="layui-input-block">
			<input name="class_name" type="text" placeholder="请输入类型名称" value="{$class_info.data.class_name}" lay-verify="required" class="layui-input ns-len-mid">
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-inline">
			<input name="sort" type="number" placeholder="请输入排序值" value="{$class_info.data.sort}" lay-verify="num" class="layui-input ns-len-short">
		</div>
		<span class="layui-word-aux">排序值必须为整数</span>
	</div>
	
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
	
	<input type="hidden" name="class_id" value="{$class_info.data.class_id}" />
</div>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false;//防重复标识
		form.render();

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
	        if (repeat_flag) return;
	        repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/help/editClass"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res){
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(){
								location.href = ns.url("shop/help/classList")
							},
							btn2: function() {
								location.reload();
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		
		/**
		 * 表单验证
		 */
		form.verify({
			num: function(value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return "输入错误,请输入整数!";
				}
			}
		});
	});
	
	function back() {
		location.href = ns.url("shop/help/classList");
	}
</script>
{/block}