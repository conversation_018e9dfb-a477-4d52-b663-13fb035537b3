{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>感谢您提出系统的优化建议，我们会及时处理反馈。</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加优化建议</button>

	<div class="layui-form">
		<div class="layui-input-inline" hidden>
			<input type="text" name="search_text" placeholder="请输入帮助名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="help_list" lay-filter="help_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="edit">详情</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>
<script type="text/html" id="sort">
	<input name="sort" type="number" onchange="editSort({{d.id}},this)" value="{{d.sort}}" placeholder="请输入排序" class="layui-input edit-sort ns-len-short">
</script>

{/block}
{block name="script"}
<script>
	var table, repeat_flag = false;//防重复标识;
	layui.use('form', function() {
		var form = layui.form;
		form.render();

		table = new Table({
			elem: '#help_list',
			url: ns.url("shop/help/helpList"),
			cols: [
				[ {
					field: 'sort',
					title: '排序',
					width: '15%',
					unresize: 'false',
					templet: '#sort'
				}, {
					field: 'create_time',
					title: '时间',
					width: '20%',
					unresize: 'false',
					templet: function (data) {
						return ns.time_to_date(data.create_time);
					}
				}, {
					field: 'status',
					title: '状态',
					width: '15%',
					unresize: 'false',
					templet: function (data) {
						if (data.modify_time>data.create_time){
							return '已回复';
						}else{
							return '暂未处理';
						}
					}
				}, {
					title: '操作',
					width: '10%',
					toolbar: '#operation',
					unresize: 'false'
				}]
			],
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("shop/help/editHelp?id=" + data.id);
					break;
				case 'delete': //删除
					deleteHelp(data.id);
					break;
			}
		});
	});

	/**
	 * 删除
	 */
	function deleteHelp(id) {
		if (repeat_flag) return false;
		repeat_flag = true;

		layer.confirm('确定要删除该优化建议吗?', function() {
					$.ajax({
						url: ns.url("shop/help/deleteHelp"),
						data: {id},
						dataType: 'JSON',
						type: 'POST',
						success: function(res) {
							layer.msg(res.message);
							repeat_flag = false;

							if (res.code == 0) {
								table.reload();
							}
						}
					});
				},
				function() {
					repeat_flag = false;
					layer.close();
				});
	}

	// 监听单元格编辑
	function editSort(id, event) {
		var data = $(event).val();
		if (!new RegExp("^-?[1-9]\\d*$").test(data)) {
			layer.msg("排序号只能是整数");
			return;
		}
		if(data<0){
			layer.msg("排序号必须大于0");
			return ;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("shop/help/modifySort"),
			data: {
				sort: data,
				help_id: id
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if (res.code == 0) {
					location.reload();
				}
			}
		});
	}

	function add() {
		location.href = ns.url("shop/help/addHelp");
	}
</script>
{/block}