{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" href="SHOP_CSS/index.css">
{/block}
{block name="main"}

<div class="ns-survey">
	<div class="ns-survey-left">
		<div class="ns-survey-item">
			<!-- 商家信息 -->
			<!-- <div class="ns-survey-shop">
				<div class="ns-item-pic">
					<img layer-src src="{:img($shop_info['logo'])}" onerror=src="SHOP_IMG/default_shop.png" />
				</div>

				<div class="ns-surver-shop-detail">
					<p class="ns-survey-shop-name">{$shop_info.site_name}
					</p>
					<p class="ns-text-color-dark-gray"></p>
					<p class="ns-text-color-dark-gray">用户名：<span>{$user_info.username}</span></p>
					<p class="ns-text-color-dark-gray">登录ip：<span>{$user_info.login_ip}</span></p>
					<p>最后登录：<span class="ns-text-color-dark-gray">{php} echo date("Y-m-d H:i:s", $user_info['login_time']);{/php}</span></p>
				</div>
				
			</div> -->

			<!-- 概况 -->
			<div class="layui-card ns-survey-info ns-card-common">
				<div class="layui-card-header">
					<div>
						<span class="ns-card-title">实时概况</span>
						<span class="ns-card-sub">更新时间：{$today}</span>
					</div>
				</div>
				<div class="layui-card-body">
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">今日待发货订单数</p>
						<p class="ns-survey-detail-num">{$today_develity_count}</p>
						<p class="ns-survey-detail-yesterday">金额：{$today_develity_sum}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">今日订单数</p>
						<p class="ns-survey-detail-num">{$today_count}</p>
						<p class="ns-survey-detail-yesterday">金额：{$today_sum}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">该月订单总数</p>
						<p class="ns-survey-detail-num">{$month_count}</p>
						<p class="ns-survey-detail-yesterday">金额：{$month_sum}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">订单总数</p>
						<p class="ns-survey-detail-num">{$total_count}</p>
						<p class="ns-survey-detail-yesterday">金额：{$total_sum}</p>
					</div>

					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">商品收藏总数</p>
						<p class="ns-survey-detail-num">{$shop_stat_sum.collect_goods}</p>
						<p class="ns-survey-detail-yesterday">昨日：{if isset($stat_yesterday.collect_goods)}{$stat_yesterday.collect_goods}{else /} 0 {/if}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">商品总数</p>
						<p class="ns-survey-detail-num">{$shop_stat_sum.goods_count}</p>
						<p class="ns-survey-detail-yesterday">昨日：{if isset($stat_yesterday.goods_count)}{$stat_yesterday.goods_count}{else /} 0 {/if}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">会员总数</p>
						<p class="ns-survey-detail-num">{$member_count}</p>
						<p class="ns-survey-detail-yesterday">昨日：{if isset($stat_yesterday.member_count)}{$stat_yesterday.member_count}{else /} 0 {/if}</p>
					</div>
					<div class="ns-survey-detail-con">
						<p class="ns-survey-detail-aco">浏览量</p>
						<p class="ns-survey-detail-num">{$shop_stat_sum.visit_count}</p>
						<p class="ns-survey-detail-yesterday">昨日：{if isset($stat_yesterday.visit_count)}{$stat_yesterday.visit_count}{else /} 0 {/if}</p>
					</div>

				</div>
			</div>

		</div>



		<!-- 插件 -->
		<div class="layui-card ns-card-common">
			<div class="layui-card-header">
				<div>
					<span class="ns-card-title"><strong>营销活动</strong></span>
					<a class="ns-text-color" href="{:addon_url('shop/promotion/index')}">更多</a>
				</div>
			</div>

			<div class="layui-card-body">
				<div class="ns-item-block-parent">
					{php} $num = 0; {/php}
					{foreach $promotion as $list_k => $list_v}
					{empty name="$list_v['is_developing']"}
					{if in_array($list_v['show_type'],['shop','member']) && $num <5}
					{php} $num++;{/php}
					<a class="ns-item-block ns-item-block-hover-a" {empty name="$list_v['is_developing']"} href="{:addon_url($list_v['url'])}" {/empty}>
					<div class="ns-item-block-wrap">
						<div class="ns-item-pic">
							<img src="{:img($list_v.icon)}">
						</div>
						<div class="ns-item-con">
							<div class="ns-item-content-title">{$list_v.title}</div>
							<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
						</div>
					</div>
					</a>
					{/if}
					{/empty}
					{/foreach}
				</div>
			</div>
		</div>

		<div class="layui-card ns-card-common">
			<div class="layui-card-header">
				<div>
					<span class="ns-card-title"><strong>应用工具</strong></span>
					<a class="ns-text-color" href="{:addon_url('shop/promotion/tool')}">更多</a>
				</div>
			</div>

			<div class="layui-card-body">
				<div class="ns-item-block-parent">
					{php} $tool_num = 0; {/php}
					{foreach $promotion as $list_k => $list_v}
					{empty name="$list_v['is_developing']"}

					{if $list_v['show_type'] == 'tool' && $tool_num < 5}
					{php} $tool_num++; {/php}
					<a class="ns-item-block ns-item-block-hover-a" {empty name="$list_v['is_developing']"} href="{:addon_url($list_v['url'])}" {/empty}>
					<div class="ns-item-block-wrap">
						<div class="ns-item-pic">
							<img src="{:img($list_v.icon)}">
						</div>
						<div class="ns-item-con">
							<div class="ns-item-content-title">{$list_v.title}</div>
							<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
						</div>
					</div>
					</a>
					{/if}

					{/empty}
					{/foreach}
				</div>
			</div>
		</div>
	</div>
</div>
{/block}
{block name="script"}
<script>
	$(function () {
		$("body").on("click", ".ns-shop-state button", function () {
			location.href = ns.url("shop/index/renewExpireTime");
		});
	});
</script>
{/block}
{/block}