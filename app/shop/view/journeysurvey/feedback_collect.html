{extend name="base"/}

{block name="main"}
    <div class="feedback-page-container">
        <div class="layui-fluid">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                             <button class="layui-btn layui-btn-normal layui-btn-sm" id="createRecordBtn" style="margin-right: 15px;">创建新干预记录</button>
                             干预反馈列表 - <span id="member-name-placeholder">加载中...</span>
                             <button class="layui-btn layui-btn-primary layui-btn-sm" id="backToMemberBtn" style="float: right;">返回</button>
                         </div>
                        <div class="layui-card-body">
                            <table class="layui-hide" id="reportListTable" lay-filter="reportListTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}

{block name="script"}
    <link rel="stylesheet" href="/public/static/css/shop/journeysurvey/feedback_collect.css">
    <script>
        // IMPORTANT: 将后端控制器assign的变量嵌入到JS全局变量中
        // 这些变量在控制器 feedbackCollect() 或 reportList() 方法中通过 $this->assign 赋值
        var currentMemberId = {$member_id|default=0}; // 确保有默认值
        var currentMemberName = '{$member_name|default="加载中..."}'; // 确保有默认值
        var errorMsg = '{$error_msg|default=""}'; // 确保有默认值，以便JS处理
    </script>
    <script src="/public/static/js/shop/journeysurvey/feedback_collect.js"></script>
{/block}