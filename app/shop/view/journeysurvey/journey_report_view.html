{extend name="base"/}

{block name="main"}
    {if condition="$error_msg"}
        <div class="layui-card">
            <div class="layui-card-header">错误提示</div>
            <div class="layui-card-body" style="color: red;">
                {$error_msg}
            </div>
        </div>
    {/if}
    
    <div class="layui-fluid" id="report-details-container" style="display: none;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        报告详情
                        <span id="report-title" style="margin-left: 10px; font-weight: normal;"></span>
                    </div>
                    <div class="layui-card-body">
                        <p>分析时间: <span id="upload-time"></span></p>
                        <hr>
                        <div id="report-content" class="markdown-body">
                            {/* 报告内容将通过JS加载 */}
                        </div>
                        <hr>
                        <div id="file-list-section">
                            <h4>相关文件</h4>
                            <div id="file-list">
                                {/* 文件列表将通过JS加载 */}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}

{block name="script"}
    <link rel="stylesheet" href="/public/static/css/shop/journeysurvey/journey_report_view.css">
    <script src="/public/static/js/shop/journeysurvey/journey_report_view.js"></script>
    <!-- 引入Marked.js -->
    <script src="/public/static/js/marked.min.js"></script>
{/block}