{extend name="base"/}

{block name="main"}
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        会员报告列表 - <span id="member-name-placeholder">加载中...</span>
                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="backToMemberBtn" style="float: right;">返回</button>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="reportListTable" lay-filter="reportListTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/block}

{block name="script"}
    <script>
        // IMPORTANT: 将后端控制器assign的变量嵌入到JS全局变量中
        // 这些变量在控制器 reportList() 方法中通过 $this->assign 赋值
        var currentMemberId = {$member_id};
        var currentMemberName = '{$member_name|default="加载中..."}';
        var errorMsg = '{$error_msg|default=""}'; // 确保错误信息也被传递，以便JS处理
    </script>
    <script src="/public/static/js/shop/journeysurvey/report_list_for_member.js"></script>
    <script>
        // Layui.use table 的脚本块 (可选，如果您的其他Layui组件需要)
        // layui.use('table', function(){
        //     var table = layui.table;
        // });
    </script>
{/block}

{block name="resources"}
    <link rel="stylesheet" href="/public/static/css/shop/journeysurvey/report_list_for_member.css">
{/block}