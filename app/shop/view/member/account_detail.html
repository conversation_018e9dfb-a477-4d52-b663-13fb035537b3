{extend name="base"/}
{block name="resources"}
<style>
	.panel-content { padding-left: 15px; box-sizing: border-box; }
	.ns-custom-panel .custom-panel-title .panel-content { width: calc(100% - 190px); }
	.ns-account-value, .ns-split { line-height: 34px; }
	.ns-custom-panel .custom-panel-from { display: block; }
	.layui-input-block + .layui-word-aux {
	    display: block;
	    margin-left: 100px;
	}
	.ns-shop-account {
		display: flex;
		align-items: center;
		position: relative;
		padding: 15px;
		box-sizing: border-box;
	}
	
	.ns-shop-detail p {
		display: inline-block;
		width: 300px;
		line-height: 30px;
	}
</style>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
{/block}
{block name="main"}
<div class="ns-custom-panel">
	<div class="layui-form ns-form" lay-filter="storeform">
		<div class="layui-card ns-card-common js-goods-image-wrap">
			<div class="layui-card-header">
				<span class="ns-card-title">检测报告上传</span>
			</div>

			<div class="layui-card-body">
				<div class="layui-form-item goods-image-wrap">
					<label class="layui-form-label">图片上传：</label>
					<div class="layui-input-block">
						<!--商品主图项-->
						<div class="js-goods-image"></div>
						<button class="layui-btn layui-btn-primary layui-btn-sm js-add-goods-image" type="button">上传图片</button>
					</div>
					<div class="ns-word-aux">支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。</div>
				</div>
			</div>
		</div>

		<div class="layui-card ns-card-common">
			<div class="layui-card-header">
				<span class="ns-card-title">病理视频</span>
			</div>

			<div class="layui-card-body">
				<div class="layui-form-item">
					<label class="layui-form-label">视频上传：</label>
					<div class="layui-input-block">
						<div class="video-thumb">
							<video id="goods_video" class="video-js vjs-big-play-centered" controls="" poster="SHOP_IMG/goods_video_preview.png" preload="auto"></video>
							<span class="delete-video hide" onclick="deleteVideo()"></span>
						</div>
						<div id="videoUpload" title="视频上传" style="position: absolute;left: 0;width: 290px;height: 135px;opacity: 0;cursor: pointer;z-index:10;"></div>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">视频地址：</label>
					<div class="layui-input-block">
						<input type="text" name="video_url" placeholder="在此输入外链视频地址" autocomplete="off" class="layui-input ns-len-long">
					</div>
					<div class="file-title ns-word-aux">
						<div>注意事项：</div>
						<ul>
							<li>1、检查upload文件夹是否有读写权限。</li>
							<li>2、PHP默认上传限制为2MB，需要在php.ini配置文件中修改“post_max_size”和“upload_max_filesize”的大小。</li>
							<li>3、视频支持手动输入外链视频地址或者上传本地视频文件</li>
							<li>4、必须上传.mp4视频格式</li>
							<li>5、视频文件大小不能超过500MB</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
		</div>

		<!-- 隐藏域 -->
		<input type="hidden" name="member_id" value="{$member_info.member_id}" />
		<input type="hidden" name="goods_image" value="{$member_info.goods_image}" />
	</div>
	</div>
<!--商品主图列表-->
<script type="text/html" id="goodsImage">
	{{# if(d.length){ }}
	{{# for(var i=0;i<d.length;i++){ }}
	{{# if(d[i]){ }}
	<div class="item" data-index="{{i}}">
		<div class="img-wrap">
			<img src="{{ns.img(d[i])}}" layer-src>
		</div>
		<div class="operation">
			<i title="图片预览" class="iconfont iconreview js-preview"></i>
			<i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
		</div>
		{{# }else{ }}
		<div class="item empty">
			{{# } }}
		</div>
		{{# } }}
		{{# }else{ }}
		<div class="item empty"></div>
		{{# } }}
</script>
{/block}
{block name="script"}
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script>
		var goodsImage = [];//商品主图
		var laytpl= false;
		const GOODS_IMAGE_MAX = 10;//商品主图数量
		layui.use(['element','laytpl','form', 'laydate','upload'], function() {
			var form = layui.form,
					element = layui.element,
					laydate = layui.laydate,
					upload = layui.upload,
					repeat_flag = false; //防重复标识

			laytpl = layui.laytpl;
			form.render();

			// 加载商品主图
			goodsImage = $("input[name='goods_image']").val().split(",");
			//渲染商品主图列表
			refreshGoodsImage();


			laydate.render({
				elem: '#laydate'
			});

			//添加商品主图
			$(".js-add-goods-image").click(function () {
				openAlbum(function (data) {
					for (var i = 0; i < data.length; i++) {
						if (goodsImage.length < GOODS_IMAGE_MAX) goodsImage.push(data[i].pic_path);
					}
					refreshGoodsImage();
				}, GOODS_IMAGE_MAX);
			});

			//视频上传
			var videoUpload = upload.render({
				elem: '#videoUpload',
				url: ns.url("shop/upload/video"),
				accept: "video",
				done: function (res) {
					if (res.code >= 0) {
						$("input[name='video_url']").val(res.data.path);
						$(".delete-video").show();
						var video = "goods_video";
						var myPlayer = videojs(video);
						var path = ns.img(res.data.path);

						videojs(video).ready(function () {
							var myPlayer = this;
							myPlayer.src(path);
							myPlayer.load(path);
							myPlayer.play();
						});

					}
					layer.msg(res.message);
				}
			});

			/**
			 * 监听提交
			 */
			form.on('submit(save)', function(data) {
				data.field.goods_image = goodsImage.toString();//商品主图
				if(repeat_flag) return false;
				repeat_flag = true;

				$.ajax({
					url: ns.url("shop/member/accountDetail"),
					data: data.field,
					dataType: 'JSON', //服务器返回json格式数据
					type: 'POST', //HTTP请求类型
					success: function(res) {
						repeat_flag = false;
						if (res.code == 0) {
							layer.confirm('修改成功', {
								title:'操作提示',
								btn: ['返回列表', '留在该页'],
								yes: function(){
									location.href = ns.url("shop/member/memberList")
								},
								btn2: function () {
									location.reload();
								}
							});
						}else{
							layer.msg(res.message);
						}
					}
				});
			});

			//普通图片上传
			var uploadInst = upload.render({
				elem: '#headImg',
				url: ns.url("shop/upload/image"),
				done: function(res) {
					if (res.code >= 0) {
						$("input[name='headimg']").val(res.data.pic_path);
						$("#headImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
					}
					return layer.msg(res.message);
				}
			});

		});

		function back() {
			location.href = ns.url("shop/member/memberList");
		}

		//渲染商品主图列表
		function refreshGoodsImage() {
			var goods_image_template = $("#goodsImage").html();
			laytpl(goods_image_template).render(goodsImage, function (html) {
				$(".js-goods-image").html(html);
				//加载图片放大
				loadImgMagnify();

				if (goodsImage.length) {

					//预览
					$(".js-goods-image .js-preview").click(function () {
						$(this).parent().prev().find("img").click();
					});

					//图片删除
					$(".js-goods-image .js-delete").click(function () {
						var index = $(this).attr("data-index");
						goodsImage.splice(index, 1);
						refreshGoodsImage();
					});

					// 拖拽
					$('.js-goods-image .item').arrangeable({
						//拖拽结束后执行回调
						callback: function (e) {
							var indexBefore = $(e).attr("data-index");//拖拽前的原始位置
							var indexAfter = $(e).index();//拖拽后的位置
							var temp = goodsImage[indexBefore];
							goodsImage[indexBefore] = goodsImage[indexAfter];
							goodsImage[indexAfter] = temp;

							refreshGoodsImage();
						}
					});
				}

				//最多传十张图
				if (goodsImage.length < GOODS_IMAGE_MAX) {
					$(".js-add-goods-image").show();
				} else {
					$(".js-add-goods-image").hide();
				}

			});
		}
	</script>

<!-- 积分弹框html -->
<script type="text/html" id="point">
	<div class="layui-form integral-bounced">
		<div class="layui-form-item">
			<label class="layui-form-label">当前积分：</label>
			<div class="layui-input-block ns-account-value">{{ parseInt(d.point) }}</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">调整数额：</label>
			<div class="layui-input-block amount">
				<input type="number" value="0" placeholder="请输入调整数额" name="adjust_num" lay-verify="num" class="layui-input ns-len-short">
			</div>
			<span class="ns-word-aux">调整数额与当前积分数相加不能小于0</span>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">备注：</label>
			<div class="layui-input-block ns-len-long">
				<textarea class="layui-textarea" name="remark" placeholder="请输入备注"></textarea>
			</div>
		</div>
		
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="savePoint">确定</button>
		</div>
		
		<input type="hidden" name="member_id" value="{$member_info.member_id}" />
		<input type="hidden" name="point" value="{{ d.point }}" />
	</div>
</script>

<!-- 余额弹框html -->
<script type="text/html" id="balance">
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label">当前余额（不可提现）：</label>
			<div class="layui-input-block ns-account-value">{{ d.balance }}</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">调整数额：</label>
			<div class="layui-input-block">
				<input type="number" value="0" placeholder="请输入调整数额" name="adjust_num" lay-verify="num" class="layui-input ns-len-short">
			</div>
			<span class="ns-word-aux">调整数额与当前不可提现余额相加不能小于0</span>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">备注：</label>
			<div class="layui-input-block ns-len-long">
				<textarea class="layui-textarea" name="remark" placeholder="请输入备注"></textarea>
			</div>
		</div>
		
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="saveBalance">确定</button>
		</div>
		
		<input type="hidden" name="member_id" value="{$member_info.member_id}" />
		<input type="hidden" name="point" value="{{ d.balance }}" />
	</div>
</script>

<!-- 余额（可提现）弹框html -->
<script type="text/html" id="balance_money">
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label">当前余额（可提现）：</label>
			<div class="layui-input-block ns-account-value">{{ d.balance_money }}</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">调整数额：</label>
			<div class="layui-input-block">
				<input type="number" value="0" placeholder="请输入调整数额" name="adjust_num" lay-verify="num" class="layui-input ns-len-short">
			</div>
			<span class="ns-word-aux">调整数额与当前可提现余额相加不能小于0</span>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">备注：</label>
			<div class="layui-input-block ns-len-long">
				<textarea class="layui-textarea" name="remark" placeholder="请输入备注"></textarea>
			</div>
		</div>
		
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="saveBalanceMoney">确定</button>
		</div>
		
		<input type="hidden" name="member_id" value="{$member_info.member_id}" />
		<input type="hidden" name="point" value="{{ d.balance_money }}" />
	</div>
</script>

<!-- 成长值弹框html -->
<script type="text/html" id="growth">
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label">当前成长值：</label>
			<div class="layui-input-block ns-account-value">{{ d.growth }}</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">调整数额：</label>
			<div class="layui-input-block">
				<input type="number" value="0" placeholder="请输入调整数额" name="adjust_num" lay-verify="num" class="layui-input ns-len-short">
			</div>
			<span class="ns-word-aux">调整数额与当前成长值相加不能小于0</span>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label">备注：</label>
			<div class="layui-input-block ns-len-long">
				<textarea class="layui-textarea" name="remark" placeholder="请输入备注"></textarea>
			</div>
		</div>
		
		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="saveGrowth">确定</button>
		</div>
		
		<input type="hidden" name="member_id" value="{$member_info.member_id}" />
		<input type="hidden" name="point" value="{{ d.growth }}" />
	</div>
</script>
{/block}