{extend name="base"/}
{block name="resources"}
<style>
	.ns-form {margin-top: 0;}
	 #container{ width: 650px; height: 500px; }
	.empty-address{ display: none; }
	.address-content {display: inline-block;vertical-align: top;}
</style>
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/searchable_select/searchable_select.css" />
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="storeform" >
    <div class="ns-tab layui-tab layui-tab-brief" lay-filter="editselffetch">
        <ul class="layui-tab-title">
            <li class="layu1i-this" lay-id="basic">基础设置</li>
            <li lay-id="media">检测报告</li>
            <li lay-id="attr">病历管理</li>
        </ul>
        <div class="layui-tab-content">
			<!-- 基础设置 -->
			<div class="layui-tab-item layui-show">
				<div class="layui-card ns-card-common">
					<div class="layui-card-header">
						<span class="ns-card-title">基础信息</span>
					</div>

					<div class="layui-card-body">
				<div class="layui-form-item">
					<label class="layui-form-label"><span class="required">*</span>用户名：</label>
					<div class="layui-input-block">
						<input name="username" type="text" lay-verify="required" class="layui-input ns-len-long" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
					</div>
					<div class="ns-word-aux">用于最终产品说明单呈现的名字，请认真填写</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label"><span class="required">*</span>昵称：</label>
					<div class="layui-input-block">
						<input name="nickname" type="text" lay-verify="required" class="layui-input ns-len-long">
					</div>
					<div class="ns-word-aux">用于机构自己人熟悉客户，比如:客户是“小明”，昵称可以是"张亮的孩子”，用干内部人知晓客户是谁，不做外部的展示</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">手机号：</label>
					<div class="layui-input-block">
						<input name="mobile" type="text" lay-verify="mobile" class="layui-input ns-len-long">
					</div>
					<div class="ns-word-aux">已进行手机号验证，请填写正确的手机号</div>
				</div>

				<!--<div class="layui-form-item">
					<label class="layui-form-label">密码：</label>
					<div class="layui-input-block">
						<input type="password" name="password"  class="layui-input ns-len-long">
					</div>
				</div>-->

				<!--<div class="layui-form-item">
					<label class="layui-form-label"><span class="required">*</span>会员等级：</label>
					<div class="layui-input-inline ns-len-mid">
						<select class="member_level" name="member_level" lay-verify="required" lay-filter="member_level">
							<option value="">请选择</option>
							{volist name="member_level_list" id="member_level"}
							<option value="{$member_level.level_id}">{$member_level.level_name}</option>
							{/volist}
						</select>
					</div>
				</div>-->

				<div class="layui-form-item">
					<label class="layui-form-label">真实姓名：</label>
					<div class="layui-input-inline">
						<input name="realname" type="text" class="layui-input ns-len-long">
					</div>
				</div>

				{if $order_deverys}
				<div class="layui-form-item">
					<label class="layui-form-label">常用收货地址：</label>
						<div class="layui-input-block ns-len-mid">
							<select id="order_deverys" name="order_deverys" lay-search="" lay-filter="order_deverys">
								<option value="">请选择常用收货地址</option>
								{foreach name="$order_deverys" item="v"}
								<option value="{$v.name}-{$v.mobile}-{$v.address}">{$v.name}-{$v.mobile}-{$v.address}</option>
								{/foreach}
							</select>
						</div>
				</div>
				{/if}

				<div class="layui-form-item">
					<label class="layui-form-label">收货昵称：</label>
					<div class="layui-input-inline">
						<input name="addressname" type="text" class="layui-input ns-len-long">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">收货电话：</label>
					<div class="layui-input-inline">
						<input name="addressmobile" type="text" class="layui-input ns-len-long">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">收货地址：</label>
					<div class="layui-input-inline ns-len-mid area-select">
						<select name="province_id" lay-filter="province_id" lay-verify="province_id">
							<option value="">请选择省份</option>
							{foreach $province_list as $k => $v}
							<option value="{$v.id}">{$v.name}</option>
							{/foreach}
						</select>
					</div>

					<div class="layui-input-inline ns-len-mid area-select">
						<select name="city_id"  lay-filter="city_id" lay-verify="city_id">
							<option value="">请选择城市</option>
						</select>
					</div>

					<div class="layui-input-inline ns-len-mid area-select">
						<select name="district_id"  lay-filter="district_id" lay-verify="district_id">
							<option value="">请选择区/县</option>
						</select>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label"></label>
					<div class="layui-input-block">
						<input type="text" name="address"  placeholder="请填写收货具体地址" autocomplete="off" class="layui-input ns-len-long address-content" value="">
						<input type = "hidden" name="longitude" class="layui-input"/>
						<input type = "hidden" name="latitude" class="layui-input"/>
						<button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>
					</div>
					<div class="ns-word-aux">点击查找地址可在地图上显示输入的地理位置</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">地图定位：</label>
					<div class="layui-input-block ns-special-length">
						<div id="container" class="selffetch-map"></div>
					</div>
					<div class="ns-word-aux empty-address">请选择地理位置！在地图上点击得到的地理位置会自动填入到对应的输入框中</div>
				</div>

				<!--<div class="layui-form-item">
					<label class="layui-form-label">性别：</label>
					<div class="layui-input-inline">
						<input type="radio" name="sex" value="0" title="未知" checked="">
						<input type="radio" name="sex" value="1" title="男">
						<input type="radio" name="sex" value="2" title="女">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">生日：</label>
					<div class="layui-input-inline">
						<input name="birthday" type="text" id="laydate" class="layui-input ns-len-mid" autocomplete="off">
					</div>
				</div>-->

				<!--<div class="ns-form-row">
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
					<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
				</div>-->
			</div>
				</div>
			</div>

			<!-- 媒体设置 -->
			<div class="layui-tab-item">

				<div class="layui-card ns-card-common js-goods-image-wrap">
					<div class="layui-card-header">
						<span class="ns-card-title">检测报告上传</span>
					</div>

					<div class="layui-card-body">
						<div class="layui-form-item goods-image-wrap">
							<label class="layui-form-label">图片上传：</label>
							<div class="layui-input-block">
								<!--商品主图项-->
								<div class="js-goods-image"></div>
								<button class="layui-btn layui-btn-primary layui-btn-sm js-add-goods-image" type="button">上传图片</button>
							</div>
							<div class="ns-word-aux">支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。</div>
						</div>
					</div>
				</div>

				<div class="layui-card ns-card-common">
					<div class="layui-card-header">
						<span class="ns-card-title">病理视频</span>
					</div>

					<div class="layui-card-body">
						<div class="layui-form-item">
							<label class="layui-form-label">视频上传：</label>
							<div class="layui-input-block">
								<div class="video-thumb">
									<video id="goods_video" class="video-js vjs-big-play-centered" controls="" poster="SHOP_IMG/goods_video_preview.png" preload="auto"></video>
									<span class="delete-video hide" onclick="deleteVideo()"></span>
								</div>
								<div id="videoUpload" title="视频上传" style="position: absolute;left: 0;width: 290px;height: 135px;opacity: 0;cursor: pointer;z-index:10;"></div>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">视频地址：</label>
							<div class="layui-input-block">
								<input type="text" name="video_url" placeholder="在此输入外链视频地址" autocomplete="off" class="layui-input ns-len-long">
							</div>
							<div class="file-title ns-word-aux">
								<div>注意事项：</div>
								<ul>
									<li>1、检查upload文件夹是否有读写权限。</li>
									<li>2、PHP默认上传限制为2MB，需要在php.ini配置文件中修改“post_max_size”和“upload_max_filesize”的大小。</li>
									<li>3、视频支持手动输入外链视频地址或者上传本地视频文件</li>
									<li>4、必须上传.mp4视频格式</li>
									<li>5、视频文件大小不能超过500MB</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 属性设置 -->
			<div class="layui-tab-item">
				<div class="ns-form">
					<div class="layui-form-item">
						<label class="layui-form-label">温馨提示：</label>
						<div class="layui-input-inline ns-special-length" style="color: #ff8143;font-size: 12px">
							格式参考：2020/02/02:   xx不适
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">病历详情：</label>
						<div class="layui-input-inline ns-special-length">
							<script id="editor" type="text/plain" style="width:100%;height:500px;"></script>
						</div>
					</div>
				</div>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"> </script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>
			</div>
		</div>
	</div>
	<div class="fixed-btn">
		<button class="layui-btn layui-btn-primary ns-border-color ns-text-color js-prev" lay-submit="" lay-filter="prev">上一步</button>
		<button class="layui-btn ns-bg-color js-save" lay-submit="" lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary ns-border-color ns-text-color js-next" lay-submit="" lay-filter="next">下一步</button>
	</div>
</div>
<!--商品主图列表-->
<script type="text/html" id="goodsImage">
	{{# if(d.length){ }}
	{{# for(var i=0;i<d.length;i++){ }}
	{{# if(d[i]){ }}
	<div class="item" data-index="{{i}}">
		<div class="img-wrap">
			<img src="{{ns.img(d[i])}}" layer-src>
		</div>
		<div class="operation">
			<i title="图片预览" class="iconfont iconreview js-preview"></i>
			<i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
		</div>
		{{# }else{ }}
		<div class="item empty">
			{{# } }}
		</div>
		{{# } }}
		{{# }else{ }}
		<div class="item empty"></div>
		{{# } }}
</script>
{/block}
{block name="script"}
<script type="text/javascript" src="{$http_type}://webapi.amap.com/maps?v=1.4.6&amp;key=66d777244fce2401c784bdaa1da8decd"></script>
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script>
	var map_class;
	var stepTab  = false;
	var tab = ["basic", "media", "attr"];
	var goodsImage = [];//商品主图
	var laytpl= false;
	const GOODS_IMAGE_MAX = 10;//商品主图数量
	var goodsContent;//商品详情
	//获取hash来切换选项卡
	stepTab = location.hash.replace(/^#tab=/, '');
	goodsContent = UE.getEditor('editor');
	layui.use(['element','laytpl','form', 'laydate','upload'], function() {
		var form = layui.form,
			element = layui.element,
			laydate = layui.laydate,
			upload = layui.upload,
			repeat_flag = false; //防重复标识

		laytpl = layui.laytpl;
		map_class = new mapClass("container",{lat:'',lng:''});
		form.render();

		stepTab = 'basic';

		element.tabChange('editselffetch', stepTab);

		// 加载商品详情
		goodsContent.ready(function () {
			goodsContent.setContent('');
		});
		//渲染商品主图列表
		refreshGoodsImage();

		//监听Tab切换，以改变地址hash值
		element.on('tab(editselffetch)', function () {
			location.hash = 'tab=' + this.getAttribute('lay-id');
			stepTab = this.getAttribute('lay-id');
			refreshStepButton();
		});

		laydate.render({
			elem: '#laydate'
		});

		//上一步
		form.on('submit(prev)', function (data) {
			// var prev = tab[tab.indexOf(location.hash.replace(/^#tab=/, '')) - 1];
			var prev = tab[tab.indexOf(stepTab) - 1];
			if (prev) element.tabChange('editselffetch', prev);
			refreshStepButton();
			return false;

		});

		//下一步
		form.on('submit(next)', function (data) {
			// var next = tab[tab.indexOf(location.hash.replace(/^#tab=/, '')) + 1];
			var next = tab[tab.indexOf(stepTab) + 1];

			if (next) element.tabChange('editselffetch', next);
			refreshStepButton();
			return false;

		});

		form.on("select(order_deverys)", function (data) {
			if (data.value) {
				$.ajax({
					url: ns.url("shop/member/orderAddress"),
					data: {order_deverys: data.value},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						var result = res.data;
						var initdata = {
							province_id: result.province_id,
							city_id: result.city_id,
							district_id: result.district_id
						};
						console.log(initdata);
						initAddress(initdata, "storeform");
						$("input[name='addressname']").val(result.name);
						$("input[name='addressmobile']").val(result.mobile);
						$("input[name='address']").val(result.address);
					}
				});
			}
		});

		//添加商品主图
		$(".js-add-goods-image").click(function () {
			openAlbum(function (data) {
				for (var i = 0; i < data.length; i++) {
					if (goodsImage.length < GOODS_IMAGE_MAX) goodsImage.push(data[i].pic_path);
				}
				refreshGoodsImage();
			}, GOODS_IMAGE_MAX);
		});

		//视频上传
		var videoUpload = upload.render({
			elem: '#videoUpload',
			url: ns.url("shop/upload/video"),
			accept: "video",
			done: function (res) {
				if (res.code >= 0) {
					$("input[name='video_url']").val(res.data.path);
					$(".delete-video").show();
					var video = "goods_video";
					var myPlayer = videojs(video);
					var path = ns.img(res.data.path);

					videojs(video).ready(function () {
						var myPlayer = this;
						myPlayer.src(path);
						myPlayer.load(path);
						myPlayer.play();
					});

				}
				layer.msg(res.message);
			}
		});

		/**
		 * 表单验证
		 */
		form.verify({
			mobile: function(value) {
				var reg = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[1589])\d{8}$/;
				if (value == '') {
					return;
				}
			},
			isemail: function(value) {
				var reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
				if (value == '') {
					return;
				}
				if (!reg.test(value)) {
					return '请输入正确的邮箱!';
				}
			}
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			var goods_content = goodsContent.getContent();
			data.field.member_level_name = $(".member_level").find("option[value=" + data.field.member_level + "]").text();
			data.field.goods_content = goods_content;//商品详情
			data.field.goods_image = goodsImage.toString();//商品主图
			if(repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/member/addMember"),
				data: data.field,
				dataType: 'JSON', //服务器返回json格式数据
				type: 'POST', //HTTP请求类型
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("shop/member/memberList")
							},
							btn2: function () {
								location.href = ns.url("shop/member/addMember")
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		
		//普通图片上传
		var uploadInst = upload.render({
			elem: '#headImg',
			url: ns.url("shop/upload/image"),
			done: function(res) {
				if (res.code >= 0) {
					$("input[name='headimg']").val(res.data.pic_path);
					$("#headImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
				}
				return layer.msg(res.message);
			}
		});

	});
	
	function back() {
		location.href = ns.url("shop/member/memberList");
	}

    /**
     * 重新渲染表单
     */
    function refreshFrom(){
        form.render();
        orderAddressChange();//改变地址
        map_class.mapChange();
    }

	//动态改变订单地址赋值
	function orderAddressChange(){
		map_class.address.province = $("select[name=province_id]").val();
		map_class.address.province_name = $("select[name=province_id] option:selected").text();
		map_class.address.city = $("select[name=city_id]").val();
		map_class.address.city_name = $("select[name=city_id] option:selected").text();
		map_class.address.district = $("select[name=district_id]").val();
		map_class.address.district_name = $("select[name=district_id] option:selected").text();
		map_class.address.detail_address = $("input[name=address]").val()
	}

	/**
	 * 地址下拉框（主要用于坐标记录）
	 */
	function selectCallBack(){
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标
	}

	//地图点击回调事件
	function mapChangeCallBack(){
		$("input[name=address]").val(map_class.address.address);//详细地址
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标

		$.ajax({
			type : "POST",
			dataType: 'JSON',
			url : ns.url("shop/address/getGeographicId"),
			async : true,
			data : {
				"address" : map_class.address.area
			},
			success : function(data) {
				map_class.address.province = data.province_id;
				map_class.address.city = data.city_id;
				map_class.address.district = data.district_id;
				map_class.address.township = data.subdistrict_id;
				map_class.map_change = false;
				form.val("editselffetch", {
					"province_id": data.province_id
				});
				$("select[name=province_id]").change();
				form.val("editselffetch", {
					"city_id": data.city_id
				});
				$("select[name=city_id]").change();
				form.val("editselffetch", {
					"district_id": data.district_id
				});
				refreshFrom();//重新渲染form
				map_class.map_change = true;
			}
		});
	}
	//刷新步骤按钮
	function refreshStepButton() {
		var index = tab.indexOf(location.hash.replace(/^#tab=/, '')) + 1;
		switch (index) {
			case 1:
				$(".js-save").hide();
				$(".js-prev").hide();
				$(".js-next").show();
				break;
			case 2:
				$(".js-save").hide();
				$(".js-prev").show();
				$(".js-next").show();
				break;
			case 3:
				$(".js-save").show();
				$(".js-prev").show();
				$(".js-next").hide();
				break;
			case 4:
				$(".js-save").show();
				$(".js-prev").show();
				$(".js-next").hide();
				break;
		}
	}
	//渲染商品主图列表
	function refreshGoodsImage() {
		var goods_image_template = $("#goodsImage").html();
		laytpl(goods_image_template).render(goodsImage, function (html) {
			$(".js-goods-image").html(html);
			//加载图片放大
			loadImgMagnify();

			if (goodsImage.length) {

				//预览
				$(".js-goods-image .js-preview").click(function () {
					$(this).parent().prev().find("img").click();
				});

				//图片删除
				$(".js-goods-image .js-delete").click(function () {
					var index = $(this).attr("data-index");
					goodsImage.splice(index, 1);
					refreshGoodsImage();
				});

				// 拖拽
				$('.js-goods-image .item').arrangeable({
					//拖拽结束后执行回调
					callback: function (e) {
						var indexBefore = $(e).attr("data-index");//拖拽前的原始位置
						var indexAfter = $(e).index();//拖拽后的位置
						var temp = goodsImage[indexBefore];
						goodsImage[indexBefore] = goodsImage[indexAfter];
						goodsImage[indexAfter] = temp;

						refreshGoodsImage();
					}
				});
			}

			//最多传十张图
			if (goodsImage.length < GOODS_IMAGE_MAX) {
				$(".js-add-goods-image").show();
			} else {
				$(".js-add-goods-image").hide();
			}

		});
	}
</script>
{/block}