{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="storeform">
	<div class="ns-form">
		<div class="layui-form-item">
			<label class="layui-form-label">温馨提示：</label>
			<div class="layui-input-inline ns-special-length" style="color: #ff8143;font-size: 12px">
				格式参考：2020/02/02:   xx不适
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">病历详情：</label>
			<div class="layui-input-inline ns-special-length">
                <input type="hidden" name="goods_content" value="{$member_info['goods_content']}" />
				<script id="editor" type="text/plain" style="width:100%;height:500px;"></script>
			</div>
		</div>
	</div>
	<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"> </script>
	<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>
				<div class="ns-form-row">
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
					<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
				</div>
	<!-- 隐藏域 -->
	<input type="hidden" name="member_id" value="{$member_info.member_id}" />
</div>
{/block}
{block name="script"}
<script>

				var goodsContent;//商品详情
				goodsContent = UE.getEditor('editor');
				layui.use(['element','form', 'laydate','upload'], function() {
					var form = layui.form,
							element = layui.element,
							laydate = layui.laydate,
							upload = layui.upload,
							repeat_flag = false; //防重复标识

					form.render();

					// 加载商品详情
					goodsContent.ready(function () {
						goodsContent.setContent($("input[name='goods_content']").val());
					});

					/**
					 * 监听提交
					 */
					form.on('submit(save)', function(data) {
						var goods_content = goodsContent.getContent();
						data.field.goods_content = goods_content;//商品详情
						if(repeat_flag) return false;
						repeat_flag = true;

						$.ajax({
							url: ns.url("shop/member/addressDetail"),
							data: data.field,
							dataType: 'JSON', //服务器返回json格式数据
							type: 'POST', //HTTP请求类型
							success: function(res) {
								repeat_flag = false;
								if (res.code == 0) {
									layer.confirm('修改成功', {
										title:'操作提示',
										btn: ['返回列表', '留在该页'],
										yes: function(){
											location.href = ns.url("shop/member/memberList")
										},
										btn2: function () {
											location.reload();
										}
									});
								}else{
									layer.msg(res.message);
								}
							}
						});
					});

					//普通图片上传
					var uploadInst = upload.render({
						elem: '#headImg',
						url: ns.url("shop/upload/image"),
						done: function(res) {
							if (res.code >= 0) {
								$("input[name='headimg']").val(res.data.pic_path);
								$("#headImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
							}
							return layer.msg(res.message);
						}
					});

				});

				function back() {
					location.href = ns.url("shop/member/memberList");
				}

			</script>
{/block}