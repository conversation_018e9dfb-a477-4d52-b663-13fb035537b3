{extend name="base"/}
{block name="resources"}
<style>
	#container{ width: 650px; height: 500px; }
	.empty-address{ display: none; }
	.address-content {display: inline-block;vertical-align: top;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="storeform">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>用户名：</label>
		<div class="layui-input-block">
			<p class="ns-input-text ns-len-mid">{$member_info.data.username}</p>
		</div>
		<div class="ns-word-aux">用于登录，不可编辑</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>昵称：</label>
		<div class="layui-input-block">
			<input name="nickname" type="text" lay-verify="required" value="{$member_info.data.nickname}" class="layui-input ns-len-long">
		</div>
		<div class="ns-word-aux">用于机构自己人熟悉客户，比如:客户是“小明”，昵称可以是"张亮的孩子”，用干内部人知晓客户是谁，不做外部的展示</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">手机号：</label>
		<div class="layui-input-block">
			<input name="mobile" type="text" lay-verify="mobile" value="{$member_info.data.mobile}" class="layui-input ns-len-long">
		</div>
		<div class="ns-word-aux">已进行手机号验证，请填写正确的手机号</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">状态：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="status" value="1" lay-skin="switch" {if condition="$member_info.data.status == 1"} checked {/if} >
		</div>
		<div class="ns-word-aux">当状态处于关闭时，该会员则不能登录。</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label img-upload-lable ns-short-label">头像：</label>
		<div class="layui-input-inline">
			<div class="upload-img-block square">
				<div class="upload-img-box" id="headImg">
					{if condition="$member_info.data.headimg"}
						<img src="{:img($member_info.data.headimg)}" />
					{else/}
						<div class="ns-upload-default">
							<img src="SHOP_IMG/upload_img.png" />
							<p>点击上传</p>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>

	<!--<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>会员等级：</label>
		<div class="layui-input-inline ns-len-mid">
			<select class="member_level" name="member_level" lay-verify="required" lay-filter="member_level">
				<option value="">请选择</option>
				{volist name="member_level_list" id="member_level"}
				<option value="{$member_level.level_id}" {$member_info.data.member_level == $member_level.level_id ? 'selected' : '' }>{$member_level.level_name}</option>
				{/volist}
			</select>
		</div>
	</div>-->


	{if $order_deverys}
	<div class="layui-form-item">
		<label class="layui-form-label">常用收货地址：</label>
		<div class="layui-input-block ns-len-mid">
			<select id="order_deverys" name="order_deverys" lay-search="" lay-filter="order_deverys">
				<option value="">请选择常用收货地址</option>
				{foreach name="$order_deverys" item="v"}
				<option value="{$v.name}-{$v.mobile}-{$v.address}">{$v.name}-{$v.mobile}-{$v.address}</option>
				{/foreach}
			</select>
		</div>
	</div>
	{/if}

	<div class="layui-form-item">
		<label class="layui-form-label">收货昵称：</label>
		<div class="layui-input-inline">
			<input name="addressname" type="text" value="{$info.name}" class="layui-input ns-len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">收货电话：</label>
		<div class="layui-input-inline">
			<input name="addressmobile" type="text"  value="{$info.mobile}" class="layui-input ns-len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">* </span>收货地址：</label>
		<div class="layui-input-inline area-select">
			<select name="province_id" lay-filter="province_id" lay-verify="province_id">
				{foreach $province_list as $k => $v}
				<option value="{$v.id}" {if $info.province_id == $v.id}select{/if}>{$v.name}</option>
				{/foreach}
			</select>
		</div>

		<div class="layui-input-inline area-select">
			<select name="city_id"  lay-filter="city_id" lay-verify="city_id">
				<option value="">请选择城市</option>
			</select>
		</div>

		<div class="layui-input-inline area-select">
			<select name="district_id"  lay-filter="district_id" lay-verify="district_id">
				<option value="">请选择区/县</option>
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"></label>
		<div class="layui-input-block">
			<input type="text" name="address"  placeholder="请填写收货具体地址" value="{$info.address}" autocomplete="off" class="layui-input ns-len-long address-content" value="">
			<input type = "hidden" name="longitude" class="layui-input" value="{$info.longitude}"/>
			<input type = "hidden" name="latitude" class="layui-input"value="{$info.latitude}"/>
			<button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>
		</div>
		<div class="ns-word-aux">点击查找地址可在地图上显示输入的地理位置</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">地图定位：</label>
		<div class="layui-input-block">
			<div id="container" class="selffetch-map"></div>
		</div>
		<span class="layui-word-aux empty-address">请选择地理位置！在地图上点击得到的地理位置会自动填入到对应的输入框中</span>
	</div>

	<!--<div class="layui-form-item">
		<label class="layui-form-label">性别：</label>
		<div class="layui-input-inline">
			<input type="radio" name="sex" value="0" title="未知" {$member_info.data.sex == 0 ? 'checked' : ''}>
			<input type="radio" name="sex" value="1" title="男" {$member_info.data.sex == 1 ? 'checked' : ''}>
			<input type="radio" name="sex" value="2" title="女" {$member_info.data.sex == 2 ? 'checked' : ''}>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">生日：</label>
		<div class="layui-input-inline">
			<input name="birthday" type="text" id="laydate" value="" class="layui-input ns-len-mid">
		</div>
	</div>-->
	
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
	
	<!-- 隐藏域 -->
	<input type="hidden" name="member_id" value="{$member_info.data.member_id}" />
	<input type="hidden" name="headimg" value="{$member_info.data.headimg}" />
	<input type="hidden" class="birthday" value="{$member_info.data.birthday}" />
</div>
{/block}
{block name="script"}
<script type="text/javascript" src="{$http_type}://webapi.amap.com/maps?v=1.4.6&amp;key=66d777244fce2401c784bdaa1da8decd"></script>
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script>
	var birthday = $(".birthday").val();
	var map_class;
	$("input[name=birthday]").attr("value", ns.time_to_date(birthday, "YYYY-MM-DD"));
	
	layui.use(['form', 'laydate','upload'], function() {
		var form = layui.form,
			laydate = layui.laydate,
			upload = layui.upload,
			repeat_flag = false; //防重复标识
		form.render();

		var initdata = {province_id : '{$info.province_id}', city_id : '{$info.city_id}', district_id : '{$info.district_id}'};
		initAddress(initdata, "storeform");

		if('{$info.latitude}' == "" || '{$info.longitude}' == ""){
			var latlng = {lat:'',lng:''};
		}else{
			var latlng = {lat:'{$info.latitude}',lng:'{$info.longitude}'};
		}

		//地图展示
		map_class = new mapClass("container",latlng);

		laydate.render({
			elem: '#laydate'
		});

		/**
		 * 表单验证
		 */
		form.verify({
			mobile: function(value) {
				var reg = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[1589])\d{8}$/;
				if (value == '') {
					return;
				}
			},
			isemail: function(value) {
				var reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
				if (value == '') {
					return;
				}
				if (!reg.test(value)) {
					return '请输入正确的邮箱!';
				}
			}
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			
			data.field.member_level_name = $(".member_level").find("option[value=" + data.field.member_level + "]").text();
			
			if (data.field.status == undefined) {
				data.field.status = 0;
			}

			if(repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/member/editMember"),
				data: data.field,
				dataType: 'JSON', //服务器返回json格式数据
				type: 'POST', //HTTP请求类型
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(){
								location.href = ns.url("shop/member/memberList")
							},
							btn2: function() {
								location.reload();
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		form.on("select(order_deverys)", function (data) {
			if (data.value) {
				$.ajax({
					url: ns.url("shop/member/orderAddress"),
					data: {order_deverys: data.value},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						var result = res.data;
						var initdata = {
							province_id: result.province_id,
							city_id: result.city_id,
							district_id: result.district_id
						};
						console.log(initdata);
						initAddress(initdata, "storeform");
						$("input[name='addressname']").val(result.name);
						$("input[name='addressmobile']").val(result.mobile);
						$("input[name='address']").val(result.address);
					}
				});
			}
		});
		
		//普通图片上传
		var uploadInst = upload.render({
			elem: '#headImg',
			url: ns.url("shop/upload/image"),
			done: function(res) {
				if (res.code >= 0) {
					$("input[name='headimg']").val(res.data.pic_path);
					$("#headImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
				}
				return layer.msg(res.message);
			}
		});

	});
	
	function back() {
		location.href = ns.url("shop/member/memberList");
	}
	/**
	 * 重新渲染表单
	 */
	function refreshFrom(){
		form.render();
		orderAddressChange();//改变地址
		map_class.mapChange();
	}

	//动态改变订单地址赋值
	function orderAddressChange(){
		map_class.address.province = $("select[name=province_id]").val();
		map_class.address.province_name = $("select[name=province_id] option:selected").text();
		map_class.address.city = $("select[name=city_id]").val();
		map_class.address.city_name = $("select[name=city_id] option:selected").text();
		map_class.address.district = $("select[name=district_id]").val();
		map_class.address.district_name = $("select[name=district_id] option:selected").text();
		map_class.address.detail_address = $("input[name='address']").val();
	}

	/**
	 * 地址下拉框（主要用于坐标记录）
	 */
	function selectCallBack(){
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标
	}

	//地图点击回调事件
	function mapChangeCallBack(){
		$("input[name=address]").val(map_class.address.address);//详细地址
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标

		$.ajax({
			type : "POST",
			dataType: 'JSON',
			url : ns.url("shop/address/getGeographicId"),
			async : true,
			data : {
				"address" : map_class.address.area
			},
			success : function(data) {
				map_class.address.province = data.province_id;
				map_class.address.city = data.city_id;
				map_class.address.district = data.district_id;
				map_class.address.township = data.subdistrict_id;
				map_class.map_change = false;
				form.val("storeform", {
					"province_id": data.province_id
				});
				$("select[name=province_id]").change();
				form.val("storeform", {
					"city_id": data.city_id
				});
				$("select[name=city_id]").change();
				form.val("storeform", {
					"district_id": data.district_id
				});
				refreshFrom();//重新渲染form
				map_class.map_change = true;
			}
		})

	}
</script>
{/block}