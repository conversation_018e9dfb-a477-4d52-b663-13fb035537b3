{extend name="base"/}
{block name="main"}
<!-- 该页面主要实现了上传检测报告的功能，其他按钮功能集成在了journeymember目录中 -->
<div class="changelog-container">
    <!-- 顶部按钮区域 -->
    <div class="action-buttons">
        <button class="btn" data-action="survey">填写问卷调查</button>
        <button class="btn" data-action="upload">上传检测报告</button>
        <button class="btn" data-action="view-report">查看检测报告</button>
        <button class="btn" data-action="ai-prescription">AI处方</button>
        <button class="btn" data-action="feedback">干预反馈收集</button>
    </div>
    
    <!-- 报告总结区域 -->
    <div class="update-log">
        <h2 class="log-title">患者报告列表</h2>
        <p class="log-subtitle">查看您的健康检测报告总结</p>
        
        <div class="timeline-container">
            <!-- 时间轴主体容器，供JS动态插入 -->
        </div>
    </div>
</div>
{/block}

{block name="script"}
<!-- 引入Font Awesome图标库 -->
<link rel="stylesheet" href="/public/static/css/all.min.css">
<!-- 引入Marked.js -->
<script src="/public/static/js/marked.min.js"></script>
<!-- 引入EasyMDE编辑器 -->
<link rel="stylesheet" href="/public/static/css/easymde.min.css">
<script src="/public/static/js/easymde.min.js"></script>
<!-- 引入自定义弹窗组件 -->
<link rel="stylesheet" href="/public/static/css/nsModal.css">
<script src="/public/static/js/nsModal.js"></script>
<style>
.thumbnail-container {
    margin: 15px 0;
}

.thumbnail-container h4 {
    margin-bottom: 10px;
    color: #333;
}

.thumbnails {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.thumbnail-item {
    width: 100px;
    height: 100px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.2s;
}

.thumbnail-item:hover {
    transform: scale(1.05);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-preview-modal .modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    text-align: center;
}

.image-preview-modal img {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 4px;
}

.image-preview-modal .close {
    position: absolute;
    top: -30px;
    right: -30px;
    color: white;
    font-size: 30px;
    font-weight: bold;
    cursor: pointer;
}

.image-preview-modal .close:hover {
    color: #ccc;
}

.editor-container {
    margin: 20px 0;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
}

.editor-container .CodeMirror {
    height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.editor-container .editor-toolbar {
    border: none;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.editor-container .CodeMirror {
    border: none;
}

.changelog-container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 顶部按钮样式 */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 25px;
}

.btn {
    padding: 10px 22px;
    border: none;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s ease;
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    background-color: #4a6bdf; /* 默认颜色 */
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.btn:hover:before {
    transform: scaleX(1);
    transform-origin: left;
}

.btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* 为不同按钮设置不同颜色 */
.btn[data-action="survey"] {
    background-color: #ff7043; /* 橙色 */
}

.btn[data-action="upload"] {
    background-color: #2ecc71; /* 绿色 */
}

.btn[data-action="ai-prescription"] {
    background-color: #3498db; /* 蓝色 */
}

.btn[data-action="feedback"] {
    background-color: #9b59b6; /* 紫色 */
}

.btn[data-action="view-report"] {
    background-color: #f39c12; /* 橙色 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}


/* 日志标题样式 */
.log-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.log-subtitle {
    color: #666;
    margin-bottom: 30px;
}

/* 时间轴样式 - 修改后的版本 */
.timeline-container {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    display: flex;
    margin-bottom: 40px;
    position: relative;
}

.timeline-content {
    width: 45%;
    display: flex;
    align-items: center;
}

.timeline-content.left {
    justify-content: flex-end;
    padding-right: 20px;
}

.timeline-content.right {
    padding-left: 20px;
}

.timeline-center {
    position: relative;
    width: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: -20px;
    bottom: -20px;
    width: 2px;
    background-color: #e0e0e0;
    transform: translateX(-50%);
}

/* 节点样式 */
.timeline-node {
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 2px solid #6c7ae0;
    border-radius: 50%;
    z-index: 2;
    position: relative;
}

.timeline-node:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background-color: #6c7ae0;
    border-radius: 50%;
}

.date-badge {
    background-color: #6c7ae0;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    position: relative;
    z-index: 2;
    white-space: nowrap;
    height: fit-content;
    display: inline-block;
}

.timeline-content.left .date-badge {
    margin-right: 0;
    position: relative;
}

.timeline-content.left .date-badge:after {
    content: '';
    position: absolute;
    top: 50%;
    right: -16px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-left-color: #6c7ae0;
    transform: translateY(-50%);
}

.timeline-content.right .date-badge {
    margin-left: 0;
    position: relative;
}

.timeline-content.right .date-badge:after {
    content: '';
    position: absolute;
    top: 50%;
    left: -16px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-right-color: #6c7ae0;
    transform: translateY(-50%);
}

/* 版本信息样式 */
.version-info {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    width: 100%;
}

.version-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.version-number {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.version-tag {
    background-color: #ff5252;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    margin-left: 10px;
}

.version-desc {
    color: #666;
    margin-bottom: 10px;
}

.update-list {
    padding-left: 20px;
    margin: 0;
}

.update-list li {
    margin-bottom: 5px;
    color: #555;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .timeline-item {
        flex-direction: column;
    }
    
    .timeline-content.left, .timeline-content.right {
        width: 100%;
        padding: 0 0 0 40px;
        justify-content: flex-start;
    }
    
    .timeline-center {
        position: absolute;
        left: 0;
        width: auto;
    }
    
    .timeline-line {
        left: 20px;
    }
    
    .timeline-node {
        left: 20px;
        margin-left: -8px;
    }
    
    .timeline-content.left .date-badge, .timeline-content.right .date-badge {
        margin-left: 40px;
    }
    
    .timeline-content.left .date-badge:after, .timeline-content.right .date-badge:after {
        left: -16px;
        right: auto;
        border-right-color: #6c7ae0;
        border-left-color: transparent;
    }
    
    .timeline-content.left .version-info {
        order: 2;
        margin-top: 10px;
    }
    
    .timeline-content.left .date-badge {
        order: 1;
    }
}
</style>

<script>
$(function() {
    // 创建编辑器模态框
    let currentEditor = null;
    let currentContent = null;
    let currentUploadImages = [];
    let reportId = 0;
    const urlParams = new URLSearchParams(window.location.search);
    const member_id = urlParams.get('member_id');
    // let currentUploadImages = [
    //     "http://fenzhuang.oss-cn-shenzhen.aliyuncs.com/upload/1/analyse_image/20250507/20250507082734174662085481630.jpg",
    //     "http://fenzhuang.oss-cn-shenzhen.aliyuncs.com/upload/1/analyse_image/20250507/20250507082735174662085531514.jpg"
    // ];
    // 图片预览功能
    window.previewImage = function(imgUrl) {
        const modal = document.createElement('div');
        modal.className = 'image-preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close">&times;</span>
                <img src="${imgUrl}" alt="预览大图" />
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击关闭
        modal.querySelector('.close').onclick = function() {
            modal.remove();
        };
        
        // 点击模态框背景关闭
        modal.onclick = function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        };
    }
    
    // 创建编辑器模态框
    function createEditorModal(streamContentRight, streamContainer, fileLinksContainer, streamModal) {
        let content = currentContent;
        // 如果已经存在编辑器实例，先销毁
        if (currentEditor) {
            currentEditor.toTextArea();
            currentEditor = null;
        }
        
        // 检查是否已经处于编辑模式
        if (streamContainer.classList.contains('edit-mode')) {
            return;
        }
        
        // 添加编辑模式类
        streamContainer.classList.add('edit-mode');
        
        // 创建编辑器容器
        const editContainer = document.createElement('div');
        editContainer.className = 'editor-container';
        editContainer.innerHTML = `
            <textarea id="markdown-editor-${Date.now()}"></textarea>
            <div class="edit-actions">
                <button class="cancel-btn">取消</button>
                <button class="save-btn">保存</button>
            </div>
        `;

        // 替换右侧内容
        streamContentRight.innerHTML = '';
        streamContentRight.appendChild(editContainer);

        // 初始化EasyMDE编辑器
        currentEditor = new EasyMDE({
            element: editContainer.querySelector('textarea'),
            autoDownloadFontAwesome: false,
            spellChecker: false,
            status: false,
            placeholder: '在这里输入Markdown内容...',
            previewImagesInEditor: true,
            toolbar: [
                'bold', 'italic', 'strikethrough', 'heading-smaller', 'heading-bigger', '|',
                'quote', 'code', 'unordered-list', 'ordered-list', '|',
                'link', 'table', 'horizontal-rule', '|',
                'clean-block', 'preview', 'side-by-side', 'fullscreen', '|',
                'undo', 'redo', '|',
                'guide'
            ]
        });
        
        // 设置初始内容
        currentEditor.value(content || '');

        // 绑定取消按钮事件
        const handleCancel = () => {
            streamContainer.classList.remove('edit-mode');
            if (currentEditor) {
                currentEditor.toTextArea();
                currentEditor = null;
            }
            streamContentRight.innerHTML = `
                <div class="file-links-container">
                    <h4>相关文件</h4>
                    <div class="file-links" id="file-links">${fileLinksContainer.innerHTML}</div>
                </div>
                <button id="edit-btn" class="edit-btn">编辑内容</button>
            `;
            
            // 重新绑定编辑按钮事件
            const newEditBtn = streamModal.querySelector('#edit-btn');
            if (newEditBtn) {
                newEditBtn.addEventListener('click', function() {
                    createEditorModal(streamContentRight, streamContainer, fileLinksContainer, streamModal);
                });
            }
        };

        // 绑定保存按钮事件
        const handleSave = () => {
            console.log('保存按钮被点击');
            console.log('currentEditor', currentEditor);
            if (!currentEditor) return;
            
            const newContent = currentEditor.value();
            localStorage.setItem('journey-member-md', newContent);

            // 更新当前内容
            currentContent = newContent;
            
            // 更新流式响应内容
            const resultElement = document.getElementById('stream-result');
            if (resultElement && typeof marked !== 'undefined') {
                resultElement.innerHTML = marked.parse(newContent);
            }

            // 报告id，reportId
            // 发起更新请求 
            if(reportId) {
                fetch('/api/member/updateUserEditedResult', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: reportId,
                        edited_result: newContent
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if(data.code >= 0) {
                        handleCancel(); // 只有保存成功才关闭编辑器
                    } else {
                        console.error('保存失败:', data.message);
                        NSModal.error({
                            title: '保存失败',
                            content: data.message
                        });
                    }
                })
                .catch(error => {
                    console.error('保存错误:', error);
                        NSModal.error({
                            title: '保存错误',
                            content: error.message
                        });
                });
            } else {
                console.error('报告id为空');
                    NSModal.error({
                        title: '保存失败',
                        content: '报告ID为空'
                    });
            }
        };

        editContainer.querySelector('.cancel-btn').addEventListener('click', handleCancel);
        editContainer.querySelector('.save-btn').addEventListener('click', handleSave);
    }

    // 按钮点击事件处理
    $('.btn').on('click', function() {
        // 获取按钮的动作类型
        const action = $(this).data('action');
        
        // 添加点击动画效果
        $(this).addClass('btn-clicked');
        setTimeout(() => {
            $(this).removeClass('btn-clicked');
        }, 300);
        
        // 根据不同的按钮执行不同的操作
        switch(action) {
            case 'survey':
                handleSurvey();
                break;
            case 'upload':
                handleUpload();
                break;
            case 'ai-prescription':
                handleAIPrescription();
                break;
            case 'feedback':
                handleFeedback();
                break;
            case 'view-report':
                handleViewReport();
                break;
        }
    });
    
    // 问卷调查处理函数
    function handleSurvey() {
        // 构建跳转URL,携带member_id参数
        const url = member_id 
            ? `/shop/JourneySurvey/index?member_id=${member_id}`
            : '/shop/JourneySurvey/index';
            
        // 跳转到问卷管理页
        window.location.href = url;
    }
    
    // AI处方处理函数
    function handleAIPrescription() {
        // 创建模态框询问用户ID
        const modalDiv = document.createElement('div');
        modalDiv.className = 'upload-modal';
        modalDiv.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h3>AI处方生成</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="input-group">
                        <label for="user-id">请输入用户ID</label>
                        <input type="text" id="user-id" placeholder="输入用户ID" class="form-input">
                    </div>
                    <button id="generate-btn" class="action-btn">生成处方</button>
                </div>
            </div>
        `;

        // 添加模态框样式
        const style = document.createElement('style');
        style.textContent = `
            .input-group {
                margin-bottom: 15px;
            }
            .input-group label {
                display: block;
                margin-bottom: 5px;
                color: #333;
                font-weight: 500;
            }
            .form-input {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            .action-btn {
                width: 100%;
                padding: 12px;
                background-color: #4a6bdf;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .action-btn:hover {
                background-color: #3857b8;
            }

            /* 编辑器模态框样式 */
            .editor-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1100;
            }

            .editor-modal-container {
                background: white;
                border-radius: 12px;
                width: 95%;
                max-width: 1200px;
                height: 85vh;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .editor-modal-header {
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .editor-modal-header h3 {
                margin: 0;
                font-size: 18px;
                color: #333;
            }

            .editor-modal-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 20px;
                overflow: hidden;
            }

            .editor-modal .editor-container {
                flex: 1;
                margin: 0;
                overflow: hidden;
            }

            .editor-modal .CodeMirror {
                height: 100%;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }

            .editor-modal-footer {
                padding: 15px 0 0;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }

            .editor-modal-footer button {
                padding: 8px 20px;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s;
            }

            .save-btn {
                background-color: #4a6bdf;
                color: white;
            }

            .save-btn:hover {
                background-color: #3857b8;
            }

            .cancel-btn {
                background-color: #f5f5f5;
                color: #666;
            }

            .cancel-btn:hover {
                background-color: #e8e8e8;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(modalDiv);

        // 关闭模态框的函数
        function closeModal() {
            document.body.removeChild(modalDiv);
            document.head.removeChild(style);
        }

        // 绑定关闭按钮事件
        modalDiv.querySelector('.close-btn').addEventListener('click', closeModal);

        // 点击模态框外部关闭
        modalDiv.addEventListener('click', (e) => {
            if (e.target === modalDiv) {
                closeModal();
            }
        });

        // 绑定生成按钮事件
        modalDiv.querySelector('#generate-btn').addEventListener('click', () => {
            const userId = document.getElementById('user-id').value.trim();
            if (!userId) {
                alert('请输入有效的用户ID');
                return;
            }
            
            // 关闭当前模态框
            closeModal();
            
            // 创建请求数据
            const formData = new FormData();
            formData.append('user_id', userId);
            
            // 调用流式响应接口
            handleStreamingResponse('/api/qianwen/generatePrescription', formData, 'prescription');
        });
    }
    
    // 查看检测报告处理函数
    function handleViewReport() {
        // 创建模态框询问报告ID
        const modalDiv = document.createElement('div');
        modalDiv.className = 'upload-modal';
        modalDiv.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h3>查看检测报告</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="input-group">
                        <label for="report-id">请输入报告ID</label>
                        <input type="text" id="report-id" placeholder="输入报告ID" class="form-input">
                    </div>
                    <button id="view-btn" class="action-btn">查看报告</button>
                </div>
            </div>
        `;

        // 添加模态框样式
        const style = document.createElement('style');
        style.textContent = `
            .input-group {
                margin-bottom: 15px;
            }
            .input-group label {
                display: block;
                margin-bottom: 5px;
                color: #333;
                font-weight: 500;
            }
            .form-input {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            .action-btn {
                width: 100%;
                padding: 12px;
                background-color: #4a6bdf;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .action-btn:hover {
                background-color: #3857b8;
            }

            /* 编辑器模态框样式 */
            .editor-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1100;
            }

            .editor-modal-container {
                background: white;
                border-radius: 12px;
                width: 95%;
                max-width: 1200px;
                height: 85vh;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .editor-modal-header {
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .editor-modal-header h3 {
                margin: 0;
                font-size: 18px;
                color: #333;
            }

            .editor-modal-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 20px;
                overflow: hidden;
            }

            .editor-modal .editor-container {
                flex: 1;
                margin: 0;
                overflow: hidden;
            }

            .editor-modal .CodeMirror {
                height: 100%;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }

            .editor-modal-footer {
                padding: 15px 0 0;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }

            .editor-modal-footer button {
                padding: 8px 20px;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s;
            }

            .save-btn {
                background-color: #4a6bdf;
                color: white;
            }

            .save-btn:hover {
                background-color: #3857b8;
            }

            .cancel-btn {
                background-color: #f5f5f5;
                color: #666;
            }

            .cancel-btn:hover {
                background-color: #e8e8e8;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(modalDiv);

        // 关闭模态框的函数
        function closeModal() {
            document.body.removeChild(modalDiv);
            document.head.removeChild(style);
        }

        // 绑定关闭按钮事件
        modalDiv.querySelector('.close-btn').addEventListener('click', closeModal);

        // 点击模态框外部关闭
        modalDiv.addEventListener('click', (e) => {
            if (e.target === modalDiv) {
                closeModal();
            }
        });

        // 绑定查看按钮事件
        modalDiv.querySelector('#view-btn').addEventListener('click', () => {
            const reportId = document.getElementById('report-id').value.trim();
            if (!reportId) {
                alert('请输入有效的报告ID');
                return;
            }
            
            // 关闭当前模态框
            closeModal();
            
            // 创建请求数据
            const formData = new FormData();
            formData.append('report_id', reportId);
            
            // 调用流式响应接口
            handleStreamingResponse('/api/qianwen/viewReport', formData, 'report');
        });
    }
    
    // 干预反馈收集处理函数
    function handleFeedback() {
        // 构建跳转URL,携带member_id参数
        const url = member_id 
            ? `/shop/journeySurvey/feedbackCollect?member_id=${member_id}`
            : '/shop/journeySurvey/feedbackCollect';
            
        // 跳转到问卷管理页
        window.location.href = url;
    }
    
    // 上传检测报告处理函数
    function handleUpload() {
        // 从url中拿到member_id
        const urlParams = new URLSearchParams(window.location.search);
        // 创建模态框
        const modalDiv = document.createElement('div');
        modalDiv.className = 'upload-modal';
        modalDiv.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h3>选择上传方式</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="drop-area">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>拖拽文件到此处或点击下方按钮选择文件</p>
                        <small>支持单个PDF文件(最大10MB)或多张图片(每张最大8MB)</small>
                    </div>
                    <button class="upload-option" data-type="pdf">
                        <i class="fas fa-file-pdf"></i>
                        <span>上传PDF文件</span>
                        <small>支持单个PDF文件，最大10MB</small>
                    </button>
                    <button class="upload-option" data-type="images">
                        <i class="fas fa-images"></i>
                        <span>上传图片文件</span>
                        <small>支持多张图片，每张最大8MB</small>
                    </button>
                </div>
            </div>
        `;

        // 添加模态框样式
        const style = document.createElement('style');
        style.textContent = `
            .upload-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }
            .modal-container {
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }
            .modal-header h3 {
                margin: 0;
                color: #333;
            }
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
            }
            .modal-content {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            .upload-modal .drop-area {
                border: 2px dashed #ddd;
                border-radius: 8px;
                padding: 30px;
                margin-bottom: 20px;
                text-align: center;
                background-color: #f8f9fa;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .upload-modal .drop-area:hover {
                border-color: #4a6bdf;
                background-color: #f0f4ff;
            }
            .upload-modal .drop-area.active {
                border-color: #4a6bdf;
                background-color: #f0f4ff;
            }
            .upload-modal .drop-area i {
                font-size: 48px;
                color: #4a6bdf;
                margin-bottom: 15px;
            }
            .upload-modal .drop-area p {
                font-size: 16px;
                color: #333;
                margin: 10px 0;
            }
            .upload-modal .drop-area small {
                color: #666;
                font-size: 12px;
                display: block;
            }
            .upload-option {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
                border: 2px dashed #ddd;
                border-radius: 8px;
                background: none;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .upload-option:hover {
                border-color: #4a6bdf;
                background: #f5f7ff;
            }
            .upload-option i {
                font-size: 32px;
                margin-bottom: 10px;
                color: #4a6bdf;
            }
            .upload-option span {
                font-size: 16px;
                font-weight: 500;
                color: #333;
                margin-bottom: 5px;
            }
            .upload-option small {
                color: #666;
                font-size: 12px;
            }
        `;
        document.head.appendChild(style);        
        document.body.appendChild(modalDiv);                
        // 获取拖拽区域元素        
        const dropArea = modalDiv.querySelector('.drop-area');                
        // 添加拖拽事件监听器        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });
        // 拖拽进入和悬停时添加active类        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, () => {
                dropArea.classList.add('active');
            });
        });
        
        // 拖拽离开时移除active类
        dropArea.addEventListener('dragleave', () => {
            dropArea.classList.remove('active');
        });
        
        // 拖拽放置时处理文件
        dropArea.addEventListener('drop', (e) => {
            dropArea.classList.remove('active');
            
            // 获取拖拽的文件列表
            const files = e.dataTransfer.files;
            if (files.length === 0) { return; }
            
            // 初始化文件类型标志
            let hasPdf = false;
            let hasImages = false;
            let hasOtherTypes = false;
            let pdfCount = 0;
            
            // 遍历文件列表进行类型和数量检查
            Array.from(files).forEach(file => {
                if (file.name.toLowerCase().endsWith('.pdf')) {
                    hasPdf = true;
                    pdfCount++;
                } else if (file.type.startsWith('image/')) {
                    hasImages = true;
                } else {
                    hasOtherTypes = true;
                }
            });
            
            // 验证文件类型组合规则
            if (hasOtherTypes || (hasPdf && hasImages) || pdfCount > 1) {
                alert('不支持同时拖拽多种文件类型或多个PDF文件。');
                return;
            }
            
            // 验证文件大小规则
            let isValid = true;
            Array.from(files).forEach(file => {
                if (file.name.toLowerCase().endsWith('.pdf') && file.size > 10 * 1024 * 1024) {
                    alert('PDF文件大小不能超过10MB');
                    isValid = false;
                } else if (file.type.startsWith('image/') && file.size > 8 * 1024 * 1024) {
                    alert(`图片 ${file.name} 大小超过8MB限制`);
                    isValid = false;
                }
            });
            
            if (!isValid) { return; }
            
            // 文件验证通过，关闭初始模态框
            closeModal();
            
            if (hasPdf) {
                const formData = new FormData();
                formData.append('file', files[0]);
                handleStreamingResponse('/api/qianwen/analyzePdf', formData, 'pdf');
            } else if (hasImages) {
                // 添加状态模态框样式 (确保不会重复添加)
                const styleId = 'analysis-status-style';
                if (!document.getElementById(styleId)) {
                     const style = document.createElement('style');
                     style.id = styleId; // 添加 ID 方便检查和移除 (虽然通常不移除动态样式)
                     style.textContent = `
                         .analysis-status {
                             position: fixed;
                             top: 0;
                             left: 0;
                             width: 100%;
                             height: 100%;
                             background: rgba(0,0,0,0.7);
                             z-index: 1000;
                             display: flex; /* 使用 flexbox 居中内容 */
                             justify-content: center;
                             align-items: center;
                             font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* 添加字体样式 */
                         }
                         .status-container {
                             background: white;
                             border-radius: 8px;
                             width: 80%;
                             max-width: 800px;
                             max-height: 80vh;
                             overflow: hidden;
                             display: flex;
                             flex-direction: column;
                             box-shadow: 0 2px 10px rgba(0,0,0,0.1); /* 添加阴影 */
                         }
                         .status-header {
                             padding: 15px;
                             background: #f5f5f5;
                             border-bottom: 1px solid #ddd;
                             display: flex;
                             justify-content: space-between;
                             align-items: center;
                             font-size: 18px; /* 添加字体大小 */
                             font-weight: bold; /* 添加字体粗细 */
                         }
                         .status-content {
                             padding: 15px;
                             overflow-y: auto;
                             max-height: calc(80vh - 120px); /* 根据头部和底部高度计算 */
                         }
                         .status-spinner {
                             width: 20px;
                             height: 20px;
                             border: 2px solid #3498db;
                             border-radius: 50%;
                             border-top-color: transparent;
                             animation: spin 1s linear infinite;
                         }
                         .status-footer {
                             padding: 15px;
                             border-top: 1px solid #ddd;
                             text-align: center;
                         }
                         .analyze-button {
                             padding: 8px 20px;
                             background-color: #4a6bdf;
                             color: white;
                             border: none;
                             border-radius: 4px;
                             cursor: pointer;
                             font-weight: 500;
                             transition: background-color 0.3s;
                         }
                         .analyze-button:hover {
                             background-color: #3857b8;
                         }
                         .image-preview-container {
                             margin-top: 15px;
                             border-top: 1px solid #eee;
                             padding-top: 15px;
                         }
                         .image-preview-container h4 {
                             margin: 0 0 10px 0;
                             color: #333;
                             font-size: 16px; /* 确保h4样式 */
                         }
                         .image-grid {
                             display: grid;
                             grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                             gap: 10px;
                             margin-top: 10px;
                         }
                         .image-grid img {
                             width: 100%;
                             height: 120px;
                             object-fit: cover;
                             border-radius: 4px;
                             border: 1px solid #ddd;
                         }
                         @keyframes spin {
                             to { transform: rotate(360deg); }
                         }
                     `;
                     document.head.appendChild(style);
                }
                // 创建状态提示模态框
                const statusDiv = document.createElement('div');
                statusDiv.className = 'analysis-status';
                const fileTypeText = '图片'; // 硬编码为图片，因为此分支只处理图片
                const fileCountText = `已选择 ${files.length} 张图片`; // files 来自 drop 事件处理函数的作用域

                statusDiv.innerHTML = `
                    <div class="status-container">
                        <div class="status-header">
                            <h3>正在上传${fileTypeText}: ${fileCountText}</h3>
                            <div class="status-spinner"></div>
                        </div>
                        <div class="status-content">
                            <pre id="analysis-result">上传处理中，请稍候...</pre>
                            <div id="image-preview" class="image-preview-container" style="display: none;">
                                <h4>已上传图片预览</h4>
                                <div class="image-grid"></div>
                            </div>
                        </div>
                        <div class="status-footer" style="display: none;">
                            <button id="cancel-btn" class="analyze-button" style="margin-right: 10px;">取消</button>
                            <button id="analyze-btn" class="analyze-button">分析图片</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(statusDiv);

                // 图片文件处理 - 循环单个上传
                const filesArray = Array.from(files); // files 来自 drop 事件的 e.dataTransfer.files
                const results = [];
                let successCount = 0;
                let failCount = 0;
                let uploadedImages = [];
                
                const uploadNext = (index) => {
                    if (index >= filesArray.length) {
                        const resultElement = document.getElementById('analysis-result');
                        resultElement.textContent = `上传完成\n成功：${successCount}张\n失败：${failCount}张`;
                        
                        // 显示图片预览
                        const imagePreview = document.getElementById('image-preview');
                        const imageGrid = imagePreview.querySelector('.image-grid');
                        imagePreview.style.display = 'block';
                        
                        // 停止加载动画
                        const spinner = statusDiv.querySelector('.status-spinner'); // 使用 statusDiv 内部的 spinner
                        spinner.style.animation = 'none';
                        if (failCount > 0) {
                            spinner.style.borderColor = '#ff4444';
                        } else {
                            spinner.style.borderColor = '#2ecc71';
                        }
                        
                        // 显示分析按钮
                        const statusFooter = statusDiv.querySelector('.status-footer'); // 使用 statusDiv 内部的 footer
                        statusFooter.style.display = 'block';
                        
                        // 绑定分析按钮点击事件
                        const analyzeBtn = document.getElementById('analyze-btn');
                        analyzeBtn.addEventListener('click', () => {
                            analyzeBtn.disabled = true;
                            analyzeBtn.textContent = '分析中...';
                            // 更新当前分析上传的图片列表
                            currentUploadImages = uploadedImages;
                            
                            // 创建图片分析的请求数据
                            const imageAnalysisData = new FormData();
                            uploadedImages.forEach((img, idx) => {
                                // 检查图片URL是否包含域名，如果没有则拼接当前域名
                                let processedImg = img;
                                if (!img.startsWith('http') && !img.startsWith('https') && !img.startsWith('/')) {
                                    processedImg = window.location.origin + '/' + img;
                                } else if (img.startsWith('http://')) {
                                    processedImg = img.replace('http://', 'https://');
                                }
                                imageAnalysisData.append('images[]', processedImg);
                            });
                            
                            // 关闭当前状态框
                            if (statusDiv.parentNode === document.body) { // 确保 statusDiv 还在 body 中
                                document.body.removeChild(statusDiv);
                            }
                            
                            // 调用流式分析接口
                            handleStreamingResponse('/api/qianwen/analyzeImages', imageAnalysisData, 'images');
                        });

                        // 绑定取消按钮事件 (确保在 analyzeBtn 之后，因为 footer 现在才显示)
                        const cancelBtn = document.getElementById('cancel-btn');
                        cancelBtn.addEventListener('click', function() {
                             if (statusDiv.parentNode === document.body) { // 确保 statusDiv 还在 body 中
                                document.body.removeChild(statusDiv);
                            }
                        });
                        return;
                    }
                    
                    const file = filesArray[index];
                    const singleFormData = new FormData();
                    singleFormData.append('file', file);
                    
                    // 上传单个图片
                    fetch('/api/upload/image', {
                        method: 'POST',
                        body: singleFormData
                    }).then(response => response.json())
                    .then(data => {
                        if (data.code === 10066) {
                            successCount++;
                            uploadedImages.push(data.data.pic_path);
                            // 添加图片预览
                            const imageGrid = statusDiv.querySelector('.image-grid'); // 使用 statusDiv 内部的 imageGrid
                            const img = document.createElement('img');
                            // 检查图片URL是否包含http或https协议头
                            let picPath = data.data.pic_path;
                            if (!picPath.startsWith('http') && !picPath.startsWith('https') && !picPath.startsWith('/')) {
                                picPath = '/' + picPath;
                            }
                            img.src = picPath;
                            img.alt = file.name;
                            img.title = file.name;
                            imageGrid.appendChild(img);
                        } else {
                            failCount++;
                            console.error('上传失败:', data.message);
                        }
                        
                        // 更新结果文本
                        const resultElement = document.getElementById('analysis-result');
                        resultElement.textContent = `上传中: ${index + 1}/${filesArray.length}\n成功: ${successCount}\n失败: ${failCount}`;
                        
                        // 继续上传下一张
                        uploadNext(index + 1);
                    }).catch(error => {
                        console.error('上传请求失败:', error);
                        failCount++;
                        
                        // 更新结果文本
                        const resultElement = document.getElementById('analysis-result');
                        resultElement.textContent = `上传中: ${index + 1}/${filesArray.length}\n成功: ${successCount}\n失败: ${failCount}\n错误: ${error.message}`;
                        
                        // 继续上传下一张
                        uploadNext(index + 1);
                    });
                };
                
                // 开始上传第一张图片
                uploadNext(0);
            }
        });
        
        // 关闭模态框的函数
        function closeModal() {
            document.body.removeChild(modalDiv);
            document.head.removeChild(style);
        }

        // 绑定关闭按钮事件
        modalDiv.querySelector('.close-btn').addEventListener('click', closeModal);

        // 点击模态框外部关闭
        modalDiv.addEventListener('click', (e) => {
            if (e.target === modalDiv) {
                closeModal();
            }
        });

        // 处理上传选项点击事件
        modalDiv.querySelectorAll('.upload-option').forEach(option => {
            option.addEventListener('click', () => {
                // 首先关闭模态框
                closeModal();
                
                const uploadType = option.dataset.type;
                
                // 创建隐藏的文件输入元素
                const fileInput = document.createElement('input');
                fileInput.style.display = 'none';
                
                if (uploadType === 'pdf') {
                    fileInput.type = 'file';
                    fileInput.accept = '.pdf';
                    fileInput.multiple = false;
                } else if (uploadType === 'images') {
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.multiple = true;
                }
                
                // 添加change事件处理
                fileInput.addEventListener('change', function(e) {
                    const files = e.target.files;
                    if (!files.length) return;
                    
                    const formData = new FormData();
                    let isValid = true;
                    
                    // 根据文件类型进行不同的验证和处理
                    if (fileInput.accept === '.pdf') {
                        const file = files[0];
                        // 验证PDF文件
                        if (!file.name.endsWith('.pdf')) {
                            alert('请选择PDF文件');
                            return;
                        }
                        if (file.size > 10 * 1024 * 1024) {
                            alert('PDF文件大小不能超过10MB');
                            return;
                        }
                        formData.append('file', file);
                    } else {
                        // 验证图片文件
                        Array.from(files).forEach((file, index) => {
                            if (!file.type.startsWith('image/')) {
                                alert('请只选择图片文件');
                                isValid = false;
                                return;
                            }
                            if (file.size > 8 * 1024 * 1024) {
                                alert(`图片 ${file.name} 大小超过8MB限制`);
                                isValid = false;
                                return;
                            }
                            formData.append('files[]', file);
                        });
                        
                        if (!isValid) return;
                    }
                    
                    // 设置上传类型
                    formData.append('upload_type', fileInput.accept === '.pdf' ? 'pdf' : 'images');
                    
                    // 根据文件类型选择不同的上传端点
                    const uploadEndpoint = fileInput.accept === '.pdf' ? '/api/qianwen/analyzePdf' : '/api/upload/multiImage';
                    
                    if (fileInput.accept === '.pdf') {
                        // PDF文件处理 - 使用流式响应
                        handleStreamingResponse(uploadEndpoint, formData, 'pdf');
                    } else {
                        // 创建状态提示
                        const statusDiv = document.createElement('div');
                        statusDiv.className = 'analysis-status';
                        const fileTypeText = '图片';
                        const fileCountText = `已选择 ${files.length} 张图片`;
                        
                        statusDiv.innerHTML = `
                            <div class="status-container">
                                <div class="status-header">
                                    <h3>正在上传${fileTypeText}: ${fileCountText}</h3>
                                    <div class="status-spinner"></div>
                                </div>
                                <div class="status-content">
                                    <pre id="analysis-result">上传处理中，请稍候...</pre>
                                    <div id="image-preview" class="image-preview-container" style="display: none;">
                                        <h4>已上传图片预览</h4>
                                        <div class="image-grid"></div>
                                    </div>
                                </div>
                                <div class="status-footer" style="display: none;">
                                    <button id="cancel-btn" class="analyze-button" style="margin-right: 10px;">取消</button>
                                    <button id="analyze-btn" class="analyze-button">分析图片</button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(statusDiv);
                        
                        // 绑定取消按钮事件
                        document.getElementById('cancel-btn').addEventListener('click', function() {
                            document.body.removeChild(statusDiv);
                        });
                        
                        // 添加样式
                        const statusStyle = document.createElement('style');
                        statusStyle.textContent = `
                            .analysis-status {
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(0,0,0,0.7);
                                z-index: 1000;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }
                            .status-container {
                                background: white;
                                border-radius: 8px;
                                width: 80%;
                                max-width: 800px;
                                max-height: 80vh;
                                overflow: hidden;
                                display: flex;
                                flex-direction: column;
                            }
                            .status-header {
                                padding: 15px;
                                background: #f5f5f5;
                                border-bottom: 1px solid #ddd;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            }
                            .status-content {
                                padding: 15px;
                                overflow-y: auto;
                                max-height: calc(80vh - 120px);
                            }
                            .status-spinner {
                                width: 20px;
                                height: 20px;
                                border: 2px solid #3498db;
                                border-radius: 50%;
                                border-top-color: transparent;
                                animation: spin 1s linear infinite;
                            }
                            .status-footer {
                                padding: 15px;
                                border-top: 1px solid #ddd;
                                text-align: center;
                            }
                            .analyze-button {
                                padding: 8px 20px;
                                background-color: #4a6bdf;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                                font-weight: 500;
                                transition: background-color 0.3s;
                            }
                            .analyze-button:hover {
                                background-color: #3857b8;
                            }
                            .image-preview-container {
                                margin-top: 15px;
                                border-top: 1px solid #eee;
                                padding-top: 15px;
                            }
                            .image-preview-container h4 {
                                margin: 0 0 10px 0;
                                color: #333;
                            }
                            .image-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                                gap: 10px;
                                margin-top: 10px;
                            }
                            .image-grid img {
                                width: 100%;
                                height: 120px;
                                object-fit: cover;
                                border-radius: 4px;
                                border: 1px solid #ddd;
                            }
                            @keyframes spin {
                                to { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(statusStyle);
                        
                        // 图片文件处理 - 循环单个上传
                        const filesArray = Array.from(files); // files 来自 drop 事件的 e.dataTransfer.files
                        const results = [];
                        let successCount = 0;
                        let failCount = 0;
                        let uploadedImages = [];
                        
                        const uploadNext = (index) => {
                            if (index >= filesArray.length) {
                                const resultElement = document.getElementById('analysis-result');
                                resultElement.textContent = `上传完成\n成功：${successCount}张\n失败：${failCount}张`;
                                
                                // 显示图片预览
                                const imagePreview = document.getElementById('image-preview');
                                const imageGrid = imagePreview.querySelector('.image-grid');
                                imagePreview.style.display = 'block';
                                
                                // 停止加载动画
                                const spinner = statusDiv.querySelector('.status-spinner'); // 使用 statusDiv 内部的 spinner
                                spinner.style.animation = 'none';
                                if (failCount > 0) {
                                    spinner.style.borderColor = '#ff4444';
                                } else {
                                    spinner.style.borderColor = '#2ecc71';
                                }
                                
                                // 显示分析按钮
                                const statusFooter = statusDiv.querySelector('.status-footer'); // 使用 statusDiv 内部的 footer
                                statusFooter.style.display = 'block';
                                
                                // 绑定分析按钮点击事件
                                const analyzeBtn = document.getElementById('analyze-btn');
                                analyzeBtn.addEventListener('click', () => {
                                    analyzeBtn.disabled = true;
                                    analyzeBtn.textContent = '分析中...';
                                    // 更新当前分析上传的图片列表
                                    currentUploadImages = uploadedImages;
                                    
                                    // 创建图片分析的请求数据
                                    const imageAnalysisData = new FormData();
                                    uploadedImages.forEach((img, idx) => {
                                        // 检查图片URL是否包含域名，如果没有则拼接当前域名
                                        let processedImg = img;
                                        if (!img.startsWith('http') && !img.startsWith('https') && !img.startsWith('/')) {
                                            processedImg = window.location.origin + '/' + img;
                                        } else if (img.startsWith('http://')) {
                                            processedImg = img.replace('http://', 'https://');
                                        }
                                        imageAnalysisData.append('images[]', processedImg);
                                    });
                                    
                                    // 关闭当前状态框
                                    if (statusDiv.parentNode === document.body) { // 确保 statusDiv 还在 body 中
                                        document.body.removeChild(statusDiv);
                                    }
                                    
                                    // 调用流式分析接口
                                    handleStreamingResponse('/api/qianwen/analyzeImages', imageAnalysisData, 'images');
                                });

                                // 绑定取消按钮事件 (确保在 analyzeBtn 之后，因为 footer 现在才显示)
                                const cancelBtn = document.getElementById('cancel-btn');
                                cancelBtn.addEventListener('click', function() {
                                     if (statusDiv.parentNode === document.body) { // 确保 statusDiv 还在 body 中
                                        document.body.removeChild(statusDiv);
                                    }
                                });
                                return;
                            }
                            
                            const file = filesArray[index];
                            const singleFormData = new FormData();
                            singleFormData.append('file', file);
                            
                            // 上传单个图片
                            fetch('/api/upload/image', {
                                method: 'POST',
                                body: singleFormData
                            }).then(response => response.json())
                            .then(data => {
                                if (data.code === 10066) {
                                    successCount++;
                                    uploadedImages.push(data.data.pic_path);
                                    // 添加图片预览
                                    const imageGrid = statusDiv.querySelector('.image-grid'); // 使用 statusDiv 内部的 imageGrid
                                    const img = document.createElement('img');
                                    // 检查图片URL是否包含http或https协议头
                                    let picPath = data.data.pic_path;
                                    if (!picPath.startsWith('http') && !picPath.startsWith('https') && !picPath.startsWith('/')) {
                                        picPath = '/' + picPath;
                                    }
                                    img.src = picPath;
                                    img.alt = file.name;
                                    img.title = file.name;
                                    imageGrid.appendChild(img);
                                } else {
                                    failCount++;
                                    console.error('上传失败:', data.message);
                                }
                                
                                // 更新结果文本
                                const resultElement = document.getElementById('analysis-result');
                                resultElement.textContent = `上传中: ${index + 1}/${filesArray.length}\n成功: ${successCount}\n失败: ${failCount}`;
                                
                                // 继续上传下一张
                                uploadNext(index + 1);
                            }).catch(error => {
                                console.error('上传请求失败:', error);
                                failCount++;
                                
                                // 更新结果文本
                                const resultElement = document.getElementById('analysis-result');
                                resultElement.textContent = `上传中: ${index + 1}/${filesArray.length}\n成功: ${successCount}\n失败: ${failCount}\n错误: ${error.message}`;
                                
                                // 继续上传下一张
                                uploadNext(index + 1);
                            });
                        };
                        
                        // 开始上传第一张图片
                        uploadNext(0);
                    }
                });
                
                // 将fileInput添加到DOM，触发点击，然后移除
                document.body.appendChild(fileInput);
                fileInput.click();
                // 使用setTimeout延迟移除，确保文件选择对话框能正常弹出
                setTimeout(() => {
                    // 检查元素是否存在再移除，增加健壮性
                    if (fileInput && fileInput.parentElement === document.body) {
                        document.body.removeChild(fileInput);
                    }
                }, 0);
            });
        });
    }
    
    // 处理流式响应的通用函数
    function handleStreamingResponse(url, formData, type, existingModal = null) {
        // 发起的请求固定携带当前用户id
        formData.append('member_id', member_id);
        // 如果没有提供现有模态框，则创建一个新的
        let streamModal;
        let resultElement;
        let isStreamComplete = false;
        let streamContent = '';
        
        if (!existingModal) {
            // 创建流式响应模态框
            streamModal = document.createElement('div');
            streamModal.className = 'stream-modal';
            streamModal.innerHTML = `
                <div class="stream-container">
                    <div class="stream-header">
                        <h3>${type === 'pdf' ? 'PDF分析结果' : (type === 'prescription' ? 'AI处方生成' : (type === 'report' ? '检测报告' : '图片分析结果'))}</h3>
                        <button class="close-btn">&times;</button>
                    </div>
                    <div class="stream-content">
                        <div class="stream-content-left">
                            <div id="stream-result" class="stream-result markdown-body"></div>
                        </div>
                        <div class="stream-content-right">
                            <div class="file-links-container">
                                <h4>相关文件</h4>
                                <div class="file-links" id="file-links"></div>
                            </div>
                            <button id="edit-btn" class="edit-btn" disabled>编辑内容</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加模态框样式
            const style = document.createElement('style');
            style.textContent = `
                .stream-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.7);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                }
                .stream-container {
                    background: white;
                    border-radius: 12px;
                    width: 95%;
                    max-width: 1200px;
                    height: 85vh;
                    display: flex;
                    flex-direction: column;
                    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
                    overflow: hidden;
                }
                .stream-header {
                    padding: 18px 24px;
                    background: linear-gradient(135deg, #4a6bdf, #6c7ae0);
                    border-bottom: 1px solid #e0e6f5;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .stream-header h3 {
                    margin: 0;
                    color: white;
                    font-size: 20px;
                    font-weight: 600;
                }
                .stream-content {
                    display: flex;
                    flex: 1;
                    overflow: hidden;
                    height: calc(85vh - 60px);
                }
                .stream-content-left {
                    flex: 7;
                    padding: 24px;
                    overflow-y: auto;
                    border-right: 1px solid #e0e6f5;
                }
                .stream-content-right {
                    flex: 3;
                    padding: 24px;
                    display: flex;
                    flex-direction: column;
                    background-color: #f8f9fd;
                    overflow-y: auto;
                }
                .file-links-container {
                    margin-bottom: 20px;
                }
                .file-links-container h4 {
                    color: #4a6bdf;
                    margin-top: 0;
                    margin-bottom: 15px;
                    font-size: 16px;
                    font-weight: 600;
                }
                .file-links {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .file-link {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background: white;
                    border-radius: 6px;
                    border: 1px solid #e0e6f5;
                    text-decoration: none;
                    color: #333;
                    transition: all 0.2s ease;
                }
                .file-link:hover {
                    background: #f0f4ff;
                    border-color: #4a6bdf;
                }
                .file-link i {
                    margin-right: 8px;
                    color: #4a6bdf;
                }
                .edit-btn {
                    padding: 12px 20px;
                    background-color: #4a6bdf;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    margin-top: auto;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    box-shadow: 0 3px 8px rgba(74, 107, 223, 0.2);
                }
                .edit-btn:disabled {
                    background-color: #b0b0b0;
                    cursor: not-allowed;
                    box-shadow: none;
                }
                .edit-btn:not(:disabled):hover {
                    background-color: #3857b8;
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(74, 107, 223, 0.3);
                }
                .edit-btn:not(:disabled):active {
                    transform: translateY(0);
                }
                /* Markdown样式 */
                .markdown-body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    word-break: break-word;
                }
                .markdown-body p {
                    margin-bottom: 16px;
                }
                .markdown-body h1 {
                    font-size: 28px;
                    border-bottom: 1px solid #eaecef;
                    padding-bottom: 0.3em;
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                    color: #24292e;
                }
                .markdown-body h2 {
                    font-size: 24px;
                    border-bottom: 1px solid #eaecef;
                    padding-bottom: 0.3em;
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                    color: #24292e;
                }
                .markdown-body h3 {
                    font-size: 20px;
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                    color: #24292e;
                }
                .markdown-body h4 {
                    font-size: 16px;
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                    color: #24292e;
                }
                .markdown-body ul, .markdown-body ol {
                    padding-left: 2em;
                    margin-bottom: 16px;
                }
                .markdown-body li {
                    margin-bottom: 8px;
                }
                .markdown-body li + li {
                    margin-top: 0.25em;
                }
                .markdown-body code {
                    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
                    padding: 0.2em 0.4em;
                    margin: 0;
                    font-size: 85%;
                    background-color: rgba(27, 31, 35, 0.05);
                    border-radius: 3px;
                }
                .markdown-body pre {
                    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
                    word-wrap: normal;
                    padding: 16px;
                    overflow: auto;
                    font-size: 85%;
                    line-height: 1.45;
                    background-color: #f6f8fa;
                    border-radius: 6px;
                    margin-bottom: 16px;
                }
                .markdown-body pre code {
                    background-color: transparent;
                    padding: 0;
                    margin: 0;
                    font-size: 100%;
                    word-break: normal;
                    white-space: pre;
                    overflow: visible;
                    line-height: inherit;
                }
                .markdown-body blockquote {
                    padding: 0 1em;
                    color: #6a737d;
                    border-left: 0.25em solid #dfe2e5;
                    margin: 0 0 16px 0;
                }
                .markdown-body table {
                    border-collapse: collapse;
                    width: 100%;
                    margin-bottom: 16px;
                    display: block;
                    overflow: auto;
                }
                .markdown-body table th, .markdown-body table td {
                    padding: 6px 13px;
                    border: 1px solid #dfe2e5;
                }
                .markdown-body table tr {
                    background-color: #fff;
                    border-top: 1px solid #c6cbd1;
                }
                .markdown-body table tr:nth-child(2n) {
                    background-color: #f6f8fa;
                }
                .markdown-body img {
                    max-width: 100%;
                    box-sizing: content-box;
                    background-color: #fff;
                }
                .markdown-body hr {
                    height: 0.25em;
                    padding: 0;
                    margin: 24px 0;
                    background-color: #e1e4e8;
                    border: 0;
                }
                /* 编辑模式样式 */
                .edit-mode .stream-content-left {
                    display: none;
                }
                .edit-mode .stream-content-right {
                    flex: 1;
                    padding: 0;
                }
                .edit-container {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                }
                .edit-textarea {
                    flex: 1;
                    padding: 16px;
                    border: none;
                    resize: none;
                    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
                    font-size: 14px;
                    line-height: 1.6;
                    outline: none;
                }
                .edit-actions {
                    display: flex;
                    justify-content: flex-end;
                    padding: 12px;
                    background-color: #f0f4ff;
                    gap: 10px;
                }
                .edit-actions button {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .save-btn {
                    background-color: #4a6bdf;
                    color: white;
                }
                .save-btn:hover {
                    background-color: #3857b8;
                }
                .cancel-btn {
                    background-color: #f0f0f0;
                    color: #333;
                }
                .cancel-btn:hover {
                    background-color: #e0e0e0;
                }
                .typing-indicator {
                    display: inline-block;
                    margin-left: 5px;
                }
                .typing-indicator span {
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    background-color: #4a6bdf;
                    border-radius: 50%;
                    margin: 0 2px;
                    animation: typing 1.4s infinite both;
                }
                .typing-indicator span:nth-child(2) {
                    animation-delay: 0.2s;
                }
                .typing-indicator span:nth-child(3) {
                    animation-delay: 0.4s;
                }
                @keyframes typing {
                    0% { transform: translateY(0); }
                    50% { transform: translateY(-5px); }
                    100% { transform: translateY(0); }
                }
                .close-btn {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: white;
                    transition: color 0.3s;
                }
                .close-btn:hover {
                    color: #f0f0f0;
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(streamModal);
            
            // 绑定关闭按钮事件
            streamModal.querySelector('.close-btn').addEventListener('click', () => {
                document.body.removeChild(streamModal);
                document.head.removeChild(style);
            });
            
            // 点击模态框外部关闭
            // streamModal.addEventListener('click', (e) => {
            //     if (e.target === streamModal) {
            //         document.body.removeChild(streamModal);
            //         document.head.removeChild(style);
            //     }
            // });
            
            resultElement = streamModal.querySelector('#stream-result');
        } else {
            // 使用现有模态框
            streamModal = existingModal;
            resultElement = document.getElementById('analysis-result');
        }
        
        // 添加打字指示器
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = '<span></span><span></span><span></span>';
        resultElement.innerHTML = '';
        resultElement.appendChild(typingIndicator);
        
        // 添加文件链接（示例链接，实际应根据类型动态生成）
        const fileLinksContainer = streamModal.querySelector('#file-links');
        if (fileLinksContainer) {
            // 根据不同类型添加不同的文件链接
            if (type === 'pdf') {
                fileLinksContainer.innerHTML = `
                    <a href="#" class="file-link">
                        <i class="fas fa-file-pdf"></i>
                        <span>检测报告.pdf</span>
                    </a>
                `;
            } else if (type === 'images') {
                fileLinksContainer.innerHTML = `
                    <div class="thumbnail-container">
                        <h4>上传的图片集</h4>
                        <div class="thumbnails">
                            ${currentUploadImages.map(img => `
                                <div class="thumbnail-item" onclick="previewImage('${img}')">
                                    <img src="${img}" alt="缩略图" />
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (type === 'prescription') {
                fileLinksContainer.innerHTML = `
                    <a href="#" class="file-link">
                        <i class="fas fa-file-medical"></i>
                        <span>AI处方数据</span>
                    </a>
                `;
            } else if (type === 'report') {
                fileLinksContainer.innerHTML = `
                    <a href="#" class="file-link">
                        <i class="fas fa-clipboard-list"></i>
                        <span>检测报告数据</span>
                    </a>
                `;
            }
        }
        
        // 获取编辑按钮并设置事件
        const editBtn = streamModal.querySelector('#edit-btn');
        if (editBtn) {
            editBtn.addEventListener('click', function() {
                // 创建编辑界面
                const streamContentRight = this.closest('.stream-content-right');
                const streamContainer = streamModal.querySelector('.stream-container');

                // 保存当前内容用于编辑
                currentContent = streamContent;

                createEditorModal(streamContentRight, streamContainer, fileLinksContainer, streamModal, resultElement);
            });
        }
        
        // 发送请求并处理流式响应
        fetch(url, {
            method: 'POST',
            body: formData
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // 获取响应的可读流
            const reader = response.body.getReader();
            let decoder = new TextDecoder();
            let buffer = '';
            
            // 移除打字指示器
            resultElement.removeChild(typingIndicator);
            
            // 处理流式数据
            function processStream() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // 流处理完成，启用编辑按钮
                        isStreamComplete = true;
                        if (editBtn) {
                            editBtn.disabled = false;
                        }
                        return;
                    }
                    
                    // 解码并处理数据块
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    let is_done = 0;
                    lines.forEach(line => {
                        if (line.trim() === '') return;
                        
                        // 移除data:前缀并解析JSON
                        const jsonStr = line.replace(/^data: /, '').trim();
                        if (jsonStr === '[DONE]') return;
                        
                        try {
                            const data = JSON.parse(jsonStr);
                            if (data.content) {
                                // 累加内容用于后续编辑
                                streamContent += data.content;
                                // 更新报告ID
                                if (is_done === 0) {
                                    reportId = data.reportId;
                                    is_done = 1;
                                }
                                
                                // 使用marked.js解析Markdown并显示
                                try {
                                    // 如果marked库已加载，使用它解析Markdown
                                    if (typeof marked !== 'undefined') {
                                        resultElement.innerHTML = marked.parse(streamContent);
                                    } else {
                                        // 否则直接显示内容
                                        resultElement.innerHTML = streamContent;
                                    }
                                } catch (e) {
                                    // 如果解析失败，直接显示内容
                                    resultElement.innerHTML = streamContent;
                                }
                            }
                        } catch (e) {
                            console.warn('JSON解析失败:', e);
                        }
                    });
                    
                    // 自动滚动到底部
                    let streamContentLeft = resultElement.closest('.stream-content-left');
                    if (streamContentLeft) {
                        streamContentLeft.scrollTop = streamContentLeft.scrollHeight;
                    }
                    
                    // 继续处理流
                    return processStream();
                });
            }
            
            return processStream();
        }).catch(error => {
            console.error('流式请求失败:', error);
            resultElement.innerHTML = `<div style="color: #ff4444;">请求失败: ${error.message}</div>`;
            // 即使失败也启用编辑按钮
            isStreamComplete = true;
            if (editBtn) {
                editBtn.disabled = false;
            }
        });
    }
    
    // 查看报告处理函数 (修改为跳转到患者报告列表页)
    function handleViewReport() {
        const urlParams = new URLSearchParams(window.location.search);
        const memberId = urlParams.get('member_id');

        if (!memberId || isNaN(memberId) || parseInt(memberId, 10) <= 0) {
            if (typeof layer !== 'undefined') {
                layer.msg('无法获取有效的会员ID，无法查看报告列表。请检查URL。', {icon: 2, anim: 6, time: 3000});
            } else {
                alert('无法获取有效的会员ID，无法查看报告列表。请检查URL。');
            }
            console.error('从URL获取的member_id无效或缺失:', memberId);
            return;
        }

        var reportListPageUrl = ns.url('shop/JourneySurvey/reportList', { member_id: memberId });
        window.location.href = reportListPageUrl;

        // console.log('会员ID有效 (' + memberId + ')，正在跳转到报告列表页:', reportListPageUrl);
    }
    // AI处方处理函数
    function handleAIPrescription() {
        console.log('获取AI处方');
        // 这里可以添加跳转到AI处方页面或打开AI处方模态框的代码
        // window.location.href = '/ai-prescription';
    }
    
    
    // 添加点击动画的CSS
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .btn-clicked {
                animation: btnPulse 0.3s ease;
            }
            
            @keyframes btnPulse {
                0% { transform: scale(1); }
                50% { transform: scale(0.95); }
                100% { transform: scale(1); }
            }
        `)
        .appendTo('head');
});

$(function() {
    // 加载患者报告时间轴列表
    var memberId = {$member_id}; // 获取会员ID从ThinkPHP模板变量
    var timelineContainer = document.querySelector('.timeline-container'); // 获取时间轴容器元素

    // 校验会员ID是否有效
    if (!memberId || memberId <= 0) {
        if (timelineContainer) { // 确保容器存在
            timelineContainer.innerHTML = '<p>错误：无法获取会员信息，无法加载报告列表。</p>';
        } else {
            console.error('时间轴容器元素未找到！');
        }
        return; // 停止后续JS执行
    }
    
    // 显示加载提示
    timelineContainer.innerHTML = '<p>加载报告列表中...</p>';
    
    // 发起AJAX请求获取报告时间轴数据
    $.ajax({
        url: '/shop/JourneySurvey/getReportTimelineForMember',
        method: 'POST',
        data: { member_id: memberId },
        dataType: 'json',
        success: function(data) {
            timelineContainer.innerHTML = '';
            if (data.code === 1) {
                var reports = data.data; // 获取报告列表数据
                
                // 检查报告列表是否是有效的非空数组
                if (Array.isArray(reports) && reports.length > 0) {
                reports.forEach(function(report, index) {
                    var timelineItemHTML;
                    
                    if (index % 2 === 0) {
                        // 偶数索引：主内容在左侧
                        // <span class="version-number">${report.file_name || '未命名文件'}</span>
                        timelineItemHTML = `
                            <div class="timeline-item">
                                <div class="timeline-content left">
                                    <div class="version-info">
                                        <h3 class="version-title">
                                            <span class="version-number">医疗建议</span>
                                        </h3>
                                        <p class="version-desc">${report.summary_snippet || '暂无摘要'}</p>
                                        ${report.id ? `<p><a href="/shop/JourneySurvey/viewReport?report_id=${report.id}" target="_blank">查看详情</a></p>` : ''}
                                    </div>
                                </div>
                                <div class="timeline-center">
                                    <div class="timeline-line"></div>
                                    <div class="timeline-node"></div>
                                </div>
                                <div class="timeline-content right">
                                    <div class="date-badge">${report.analysis_time || '未知日期'}</div>
                                </div>
                            </div>
                        `;
                    } else {
                        // 奇数索引：主内容在右侧
                        // <span class="version-number">${report.file_name || '未命名文件'}</span>
                        timelineItemHTML = `
                            <div class="timeline-item">
                                <div class="timeline-content left">
                                    <div class="date-badge">${report.analysis_time || '未知日期'}</div>
                                </div>
                                <div class="timeline-center">
                                    <div class="timeline-line"></div>
                                    <div class="timeline-node"></div>
                                </div>
                                <div class="timeline-content right">
                                    <div class="version-info">
                                        <h3 class="version-title">
                                            <span class="version-number">医疗建议</span>
                                        </h3>
                                        <p class="version-desc">${report.summary_snippet || '暂无摘要'}</p>
                                        ${report.id ? `<p><a href="/shop/JourneySurvey/viewReport?report_id=${report.id}" target="_blank">查看详情</a></p>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    // 将生成的HTML添加到时间轴容器
                    timelineContainer.innerHTML += timelineItemHTML;
                });
                    // 后续遍历报告列表并生成HTML的逻辑将在这里添加
                } else {
                    // 处理报告列表为空的情况
                    timelineContainer.innerHTML = '<p>暂无已分析的报告</p>';
                }
            } else {
                var errorMsg = data.msg || '加载报告列表失败: 未知错误';
                timelineContainer.innerHTML = '<p>' + errorMsg + '</p>';
            }
        },
        error: function(xhr, status, error) {
            // 清空加载提示或已有的内容
            timelineContainer.innerHTML = '';

            // 打印详细错误信息到控制台，用于调试
            console.error('获取报告列表 AJAX 请求失败:', status, error);
            console.error('XHR 对象:', xhr); // 打印XHR对象可能包含更多信息

            // 在页面显示用户友好的错误提示
            timelineContainer.innerHTML = '<p>加载报告列表失败，请稍后再试。</p>';
        }
    });

    // 确保时间轴线连续
    function adjustTimelineLines() {
        $('.timeline-line').each(function(index) {
            // 第一个元素顶部不需要延伸
            if (index === 0) {
                $(this).css('top', '0');
            }
            
            // 最后一个元素底部不需要延伸
            if (index === $('.timeline-line').length - 1) {
                $(this).css('bottom', '0');
            }
        });
    }
    
    // 初始化
    adjustTimelineLines();
    
    // 窗口大小改变时重新调整
    $(window).resize(function() {
        adjustTimelineLines();
    });
});
</script>
{/block}