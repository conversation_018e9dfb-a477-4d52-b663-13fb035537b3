{extend name="base"/}
{block name="resources"}
<style>
	.layui-form-item .layui-form-checkbox[lay-skin=primary] {
	    margin-top: 0;
	}
	.ns-text-color-red:hover {
		color: red;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>规定注册是可选择的类型，注册用户名、密码的规则</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label ">是否允许注册：</label>
		<div class="layui-input-block" id="isReg">
			<input type="checkbox" name="is_enable" value="1" lay-filter="is_enable" lay-skin="switch" {if condition="$value.is_enable == 1"} checked {/if} >
		</div>
		<div class="ns-word-aux">设置为关闭，则无法注册会员账号</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label ">是否开启动态码登录：</label>
		<div class="layui-input-block" id="dynamic_code_login">
			<input type="checkbox" name="dynamic_code_login" value="1" lay-filter="dynamic_code_login" lay-skin="switch" {if condition="isset($value.dynamic_code_login) && $value.dynamic_code_login == 1"} checked {/if} >
		</div>
		<div class="ns-word-aux">设置为关闭，则关闭手机动态码登录功能</div>
		<div class="ns-word-aux">若启用需配置好“动态码登录”短信模板，<a href="{:addon_url('shop/message/lists')}" class="ns-text-color" target="_blank">前去配置</a></div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label ">用户保留关键字：</label>
		<div class="layui-input-block">
			<textarea name="keyword" autocomplete="off" class="layui-textarea  ns-len-long">{$value.keyword}</textarea>
		</div>
		<div class="ns-word-aux">用户在注册用户名不可使用这些关键字。多个关键字之间以英文","分隔开，如"shop,username"</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label ">密码最小长度：</label>
		<div class="layui-input-block">
			<input type="number" min="0" name="pwd_len" class="layui-input ns-len-short" lay-verify="pwd_lens" value="{$value.pwd_len}">
		</div>
		<div class="ns-word-aux">新用户注册时密码最小长度，0或不填为不限制</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label ">密码复杂程度设置：</label>
		<div class="layui-input-block" id="pwd_complexity">
			<input type="checkbox" name="pwd_complexity" value="number" title="数字" lay-skin="primary" {if condition="!empty($value) && in_array('number', $value['pwd_complexity_arr'])"}checked{/if}>
			<input type="checkbox" name="pwd_complexity" value="letter" title="小写字母" lay-skin="primary" {if condition="!empty($value) && in_array('letter', $value['pwd_complexity_arr'])"}checked{/if}>
			<input type="checkbox" name="pwd_complexity" value="upper_case" title="大写字母" lay-skin="primary" {if condition="!empty($value) && in_array('upper_case', $value['pwd_complexity_arr'])"}checked{/if}>
			<input type="checkbox" name="pwd_complexity" value="symbol" title="符号" lay-skin="primary" {if condition="!empty($value) && in_array('symbol', $value['pwd_complexity_arr'])"}checked{/if}>
		</div>
		<div class="ns-word-aux">设置密码复杂度</div>
	</div>
	
	<div class="ns-form-row">
		<button type="button" class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false; //防重复
		form.render();

		form.on('submit(save)', function(data) {
			if (data.field.is_enable == undefined) {
				data.field.is_enable = 0;
			}
			if (data.field.dynamic_code_login == undefined){
				data.field.dynamic_code_login = 0;
			}
			
			var pwdComplexityObj = $("#pwd_complexity input:checked");
			
			var pwd_complexity_array = [];
			for (var i = 0; i < pwdComplexityObj.length; i++) {
				pwd_complexity_array.push(pwdComplexityObj.eq(i).val());
			}

			// var typeObj = $("#type input:checked");
			// var type_array = [];
			// for (var i = 0; i < typeObj.length; i++) {
			// 	type_array.push(typeObj.eq(i).val());
			// }
			
			data.field.pwd_complexity = pwd_complexity_array.toString();
			data.field.type = '';
			
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("shop/member/regConfig"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						location.reload();
					}
				}
			});
		});
		
		/**
		 * 表单验证
		 */	
		form.verify({
			pwd_lens: function(value, item){ //value：表单的值、item：表单的DOM对象
				if(!new RegExp("^[0-9]*[1-9][0-9]*$").test(value)){
					return '密码长度只能是正整数！';
				}
			}
		});    
		
	});
</script>
{/block}