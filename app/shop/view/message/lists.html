{extend name="base"/}
{block name="resources"}
<style type="text/css">
	.layui-field-box {
		display: flex;
		flex-wrap: wrap;
	}

	/* 一行4个卡片时的排布 */
	@media screen and (min-width: 1670px) {
		.block-list {
			width: 24%;
			margin-right: 1.3%;
			margin-bottom: 25px;
		}

		.block-list:nth-child(4n) {
			margin-right: 0;
		}
	}

	/* 一行3个卡片时的排布 */
	@media screen and (max-width: 1669px) {
		.block-list {
			width: 32%;
			margin-right: 2%;
			margin-bottom: 25px;
		}

		.block-list:nth-child(3n) {
			margin-right: 0;
		}
	}

	.block-list {
		box-sizing: border-box;
		border: 1px solid #F1F1F1;
	}

	.block-list .block-title {
		height: 52px;
		line-height: 52px;
		font-size: 14px;
		color: #333;
		background-color: #F7F8FA;
		padding-left: 25px;
	}

	.block-list .block-content {
		padding: 20px 25px 0;
		display: flex;
		flex-wrap: wrap;
	}

	.block-list .block-content a {
		height: 25px;
		line-height: 25px;
		width: 33.3%;
		margin-bottom: 20px;
		color: #454545;
		font-size: 12px;
		display: flex;
		align-items: center;
		white-space: nowrap;
	}

	.block-list .block-content a i {
		display: inline-block;
		margin-right: 2px;
		color: #999;
		font-weight: 600;
	}
</style>
{/block}
{block name="main"}
<!-- 买家消息 -->
<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">买家消息</span>
	</div>
	<div class="layui-card-body layui-field-box">
		{foreach $member_message_list as $member_message_k => $member_message_v}
		<div class="block-list">
			<div class="block-title">
				{$member_message_v.title}
			</div>
			<div class="block-content">
				{if in_array('sms',$member_message_v['support_type'])}
				<a href="{:addon_url('shop/message/editSmsMessage',['keywords' => $member_message_v.keywords])}">
					<i class="iconfont iconseleted {if $member_message_v.sms_is_open == 1}ns-text-color{/if}"></i>短信
				</a>
				{/if}

				{if in_array('wechat',$member_message_v['support_type'])}
				<a href="{:addon_url('wechat://shop/message/edit',['keywords' => $member_message_v.keywords])}">
					<i class="iconfont iconseleted {if $member_message_v.wechat_is_open == 1}ns-text-color{/if}"></i>微信公众号
				</a>
				{/if}
			</div>
		</div>
		{/foreach}
	</div>
</div>

<!-- 卖家通知 -->
<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">卖家通知</span>
	</div>
	<div class="layui-card-body layui-field-box">
		{foreach $shop_message_list as $shop_message_k => $shop_message_v}
		<div class="block-list">
			<div class="block-title">
				{$shop_message_v.title}
			</div>
			<div class="block-content">
				{if in_array('sms',$shop_message_v['support_type'])}
				<a href="{:addon_url('shop/message/editSmsMessage',['keywords' => $shop_message_v.keywords])}">
					<i class="iconfont iconseleted {if $shop_message_v.sms_is_open == 1}ns-text-color{/if}"></i>短信
				</a>
				{/if}
				{if in_array('wechat',$shop_message_v['support_type'])}
				<a href="{:addon_url('wechat://shop/message/edit',['keywords' => $shop_message_v.keywords])}">
					<i class="iconfont iconseleted {if $shop_message_v.wechat_is_open == 1}ns-text-color{/if}"></i>微信公众号
				</a>
				{/if}
			</div>
		</div>
		{/foreach}
	</div>
</div>
{/block}
{block name="script"}
{/block}
