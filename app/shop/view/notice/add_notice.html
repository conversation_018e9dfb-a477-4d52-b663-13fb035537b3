{extend name="base"/}
{block name="resources"}
<style>
	.ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>公告标题：</label>
		<div class="layui-input-block">
			<input type="text" name="title" lay-verify="required" placeholder="请输入公告标题" class="layui-input ns-len-long">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">是否循环：</label>
		<div class="layui-input-block">
			<input id="is_back" type="checkbox" name="is_back" lay-skin="switch" value="0" lay-filter="isBack">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">是否置顶：</label>
		<div class="layui-input-block">
			<input id="is_top" type="checkbox" name="is_top" lay-skin="switch" value="0" lay-filter="isTop">
		</div>
	</div>
	<!--<div class="layui-form-item">-->
		<!--<label class="layui-form-label">接收范围：</label>-->
		<!--<div class="layui-input-block">-->
			<!--<input type="checkbox" name="receiving_type[]" value="mobile" lay-skin="primary" title="手机端" >-->
		<!--</div>-->
	<!--</div>-->
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>公告内容：</label>
		<div class="layui-input-block ns-special-length">
			<script id="container" name="content" type="text/plain" style="width: 800px; height: 300px;"></script>
		</div>
	</div>
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
	//实例化富文本
	var ue = UE.getEditor('container');
	layui.use('form', function() {
		var form = layui.form;
		var repeat_flag = false; //防重复标识
		form.render();

		form.on('switch(isTop)', function(data) {
			if (data.elem.checked) {
				$("#is_top").val(1);
			} else {
				$("#is_top").val(0);
			}
		});

		form.on('switch(isBack)', function(data) {
			if (data.elem.checked) {
				$("#is_back").val(1);
			} else {
				$("#is_back").val(0);
			}
		});

		form.on('submit(save)', function(data) {
			var html;
			ue.ready(function() {   //对编辑器的操作最好在编辑器ready之后再做
				html = ue.getContent();   //获取html内容，返回: <p>hello</p>
			});
			data.field.content = html;
			if (data.field.content == "") {
				layer.msg('请输入公告内容', {
					icon: 5
				});
				return;
			}
			
			data.field.is_top = $("#is_top").val();
			
			if (repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/notice/addNotice"),
				data: data.field,
				type: "POST",
				dataType: "JSON",
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("shop/notice/index")
							},
							btn2: function() {
								location.href = ns.url("shop/notice/addNotice")
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
	});
	
	function back() {
		location.href = ns.url("shop/notice/index");
	}
</script>
{/block}