{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<!-- 列表 -->
<table id="help_list" lay-filter="help_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="basic">查看</a>
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form'], function() {
		var table,
			form = layui.form;
		form.render();

		table = new Table({
			elem: '#help_list',
			url: ns.url("shop/notice/lists"),
			cols: [
				[{
					field: 'title',
					title: '公告标题',
					unresize: 'false'
				}, {
					field: 'create_time',
					title: '发布时间',
					unresize: 'false',
					templet: function (data) {
						return ns.time_to_date(data.create_time);
					}
				}, {
					title: '操作',
					width: '15%',
					toolbar: '#operation',
					unresize: 'false'
				}]
			],
		});
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'basic': //编辑
					location.href = ns.url("shop/notice/detail?id=" + data.id);
					break;
			}
		});
	});
</script>
{/block}