{extend name="base" /}
{block name="resources"/}
<link rel="stylesheet" href="SHOP_CSS/order_detail.css"/>
<link rel="stylesheet" href="SHOP_CSS/package.css"/>
<link rel="stylesheet" href="SHOP_CSS/agreement-modal.css"/>
<script type="text/javascript">
    // 用户协议弹窗模块
    let agreementAccepted = false;
    let overlayElement = null; // 全局存储遮罩层引用

    // 显示用户协议弹窗
    function showAgreementModal(onAgree) {
        // 创建弹窗容器
        const modalOverlay = document.createElement('div');
        modalOverlay.classList.add('agreement-modal-overlay');
        modalOverlay.style.position = 'fixed';
        modalOverlay.style.top = '0';
        modalOverlay.style.left = '0';
        modalOverlay.style.width = '100%';
        modalOverlay.style.height = '100%';
        modalOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
        modalOverlay.style.zIndex = '10000';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.justifyContent = 'center';
        modalOverlay.style.alignItems = 'center';

        const modalContainer = document.createElement('div');
        modalContainer.classList.add('agreement-modal');

        // 使用唯一ID避免冲突
        const buttonId = 'agreeButton_' + Date.now();

        // 创建弹窗内容 - 注意这里使用反引号 ` 而不是单引号 '
        modalContainer.innerHTML = `
        <div class="agreement-avatar">
            <img src="SHOP_IMG/aii_image.png" alt="医生头像">
        </div>
        <h2 class="agreement-title">小悦</h2>
        <div class="agreement-content">
            <p>我是AI功能医学方案助手，并非真人医生，回答均由 AI生成，仅供参考，不构成诊断和处方，如有不适请尽快就医。详情请查阅<a href="/shop/notice/detail.html?id=16" target="_blank">《AI方案助手服务协议》</a>，继续使用代表您同意前述协议。</p>
        </div>
        <button id="${buttonId}" class="agree-button">同意并开始对话</button>
        `;

        // 添加弹窗到页面
        modalOverlay.appendChild(modalContainer);
        document.body.appendChild(modalOverlay);

        // 定义同意按钮的处理函数
        const handleAgree = function() {
            agreementAccepted = true;

            // 添加动画效果，优化用户体验
            modalOverlay.style.opacity = '0';
            modalOverlay.style.transition = 'opacity 0.3s ease';

            setTimeout(() => {
                // 确保元素仍然存在再尝试移除
                if (document.body.contains(modalOverlay)) {
                    document.body.removeChild(modalOverlay);
                }

                // 移除iframe遮罩层
                removeOverlay();

                // 确保没有残留的样式影响
                document.body.style.overflow = 'auto';
                document.body.style.pointerEvents = 'auto';

                if (typeof onAgree === 'function') {
                    onAgree();
                }
            }, 300);
        };

        // 使用setTimeout确保DOM已经渲染完成
        setTimeout(() => {
            // 使用事件委托，绑定到父元素
            modalContainer.addEventListener('click', function(event) {
                if (event.target && event.target.id === buttonId) {
                    handleAgree();
                }
            });

            // 添加备用点击处理，增加点击成功率
            const agreeButton = document.getElementById(buttonId);
            if (agreeButton) {
                agreeButton.addEventListener('click', handleAgree);
            }
        }, 100);
    }

    // 为iframe添加外部遮罩层
    function addOverlay() {
        const iframeContainer = document.querySelector('iframe').parentElement;
        if (!iframeContainer) return;

        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.classList.add('iframe-overlay');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(255,255,255,0.01)'; // 几乎透明
        overlay.style.zIndex = '1000';
        overlay.style.cursor = 'not-allowed';

        // 确保容器可以定位
        if (getComputedStyle(iframeContainer).position === 'static') {
            iframeContainer.style.position = 'relative';
        }

        // 添加遮罩层到iframe的父容器
        iframeContainer.appendChild(overlay);

        // 存储全局引用
        overlayElement = overlay;
    }

    // 移除iframe外部遮罩层
    function removeOverlay() {
        if (overlayElement && overlayElement.parentElement) {
            overlayElement.parentElement.removeChild(overlayElement);
            overlayElement = null;
        }
    }

    // 检查是否同意协议
    function isAgreementAccepted() {
        return agreementAccepted;
    }
</script>
{/block}

{block name="main"}
<iframe src="http://***********/chat?user={$user}"
        width="100%"
        scrolling="auto"
        frameborder="0"
        allowfullscreen>
</iframe>
{/block}

{block name="script"}
<script>
    var show = {$show};

    function FrameWH() {
        var h = $(window).height() - 175;
        $("iframe").css("height", h + "px");
    }

    // 初始化协议弹窗
    if (show == 1) {
        $(function() {
            // 等待iframe加载完成后添加遮罩层
            setTimeout(function() {
                addOverlay();
            }, 500);

            // 添加重试机制
            let attempts = 0;
            const maxAttempts = 3;

            function tryShowAgreement() {
                attempts++;

                try {
                    // 显示协议弹窗（带同意回调）
                    showAgreementModal(function() {
                        // console.log('用户已同意协议');

                        // 确保没有残留的样式影响
                        document.body.style.overflow = 'auto';

                        // 同意后的AJAX调用
                        $.ajax({
                            type: 'POST',
                            dataType: 'JSON',
                            url: ns.url("shop/order/aii"),
                            data: {},
                            success: function(res) {
                                // console.log('协议同意状态已保存');
                            },
                            error: function() {
                                // console.log('协议状态保存失败，但不影响使用');
                            }
                        });
                    });
                } catch (error) {
                    // console.error('显示协议弹窗失败:', error);

                    // 如果失败且未超过最大尝试次数，则重试
                    if (attempts < maxAttempts) {
                        setTimeout(tryShowAgreement, 1000);
                    } else {
                        // 超过最大尝试次数，默认移除遮罩层
                        // console.log('多次尝试显示协议弹窗失败，默认启用功能');
                        removeOverlay();
                    }
                }
            }

            // 开始尝试显示协议
            tryShowAgreement();

            // 添加安全检查，确保页面可交互
            setTimeout(function() {
                if (agreementAccepted) {
                    removeOverlay();
                }
            }, 2000);
        });
    }

    // 在页面加载完成后设置iframe高度
    FrameWH();

    // 在窗口大小改变时重新计算iframe高度
    $(window).resize(function() {
        FrameWH();
    });
</script>
{/block}