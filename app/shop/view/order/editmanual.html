{extend name="base"/}
{block name="resources"}
<style>
    #goods thead th{ background-color: #e6e6e6;}
    /* 优惠商品 */
    .goods-empty { width: 100%; display: flex; justify-content: center; align-items: center; }
</style>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>核对处方收货地址</li>
            <li>认真填写，核对处方详细药品信息</li>
            <li>可保存该处方为模板，方便以后开相同处方操作</li>
        </ul>
    </div>
</div>
<div class="layui-form ns-form" lay-filter="storeform" >
    <div class="ns-tab layui-tab layui-tab-brief" id="add_active">
        <ul class="layui-tab-title">
            <li class="layu1i-this" lay-id="basic">新增处方</li>
            <li lay-id="media">检测报告</li>
            <li lay-id="attr">病历管理</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <div class="layui-card-header ns-card-common">
                    <span class="ns-card-title">基础信息</span>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">会员列表：</label>
                    <div class="layui-input-block ns-len-mid">
                        <select id="member_id" name="memberaddress" lay-search="" lay-filter="memberaddress">
                            <option value="">请选择会员</option>
                            {foreach name="$member_list" item="vo"}
                            <option value="{$vo['member_id']}">{$vo['username']}</option>
                            {/foreach}
                        </select>
                        <input type="hidden" name="memberaddress" />
                    </div>
                    <div class="ns-word-aux">收货信息可以自定义填写，也可以通过选择会员设置</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">处方进程：</label>
                    <div class="layui-input-block">
                        <input id="progress" name="progress" type="text" value="" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，如第1个疗程</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">营养素干预重点：</label>
                    <div class="layui-input-inline">
                        <input id="zhongdian" name="zhongdian" type="text" value="" class="layui-input ns-len-long">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">健康管理师：</label>
                    <div class="layui-input-inline">
                        <input id="jiankangguanli" name="jiankangguanli" type="text" value="" class="layui-input ns-len-long">
                    </div>
                </div>

                {if $order_deverys}
                <div class="layui-form-item">
                    <label class="layui-form-label">常用收货地址：</label>
                    <div class="layui-input-block ns-len-mid">
                        <select id="order_deverys" name="order_deverys" lay-search="" lay-filter="order_deverys">
                            <option value="">请选择常用收货地址</option>
                            {foreach name="$order_deverys" item="v"}
                            <option value="{$v.name}-{$v.mobile}-{$v.address}">{$v.name}-{$v.mobile}-{$v.address}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                {/if}

                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">* </span>收货昵称：</label>
                    <div class="layui-input-inline">
                        {if $guding_member_id}
                        <input name="addressname" type="text" value="" class="layui-input ns-len-long" readonly>
                        {else/}
                        <input name="addressname" type="text" value="" class="layui-input ns-len-long" >
                        {/if}
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">* </span>收货电话：</label>
                    <div class="layui-input-inline">
                        <input name="addressmobile" lay-verify="mobile" type="text"  value="" class="layui-input ns-len-long">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">* </span>收货地址：</label>
                    <div class="layui-input-inline area-select">
                        <select name="province_id" lay-filter="province_id" lay-verify="province_id">
                            {foreach $province_list as $k => $v}
                            <option value="{$v.id}">{$v.name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="layui-input-inline area-select">
                        <select name="city_id"  lay-filter="city_id" lay-verify="city_id">
                            <option value="">请选择城市</option>
                        </select>
                    </div>

                    <div class="layui-input-inline area-select">
                        <select name="district_id"  lay-filter="district_id" lay-verify="district_id">
                            <option value="">请选择区/县</option>
                        </select>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                        <input type="text" name="address"  placeholder="请填写收货具体地址" value="" autocomplete="off" class="layui-input ns-len-long address-content" value="">
                    </div>
                </div>

                <div class="layui-card-header ns-card-common">
                    <span class="ns-card-title">处方详情</span>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">处方模板：</label>
                    <div class="layui-input-inline area-select">
                        <select id="order_muban"  name="order_muban" lay-search="" lay-filter="order_muban" >
                            <option value="1">客户处方模板</option>
                            <option value="2">机构处方模板</option>
                            <option value="3">系统处方模板</option>
                            <option value="4">该客户的既往处方</option>
                        </select>
                    </div>

                    <div class="layui-input-inline area-select order_muban_check">
                        <select id="class_id" name="orderattr" lay-search="" lay-filter="orderattr">
                            <option value="">请选择模板</option>
                            {foreach name="$attr_list" item="vo"}
                            <option value="{$vo['class_id']}">{$vo['class_name']}</option>
                            {/foreach}
                        </select>
                    </div>
                    <input type="hidden" name="orderattr" />
                    <div class="layui-input-inline area-select order_muban1" style="display: none">
                        <option value="">请选择模板</option>
                        {foreach name="$attr_list" item="vo"}
                        <option value="{$vo['class_id']}">{$vo['class_name']}</option>
                        {/foreach}
                    </div>
                    <div class="layui-input-inline area-select order_muban2"  style="display: none">
                        <option value="">请选择模板</option>
                        {foreach name="$attr_list2" item="vo"}
                        <option value="{$vo['class_id']}">{$vo['class_name']}</option>
                        {/foreach}
                    </div>
                    <div class="layui-input-inline area-select order_muban3"  style="display: none">
                        <option value="">请选择模板</option>
                        {foreach name="$attr_list3" item="vo"}
                        <option value="{$vo['class_id']}">{$vo['class_name']}</option>
                        {/foreach}
                    </div>

                    <div class="layui-input-inline area-select order_muban4" style="display: none">
                        <select id="memberhistroy_detail" name="memberhistroy_detail" lay-search="" lay-filter="memberhistroy_detail" >
                            <option value="">请选择既往处方</option>
                        </select>
                    </div>


                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">既往处方详情：</label>
                    <div class="layui-input-block ns-len-mid">
                        <select id="memberhistroy" name="memberhistroy" lay-search="" lay-filter="memberhistroy">
                            <option value="">请选择既往处方</option>
                        </select>
                    </div>
                    <div class="ns-word-aux">可以通过选择既往处方，快速了解用户之前所下的处方</div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">晨起包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="earlymoning_time" name="earlymoning_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如晨起空腹服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">晨起包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">早餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="moning_time" name="moning_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如早餐后10分钟服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">早餐包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_moning" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_moning()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">餐间包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="canjian_time" name="canjian_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如餐间服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">餐间包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_canjian" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_canjian()">添加商品</button>
                    </div>
                </div>


                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">午餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="aftnoon_time" name="aftnoon_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如午餐后10分钟服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">午餐包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_aftnoon" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_aftnoon()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">晚餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="night_time" name="night_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如晚餐后10分钟(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">晚餐包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_night" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_night()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">睡前包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="sleep_time" name="sleep_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如睡前10分钟(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">睡前包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_evening" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_evening()">添加商品</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">出差周转包：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods_teshu" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="20%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods_teshu()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item" hidden>
                    <label class="layui-form-label"><span class="required">* </span>是否需要二次查验：</label>
                    <div class="layui-input-inline">
                        <input type="radio" name="ccheck" value="1" title="查验不封口">
                        <input type="radio" name="ccheck" value="2" title="不查验封口" checked>
                    </div>
                </div>

                <div class="layui-form-item" hidden>
                    <label class="layui-form-label"><span class="required">* </span>是否包含粉剂：</label>
                    <div class="layui-input-inline">
                        <input type="radio" lay-filter="fenji" name="fenji" value="1" title="是">
                        <input type="radio" lay-filter="fenji" name="fenji" value="2" title="否">
                        <input type="radio" lay-filter="c_fenji" name="c_fenji" value="2" title="不分装" checked>
                    </div>
                </div>

                <div id="c_fenji" class="layui-form-item" hidden>
                    <label class="layui-form-label"><span class="required">* </span>粉剂，液体需整瓶购买：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="fenji_goods" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="10%">
                                <col width="10%">
                                <col width="10%">
                                <col width="25%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>数量</th>
                                <th class="operation">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">疗程天数：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline">
                            <input type="number" onchange="editSort(this)" name="day" id="day" autocomplete="off" class="layui-input combined-price ns-len-short" value="1" step="1"  min="1" >
                        </div>
                        <span class="layui-form-mid">天</span>
                    </div>
                    <div class="ns-word-aux">
                        <p>该处方服用疗程天数</p>
                    </div>
                </div>
                <div id="attr_fomatw" style="margin-bottom: 35px;" class="layui-card-header ns-card-common" hidden>
                    <span style="color: #FF6A00;" class="ns-card-title">温馨提示：以下呈现出的维生素、矿物质数值是方案里面含有的所有维生素、矿物质的总含量。依据部分医生提出诉求，便于计算方案中营养成分剂量叠加，以便给客户提供精准的个性化方案，特增加了此项提醒。但此功能仅用于辅助提醒，具体使用请医生自行斟酌。</span>
                </div>

                <div id="attr_fomats" class="layui-form-item" hidden>
                    <label class="layui-form-label">本处方维生素、矿物质累计含量：</label>
                    <div class="layui-input-inline" style="width: 60%;">
                        <table class="layui-table">
                            <colgroup>
                                <col width="50%" />
                                <col width="50%" />
                            </colgroup>
                            <thead>
                            <tr><th>成份名称</th><th>累计含量</th></tr>
                            </thead>
                            <tbody id="attr_fomat" class="ns-attr-new">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">分装订单费：</label>
                    <div class="layui-input-block">
                        <p class="ns-input-text"><span class="service_money">0.00</span>元</p>
                    </div>
                    <div class="ns-word-aux">
                        <p>该处方所需分装订单费用</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">整瓶订单费：</label>
                    <div class="layui-input-block">
                        <p class="ns-input-text"><span class="guding_service_money">0.00</span>元</p>
                    </div>
                    <div class="ns-word-aux">
                        <p>该处方所需整瓶订单费用</p>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">价格：</label>
                    <div class="layui-input-block">
                        <p class="ns-input-text"><span class="original-price">0.00</span>元</p>
                    </div>
                </div>

                <div id="attr" class="layui-form-item" hidden>
                    <label class="layui-form-label">处方模板名称：</label>
                    <div class="layui-input-inline">
                        <input id="orderattr" name="orderattr" type="text" value="" class="layui-input ns-len-long">
                    </div>
                </div>

                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">生成处方订单</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="attr">保存处方模板</button>
                </div>
            </div>

            <!-- 媒体设置 -->
            <div class="layui-tab-item">

                <div class="layui-card ns-card-common js-goods-image-wrap">
                    <div class="layui-card-header">
                        <span class="ns-card-title">检测报告上传</span>
                    </div>

                    <div class="layui-card-body">
                        <div class="layui-form-item goods-image-wrap">
                            <label class="layui-form-label">图片上传：</label>
                            <div class="layui-input-block">
                                <!--商品主图项-->
                                <div class="js-goods-image"></div>
                                <button class="layui-btn layui-btn-primary layui-btn-sm js-add-goods-image" type="button">上传图片</button>
                            </div>
                            <div class="ns-word-aux">支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。</div>
                        </div>
                    </div>
                </div>

                <div class="layui-card ns-card-common">
                    <div class="layui-card-header">
                        <span class="ns-card-title">病理视频</span>
                    </div>

                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">视频上传：</label>
                            <div class="layui-input-block">
                                <div class="video-thumb">
                                    <video id="goods_video" class="video-js vjs-big-play-centered" controls="" poster="SHOP_IMG/goods_video_preview.png" preload="auto"></video>
                                    <span class="delete-video hide" onclick="deleteVideo()"></span>
                                </div>
                                <div id="videoUpload" title="视频上传" style="position: absolute;left: 0;width: 290px;height: 135px;opacity: 0;cursor: pointer;z-index:10;"></div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">视频地址：</label>
                            <div class="layui-input-block">
                                <input type="text" name="video_url" placeholder="在此输入外链视频地址" autocomplete="off" class="layui-input ns-len-long">
                            </div>
                            <div class="file-title ns-word-aux">
                                <div>注意事项：</div>
                                <ul>
                                    <li>1、检查upload文件夹是否有读写权限。</li>
                                    <li>2、PHP默认上传限制为2MB，需要在php.ini配置文件中修改“post_max_size”和“upload_max_filesize”的大小。</li>
                                    <li>3、视频支持手动输入外链视频地址或者上传本地视频文件</li>
                                    <li>4、必须上传.mp4视频格式</li>
                                    <li>5、视频文件大小不能超过500MB</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="report">更新检查报告</button>
                </div>
            </div>

            <!-- 属性设置 -->
            <div class="layui-tab-item">
                <div class="ns-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">温馨提示：</label>
                        <div class="layui-input-inline ns-special-length" style="color: #ff8143;font-size: 12px">
                            格式参考：2020/02/02:   xx不适
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">病历详情：</label>
                        <div class="layui-input-inline ns-special-length">
                            <script id="editor" type="text/plain" style="width:100%;height:500px;"></script>
                        </div>
                    </div>
                </div>
                <script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
                <script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"> </script>
                <script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>
                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="updatel">更新病历</button>
                </div>
            </div>

        </div>
    </div>
    <input type="hidden" name="goods_image" value="" />
</div>
<!--商品主图列表-->
<script type="text/html" id="goodsImage">
    {{# if(d.length){ }}
    {{# for(var i=0;i<d.length;i++){ }}
    {{# if(d[i]){ }}
    <div class="item" data-index="{{i}}">
        <div class="img-wrap">
            <img src="{{ns.img(d[i])}}" layer-src>
        </div>
        <div class="operation">
            <i title="图片预览" class="iconfont iconreview js-preview"></i>
            <i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
        </div>
        {{# }else{ }}
        <div class="item empty">
            {{# } }}
        </div>
        {{# } }}
        {{# }else{ }}
        <div class="item empty"></div>
        {{# } }}
</script>
{/block}
{block name="script"}
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script>
    var form, selectGoodsSkuId = [],selectGoodsSkuId_aftnoon = [],selectGoodsSkuId_canjian = [],selectGoodsSkuId_night = [],selectGoodsSkuId_evening = [],selectGoodsSkuId_moning = [],selectGoodsSkuId_teshu = [];
    var stepTab  = false;
    var goodsImage = [];//商品主图
    var laytpl= false;
    const GOODS_IMAGE_MAX = 10;//商品主图数量
    var goodsContent;//商品详情
    var member_id = {$order_detail['member_id']};
    var guding_member_id = {$guding_member_id};
    var order_id = {$order_detail['order_id']};
    //获取hash来切换选项卡
    stepTab = location.hash.replace(/^#tab=/, '');
    goodsContent = UE.getEditor('editor');
    layui.use(['element','laytpl','form', 'laydate','upload'], function() {
        form = layui.form;
        var element = layui.element,
            laydate = layui.laydate,
            upload = layui.upload,
            repeat_flag = false; //防重复标识

        laytpl = layui.laytpl;
        var repeat_flag = false; //防重复标识

        stepTab = 'basic';

        element.tabChange('storeform', stepTab);

        // 加载商品详情
        goodsContent.ready(function () {
            goodsContent.setContent('');
        });
        //渲染商品主图列表
        refreshGoodsImage();

        //监听Tab切换，以改变地址hash值
        element.on('tab(storeform)', function () {
            location.hash = 'tab=' + this.getAttribute('lay-id');
            stepTab = this.getAttribute('lay-id');
        });

        laydate.render({
            elem: '#laydate'
        });

        //添加商品主图
        $(".js-add-goods-image").click(function () {
            openAlbum(function (data) {
                for (var i = 0; i < data.length; i++) {
                    if (goodsImage.length < GOODS_IMAGE_MAX) goodsImage.push(data[i].pic_path);
                }
                refreshGoodsImage();
            }, GOODS_IMAGE_MAX);
        });

        //视频上传
        var videoUpload = upload.render({
            elem: '#videoUpload',
            url: ns.url("shop/upload/video"),
            accept: "video",
            done: function (res) {
                if (res.code >= 0) {
                    $("input[name='video_url']").val(res.data.path);
                    $(".delete-video").show();
                    var video = "goods_video";
                    var myPlayer = videojs(video);
                    var path = ns.img(res.data.path);

                    videojs(video).ready(function () {
                        var myPlayer = this;
                        myPlayer.src(path);
                        myPlayer.load(path);
                        myPlayer.play();
                    });

                }
                layer.msg(res.message);
            }
        });

        /**
         * 表单验证
         */
        form.verify({
            mobile: function(value) {
                var reg = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[1589])\d{8}$/;
                if (value == '') {
                    return;
                }
            },
            isemail: function(value) {
                var reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
                if (value == '') {
                    return;
                }
                if (!reg.test(value)) {
                    return '请输入正确的邮箱!';
                }
            }
        });

        /**
         * 监听提交
         */
        form.on('submit(save)', function(data) {
//粉剂分装
            var ccheck = $("input[name='ccheck']:checked").val();
            var fenji = $("input[name='fenji']:checked").val();
            var c_fenji = $("input[name='c_fenji']:checked").val();

            if (!ccheck){
                return layer.confirm('请选择是否二次查验');
            }

            if (!c_fenji && fenji==1){
                return layer.confirm('请选择粉剂是否分装');
            }
            var service_money = $(".service_money").text();
            var sku_ids = selectGoodsSkuId.toString();
            var sku_ids_aftnoon = selectGoodsSkuId_aftnoon.toString();
            var sku_ids_canjian = selectGoodsSkuId_canjian.toString();
            var sku_ids_night = selectGoodsSkuId_night.toString();
            var sku_ids_evening = selectGoodsSkuId_evening.toString();
            var sku_ids_moning = selectGoodsSkuId_moning.toString();
            var sku_ids_teshu = selectGoodsSkuId_teshu.toString();


            //疗程天数
            var progress = $("#progress").val();
            var day = $("#day").val();
            var earlymoning_time = $('#earlymoning_time').val();
            var moning_time = $('#moning_time').val();
            var aftnoon_time = $('#aftnoon_time').val();
            var canjian_time = $('#canjian_time').val();
            var night_time = $('#night_time').val();
            var sleep_time = $('#sleep_time').val();
            var sku_ids_chenqi = sku_ids;
            sku_ids = sku_ids + ','+sku_ids_aftnoon + ','+sku_ids_canjian + ','+ sku_ids_night + ',' + sku_ids_evening + ',' + sku_ids_moning;
            /*if (earlymoning_time.length==0 && sku_ids_chenqi){
                return layer.confirm('晨起包时间不能为空');
            }
            if (moning_time.length==0 && sku_ids_moning){
                return layer.confirm('早餐包时间不能为空');
            }
            if (aftnoon_time.length==0 && sku_ids_aftnoon){
                return layer.confirm('午餐包时间不能为空');
            }
            if (canjian_time.length==0 && sku_ids_canjian){
                return layer.confirm('餐间包时间不能为空');
            }
            if (night_time.length==0 && sku_ids_night){
                return layer.confirm('晚餐包时间不能为空');
            }
            if (sleep_time.length==0 && sku_ids_evening){
                return layer.confirm('睡前包时间不能为空');
            }*/

            //获取商品数量
            var goods_name = [];
            var goods_nums = [];
            //获取特殊产品
            var goods_t_name = [];
            var goods_t_nums = [];
            var e_mm =0;  var e_m =0;  var e_a =0;  var e_n =0;  var e_e =0; var e_c =0;
            $("#goods").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_mm = e_mm + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_moning").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_m = e_m + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_a = e_a + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_canjian").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_c = e_c + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_night").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_n = e_n + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_evening").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
                e_e = e_e + Number($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_teshu").find("tbody td.price-one").each(function(i) {
                goods_t_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_t_nums.push($(this).parents("tr").find(".goods-num").val());
            });

            //前台会员账号
            var member_id = $("#member_id").val();
            //收货地址
            var addressname = $("input[name='addressname']").val();
            var addressmobile = $("input[name='addressmobile']").val();
            var address = $("input[name='address']").val();
            var province_id = $("select[name=province_id]").val();
            var city_id = $("select[name=city_id]").val();
            var district_id = $("select[name=district_id]").val();
            //其他字段
            var zhongdian = $("input[name='zhongdian']").val();
            var jiankangguanli = $("input[name='jiankangguanli']").val();


            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("shop/order/manual"),
                data:   {goods_t_name:goods_t_name,goods_t_nums:goods_t_nums,ccheck:ccheck,fenji:fenji,c_fenji:c_fenji,jiankangguanli:jiankangguanli,zhongdian:zhongdian,sleep_time:sleep_time,night_time:night_time,aftnoon_time:aftnoon_time,canjian_time:canjian_time,moning_time:moning_time,earlymoning_time:earlymoning_time,progress:progress,day:day,addressname:addressname,addressmobile:addressmobile,address:address,province_id:province_id,city_id:city_id,district_id:district_id,sku_ids:sku_ids,goods_name:goods_name,goods_nums:goods_nums,member_id:member_id,service_money:service_money},
                async: false,
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.open({
                            title:'您的订单已提交成功',
                            type:1,
                            content:$('#fankui'),
                            offset: 'auto',
                            area: ['700px'],
                            success: function(layero, index){
                                // 成功弹出层的回调
                                // 给弹窗绑定关闭事件监听
                                layero.find('.layui-layer-close').on('click', function(){
                                    // 在这里执行页面跳转
                                    window.location.href = ns.url("shop/order/lists");
                                });
                            }
                        });
                    } else {
                        layer.msg(res.message);
                    }
                }
            })
        });

        //普通图片上传
        var uploadInst = upload.render({
            elem: '#headImg',
            url: ns.url("shop/upload/image"),
            done: function(res) {
                if (res.code >= 0) {
                    $("input[name='headimg']").val(res.data.pic_path);
                    $("#headImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });

        form.on('submit(updatel)', function(data) {
            var goods_content = goodsContent.getContent();
            data.field.goods_content = goods_content;//商品详情
            data.field.member_id = $("#member_id").val();
            if(repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: ns.url("shop/member/addressDetail"),
                data: data.field,
                dataType: 'JSON', //服务器返回json格式数据
                type: 'POST', //HTTP请求类型
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('修改成功');
                    }else{
                        layer.msg(res.message);
                    }
                }
            });
        });

        form.on('submit(report)', function(data) {
            data.field.goods_image = goodsImage.toString();//商品主图
            data.field.member_id = $("#member_id").val();
            if(repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: ns.url("shop/member/accountDetail"),
                data: data.field,
                dataType: 'JSON', //服务器返回json格式数据
                type: 'POST', //HTTP请求类型
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('修改成功');
                    }else{
                        layer.msg(res.message);
                    }
                }
            });
        });
        /**
         * 监听保存该处方模板
         */
        form.on('submit(attr)', function(data) {
            var ccheck = $("input[name='ccheck']:checked").val();
            var fenji = $("input[name='fenji']:checked").val();
            var c_fenji = $("input[name='c_fenji']:checked").val();

            if (!ccheck){
                return layer.confirm('请选择是否二次查验');
            }

            if (!c_fenji && fenji==1){
                return layer.confirm('请选择粉剂是否分装');
            }
            var service_money = $(".service_money").text();

            var sku_ids = selectGoodsSkuId.toString();
            var sku_ids_aftnoon = selectGoodsSkuId_aftnoon.toString();
            var sku_ids_canjian = selectGoodsSkuId_canjian.toString();
            var sku_ids_night = selectGoodsSkuId_night.toString();
            var sku_ids_evening = selectGoodsSkuId_evening.toString();
            var sku_ids_moning = selectGoodsSkuId_moning.toString();
            sku_ids = sku_ids + ','+sku_ids_aftnoon + ','+sku_ids_canjian + ','+ sku_ids_night + ',' + sku_ids_evening + ',' + sku_ids_moning;
            //获取商品数量
            var goods_name = [];
            var goods_nums = [];
            $("#goods").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_moning").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_canjian").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_night").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_evening").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            //前台会员账号
            var member_id = $("#member_id").val();
            var class_id  = $("#class_id").val();

            //疗程天数
            var day = $("#day").val();
            var earlymoning_time = $('#earlymoning_time').val();
            var moning_time = $('#moning_time').val();
            var aftnoon_time = $('#aftnoon_time').val();
            var canjian_time = $('#canjian_time').val();
            var night_time = $('#night_time').val();
            var sleep_time = $('#sleep_time').val();

            var class_name = $('#orderattr').val();
            var price = $(".original-price").text();


            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("shop/orderattr/addorderAttr"),
                data:   {price:price,class_id:class_id,class_name:class_name,sleep_time:sleep_time,night_time:night_time,aftnoon_time:aftnoon_time,canjian_time:canjian_time,moning_time:moning_time,earlymoning_time:earlymoning_time,day:day,sku_ids:sku_ids,goods_name:goods_name,goods_nums:goods_nums,member_id:member_id,ccheck:ccheck,fenji:fenji,c_fenji:c_fenji,service_money:service_money},
                async: false,
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('添加成功', {
                            title: '操作提示',
                            btn: ['返回列表', '留在该页'],
                            yes: function() {
                                location.href = ns.url("shop/order/lists");
                            },
                            btn2: function() {

                            }
                        });
                    } else {
                        $('#attr').show();
                        layer.msg(res.message);
                    }
                }
            })
        });
// 选择粉剂事件
        form.on("radio(c_fenji)", function (data) {
            priceCount();
        });
        // 处方模板选择查询属性
        form.on("select(order_muban)", function (data) {
            var content = '';
            if (data.value) {
                if (data.value==1){
                    content = '<select id="class_id" name="orderattr" lay-search="" lay-filter="orderattr">'+$(".order_muban1").html()+'</select>';
                    $(".order_muban_check").html(content);
                }
                if (data.value==2){
                    content = '<select id="class_id" name="orderattr" lay-search="" lay-filter="orderattr">'+$(".order_muban2").html()+'</select>';
                    $(".order_muban_check").html(content);
                }
                if (data.value==3){
                    content = '<select id="class_id" name="orderattr" lay-search="" lay-filter="orderattr">'+$(".order_muban3").html()+'</select>';
                    $(".order_muban_check").html(content);
                }
                if (data.value==4){
                    content = '<select id="memberhistroy_detail2" name="memberhistroy_detail2" lay-search="" lay-filter="memberhistroy_detail2" >'+$("#memberhistroy_detail").html()+'</select>';
                    $(".order_muban_check").html(content);
                }
                layui.form.render("select");
            }
        });

        form.on("select(orderattr)", function (data) {
            if (data.value) {
                $.ajax({
                    url: ns.url("shop/order/orderattr"),
                    data: {class_id: data.value},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        $("#goods tbody").html('');
                        var html = '';
                        selectGoodsSkuId = [];
                        var earlymoning  = res.earlymoning;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='earlymoring_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId.push(item.sku_id);
                        }
                        $("#goods tbody").html(html);

                        $("#goods_moning tbody").html('');
                        html = '';
                        selectGoodsSkuId_moning = [];
                        earlymoning  = res.moning;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='moning_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_moning.push(item.sku_id);
                        }
                        $("#goods_moning tbody").html(html);

                        $("#goods_aftnoon tbody").html('');
                        html = '';
                        selectGoodsSkuId_aftnoon = [];
                        earlymoning  = res.aftnoon;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_aftnoon.push(item.sku_id);
                        }
                        $("#goods_aftnoon tbody").html(html);

                        $("#goods_canjian tbody").html('');
                        html = '';
                        selectGoodsSkuId_canjian = [];
                        earlymoning  = res.canjian;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_canjian.push(item.sku_id);
                        }
                        $("#goods_canjian tbody").html(html);

                        $("#goods_night tbody").html('');
                        html = '';
                        selectGoodsSkuId_night = [];
                        earlymoning  = res.night;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_night.push(item.sku_id);
                        }
                        $("#goods_night tbody").html(html);

                        $("#goods_evening tbody").html('');
                        html = '';
                        selectGoodsSkuId_evening = [];
                        earlymoning  = res.evening;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_evening.push(item.sku_id);
                        }
                        $("#goods_evening tbody").html(html);

                        $("#goods_teshu tbody").html('');
                        html = '';
                        selectGoodsSkuId_teshu = [];
                        earlymoning  = res.teshu;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_teshu.push(item.sku_id);
                        }
                        $("#goods_teshu tbody").html(html);

                        $("#earlymoning_time").val(res.attr_class_info.earlymoning_time);
                        $("#moning_time").val(res.attr_class_info.moning_time);
                        $("#aftnoon_time").val(res.attr_class_info.aftnoon_time);
                        $("#canjian_time").val(res.attr_class_info.canjian_time);
                        $("#night_time").val(res.attr_class_info.night_time);
                        $("#sleep_time").val(res.attr_class_info.sleep_time);
                        $(".original-price").text(res.attr_class_info.price);
                        $("#day").val(res.attr_class_info.day);
                        //判断是否选择分装粉剂
                        if(res.attr_class_info.ccheck==1){
                            $("input[name='ccheck']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='ccheck']").eq(1).attr("checked",true);
                        }
                        if(res.attr_class_info.fenji==1){
                            $("input[name='fenji']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='fenji']").eq(1).attr("checked",true);
                        }
                        if(res.attr_class_info.c_fenji==1){
                            $("input[name='c_fenji']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='c_fenji']").eq(1).attr("checked",true);
                        }
                        layui.form.render("radio");
                        priceCount();
                    }
                });
            }
        });

        form.on("select(memberhistroy)", function (data) {
            if (data.value) {
                $.ajax({
                    url: ns.url("shop/order/printorderr"),
                    data: {order_id: data.value,type: 1,api: 1},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        $(".modal-body").html(res);
                        var modall = document.getElementById("myModal");
                        modall.style.display = "block";
                    }
                });
            }
        });

        //历史既往模板
        form.on("select(memberhistroy_detail2)", function (data) {
            if (data.value) {
                $.ajax({
                    url: ns.url("shop/order/ordera"),
                    data: {order_id: data.value},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        $("#goods tbody").html('');
                        var html = '';
                        selectGoodsSkuId = [];
                        var earlymoning  = res.earlymoning;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='earlymoring_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId.push(item.sku_id);
                        }
                        $("#goods tbody").html(html);

                        $("#goods_moning tbody").html('');
                        html = '';
                        selectGoodsSkuId_moning = [];
                        earlymoning  = res.moning;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='moning_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_moning.push(item.sku_id);
                        }
                        $("#goods_moning tbody").html(html);

                        $("#goods_aftnoon tbody").html('');
                        html = '';
                        selectGoodsSkuId_aftnoon = [];
                        earlymoning  = res.aftnoon;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_aftnoon.push(item.sku_id);
                        }
                        $("#goods_aftnoon tbody").html(html);

                        $("#goods_canjian tbody").html('');
                        html = '';
                        selectGoodsSkuId_canjian = [];
                        earlymoning  = res.canjian;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_canjian.push(item.sku_id);
                        }
                        $("#goods_canjian tbody").html(html);

                        $("#goods_night tbody").html('');
                        html = '';
                        selectGoodsSkuId_night = [];
                        earlymoning  = res.night;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_night.push(item.sku_id);
                        }
                        $("#goods_night tbody").html(html);

                        $("#goods_evening tbody").html('');
                        html = '';
                        selectGoodsSkuId_evening = [];
                        earlymoning  = res.evening;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_evening.push(item.sku_id);
                        }
                        $("#goods_evening tbody").html(html);

                        $("#goods_teshu tbody").html('');
                        html = '';
                        selectGoodsSkuId_teshu = [];
                        earlymoning  = res.teshu;
                        for (var i in earlymoning) {
                            var item = earlymoning[i];
                            html += "<tr data-sku_id=" + item.sku_id + ">";
                            html += "<td>" + item.sku_name + "</td>";
                            html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                            html += "<td class='price-one'>" + item.price + "</td>";
                            html += "<td>" + item.stock + "</td>";
                            html += "<td><input name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                            html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                            html += "</tr>";
                            selectGoodsSkuId_teshu.push(item.sku_id);
                        }
                        $("#goods_teshu tbody").html(html);

                        $("#earlymoning_time").val(res.attr_class_info.earlymoning_time);
                        $("#moning_time").val(res.attr_class_info.moning_time);
                        $("#aftnoon_time").val(res.attr_class_info.aftnoon_time);
                        $("#canjian_time").val(res.attr_class_info.canjian_time);
                        $("#night_time").val(res.attr_class_info.night_time);
                        $("#sleep_time").val(res.attr_class_info.sleep_time);
                        $(".original-price").text(res.attr_class_info.price);
                        $("#day").val(res.attr_class_info.day);
                        //判断是否选择分装粉剂
                        if(res.attr_class_info.ccheck==1){
                            $("input[name='ccheck']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='ccheck']").eq(1).attr("checked",true);
                        }
                        if(res.attr_class_info.fenji==1){
                            $("input[name='fenji']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='fenji']").eq(1).attr("checked",true);
                        }
                        if(res.attr_class_info.c_fenji==1){
                            $("input[name='c_fenji']").eq(0).attr("checked",true);
                        }else{
                            $("input[name='c_fenji']").eq(1).attr("checked",true);
                        }
                        layui.form.render("radio");
                        priceCount();
                    }
                });
            }
        });

        form.on("select(order_deverys)", function (data) {
            if (data.value) {
                $.ajax({
                    url: ns.url("shop/member/orderAddress"),
                    data: {order_deverys: data.value},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        var result = res.data;
                        var initdata = {
                            province_id: result.province_id,
                            city_id: result.city_id,
                            district_id: result.district_id
                        };
                        console.log(initdata);
                        initAddress(initdata, "storeform");
                        $("input[name='addressname']").val(result.name);
                        $("input[name='addressmobile']").val(result.mobile);
                        $("input[name='address']").val(result.address);
                    }
                });
            }
        });

        // 商品类型选择查询属性
        if(guding_member_id==0) {
            form.on("select(memberaddress)", function (data) {
                if (data.value) {
                    $.ajax({
                        url: ns.url("shop/member/memberAddress"),
                        data: {member_id: data.value},
                        dataType: 'JSON',
                        type: 'POST',
                        success: function (res) {
                            var result = res.data;
                            var initdata = {
                                province_id: result.province_id,
                                city_id: result.city_id,
                                district_id: result.district_id
                            };
                            console.log(initdata);
                            //增加既往处方
                            $("#memberhistroy").html(result.memberhistroy);
                            $("#memberhistroy_detail").html(result.memberhistroy);
                            initAddress(initdata, "storeform");
                            $("input[name='addressname']").val(result.name);
                            $("input[name='addressmobile']").val(result.mobile);
                            $("input[name='address']").val(result.address);
                            // 加载商品主图
                            $("input[name='goods_image']").val(result.goods_image);
                            goodsImage = $("input[name='goods_image']").val().split(",");
                            //渲染商品主图列表
                            refreshGoodsImage();
                            goodsContent.setContent(result.goods_content);
                        }
                    });
                }
            });
        }
        form.verify({
            flo: function(value) {
                if (value == '') {
                    return;
                }
                var reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
                if (!reg.test(value)) {
                    return '价格不能小于0，可保留两位小数！'
                }
            }
        });
    });
    if (guding_member_id){
        $.ajax({
            url: ns.url("shop/member/memberAddress"),
            data: {member_id: guding_member_id},
            dataType: 'JSON',
            type: 'POST',
            success: function (res) {

                var result = res.data;
                var initdata = {province_id : result.province_id, city_id : result.city_id, district_id : result.district_id};
                console.log(initdata);
                //增加既往处方
                $("#memberhistroy").html(result.memberhistroy);
                $("#memberhistroy_detail").html(result.memberhistroy);
                initAddress(initdata, "storeform");
                $("input[name='addressname']").val(result.name);
                $("input[name='addressmobile']").val(result.mobile);
                $("input[name='address']").val(result.address);

            }
        });
    }else{
        $.ajax({
            url: ns.url("shop/member/memberAddress"),
            data: {member_id: member_id},
            dataType: 'JSON',
            type: 'POST',
            success: function (res) {
                $("#member_id").val(member_id);
                layui.form.render("select");

                var result = res.data;
                var initdata = {province_id : result.province_id, city_id : result.city_id, district_id : result.district_id};
                console.log(initdata);
                //增加既往处方
                $("#memberhistroy").html(result.memberhistroy);
                $("#memberhistroy_detail").html(result.memberhistroy);
                initAddress(initdata, "storeform");
                $("input[name='addressname']").val(result.name);
                $("input[name='addressmobile']").val(result.mobile);
                $("input[name='address']").val(result.address);

            }
        });
    }


    $.ajax({
        url: ns.url("shop/order/ordera"),
        data: {order_id: order_id},
        dataType: 'JSON',
        type: 'POST',
        success: function (res) {
            $("#goods tbody").html('');
            var html = '';
            selectGoodsSkuId = [];
            var earlymoning  = res.earlymoning;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='earlymoring_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId.push(item.sku_id);
            }
            $("#goods tbody").html(html);

            $("#goods_moning tbody").html('');
            html = '';
            selectGoodsSkuId_moning = [];
            earlymoning  = res.moning;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='moning_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_moning.push(item.sku_id);
            }
            $("#goods_moning tbody").html(html);

            $("#goods_aftnoon tbody").html('');
            html = '';
            selectGoodsSkuId_aftnoon = [];
            earlymoning  = res.aftnoon;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_aftnoon.push(item.sku_id);
            }
            $("#goods_aftnoon tbody").html(html);

            $("#goods_canjian tbody").html('');
            html = '';
            selectGoodsSkuId_canjian = [];
            earlymoning  = res.canjian;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_canjian.push(item.sku_id);
            }
            $("#goods_canjian tbody").html(html);

            $("#goods_night tbody").html('');
            html = '';
            selectGoodsSkuId_night = [];
            earlymoning  = res.night;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_night.push(item.sku_id);
            }
            $("#goods_night tbody").html(html);

            $("#goods_evening tbody").html('');
            html = '';
            selectGoodsSkuId_evening = [];
            earlymoning  = res.evening;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_evening.push(item.sku_id);
            }
            $("#goods_evening tbody").html(html);

            $("#goods_teshu tbody").html('');
            html = '';
            selectGoodsSkuId_teshu = [];
            earlymoning  = res.teshu;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><input name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='" + item.num + "' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId_teshu.push(item.sku_id);
            }
            $("#goods_teshu tbody").html(html);

            $("#earlymoning_time").val(res.attr_class_info.earlymoning_time);
            $("#moning_time").val(res.attr_class_info.moning_time);
            $("#aftnoon_time").val(res.attr_class_info.aftnoon_time);
            $("#canjian_time").val(res.attr_class_info.canjian_time);
            $("#night_time").val(res.attr_class_info.night_time);
            $("#sleep_time").val(res.attr_class_info.sleep_time);
            $(".original-price").text(res.attr_class_info.price);
            $("#day").val(res.attr_class_info.day);
            $("#jiankangguanli").val(res.attr_class_info.jiankangguanli);
            $("#zhongdian").val(res.attr_class_info.zhongdian);
            $("#progress").val(res.attr_class_info.progress);
            //判断是否选择分装粉剂
            if(res.attr_class_info.ccheck==1){
                $("input[name='ccheck']").eq(0).attr("checked",true);
            }else{
                $("input[name='ccheck']").eq(1).attr("checked",true);
            }
            if(res.attr_class_info.fenji==1){
                $("input[name='fenji']").eq(0).attr("checked",true);
            }else{
                $("input[name='fenji']").eq(1).attr("checked",true);
            }
            if(res.attr_class_info.c_fenji==1){
                $("input[name='c_fenji']").eq(0).attr("checked",true);
            }else{
                $("input[name='c_fenji']").eq(1).attr("checked",true);
            }
            layui.form.render("radio");
            priceCount();
        }
    });

    /**
     * 添加商品
     */
    function addGoods() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        console.log(goods_r);
        console.log(goods_n);
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='earlymoring_" + item.sku_id + "' type='number' min='1'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId.push(item.sku_id);
                }
            }


            $("#goods tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_moning() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_moning").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_moning tbody .goods-empty").length ? '' : $("#goods_moning tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='moning_" + item.sku_id + "' type='number' min='1'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_moning(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_moning.push(item.sku_id);
                }
            }


            $("#goods_moning tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_moning, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_aftnoon() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_aftnoon").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_aftnoon tbody .goods-empty").length ? '' : $("#goods_aftnoon tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_aftnoon(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_aftnoon.push(item.sku_id);
                }
            }


            $("#goods_aftnoon tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_aftnoon, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 增加餐间商品
     */
    function addGoods_canjian() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_canjian").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_canjian tbody .goods-empty").length ? '' : $("#goods_canjian tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_canjian(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_canjian.push(item.sku_id);
                }
            }


            $("#goods_canjian tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_canjian, {mode: "sku", max_num: 0, min_num: 0});
    }


    /**
     * 添加晚餐商品
     */
    function addGoods_night() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_night").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_night tbody .goods-empty").length ? '' : $("#goods_night tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_night(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_night.push(item.sku_id);
                }
            }


            $("#goods_night tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_night, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加睡前商品
     */
    function addGoods_evening() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_evening").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_evening tbody .goods-empty").length ? '' : $("#goods_evening tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_evening(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_evening.push(item.sku_id);
                }
            }
            $("#goods_evening tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_evening, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加睡前商品
     */
    function addGoods_teshu() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods_teshu").find("input[type='number']").each(function(i) {
            //获取数量

            goods_r.push($(this).attr('name'));
            goods_num = Number($("input[name='" + $(this).attr('name') + "']").val());
            goods_n.push(goods_num);

        });
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_teshu tbody .goods-empty").length ? '' : $("#goods_teshu tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_teshu(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_teshu.push(item.sku_id);
                }
            }
            $("#goods_teshu tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_teshu, {mode: "sku", max_num: 0, min_num: 0});
    }

    // 监听单元格编辑
    function editSort(event){
        priceCount(); //计算出当前总价格
    }
    /**
     * 删除商品
     */
    function deleteGoods(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId) {
            if (selectGoodsSkuId[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId.splice(i, 1);
            }
        }
    }

    function deleteGoods_moning(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_moning) {
            if (selectGoodsSkuId_moning[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_moning.splice(i, 1);
            }
        }
    }

    function deleteGoods_aftnoon(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_aftnoon) {
            if (selectGoodsSkuId_aftnoon[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_aftnoon.splice(i, 1);
            }
        }
    }

    function deleteGoods_canjian(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_canjian) {
            if (selectGoodsSkuId_canjian[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_canjian.splice(i, 1);
            }
        }
    }

    function deleteGoods_night(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_night) {
            if (selectGoodsSkuId_night[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_night.splice(i, 1);
            }
        }
    }

    function deleteGoods_evening(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_evening) {
            if (selectGoodsSkuId_evening[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_evening.splice(i, 1);
            }
        }
    }

    function deleteGoods_teshu(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_teshu) {
            if (selectGoodsSkuId_teshu[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_teshu.splice(i, 1);
            }
        }
    }

    /**
     * 计算总价
     */
    function priceCount() {
        var fenji = $("input[name='fenji']:checked").val();
        var c_fenji = $("input[name='c_fenji']:checked").val();
        var goods_name = [];
        var goods_nums = [];
        var goods_t_name = [];
        var goods_t_nums = [];
        var price_count = 0;
        $("#goods").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_moning").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_canjian").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_night").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_evening").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        var day = $(".combined-price").val();
        price_count = price_count * day;
        $("#goods_teshu").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_t_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_t_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });

        $.ajax({
            type: 'POST',
            dataType: 'JSON',
            url: ns.url("shop/order/fuwufei"),
            data:   {fenji:fenji,c_fenji:c_fenji,day:day,goods_name:goods_name,goods_nums:goods_nums,goods_t_name:goods_t_name,goods_t_nums:goods_t_nums},
            async: false,
            success: function(res) {
                repeat_flag = false;
                if (res.code == 0) {
                    if(res.data.fen_ye_detail && res.data.fen_ye_detail.length > 0) {
                        var html = '';
                        for (var i = 0; i < res.data.fen_ye_detail.length; i++) {
                            var item = res.data.fen_ye_detail[i];
                            html += '<tr data-sku_id="' + item.sku_id + '">';
                            html += '<td>' + item.sku_name + '</td>';
                            html += '<td><img width="60px" layer-src src="' + ns.img(item.sku_image) + '"/></td>';
                            html += '<td class="price-one">' + item.price + '</td>';
                            html += '<td>' + item.stock + '</td>';
                            html += '<td><input type="number" min="'+item.fen_ye_num+'" value="'+item.fen_ye_num+'" class="fenji-num layui-input edit-sort ns-len-short" ></td>';
                            html += '<td>每天所需'+item.fen_ye_daynum+'小袋,共'+item.fen_ye_day+'天,产品整瓶规格'+item.daizhuang+'小袋</td>';
                            html += '</tr>';
                        }
                        $("#fenji_goods tbody").html(html);
                    }
                    price_count = Number(res.data.money);
                    $(".service_money").text(res.data.remark);
                    $(".guding_service_money").text(res.data.fen_ye_remark);
                    //处理粉剂是否需要分装
                    if(res.data.fenji==1){
                        $("input[name='fenji']").eq(0).attr("checked",true);
                        $("input[name='fenji']").eq(1).attr("checked",false);
                        $('#c_fenji').show();
                    }else{
                        $("input[name='fenji']").eq(0).attr("checked",false);
                        $("input[name='fenji']").eq(1).attr("checked",true);
                        //$("input[name='c_fenji']").eq(0).attr("checked",false);
                        //$("input[name='c_fenji']").eq(1).attr("checked",false);
                        $('#c_fenji').hide();
                    }
                    //处理成份含量
                    var html = '';
                    for (let k in res.data.goods_attr_formats) {
                        var item = res.data.goods_attr_formats[k];
                        html += "<tr class='goods-attr-tr goods-attr-temp'>";
                        html += "<td>" + item.attr_name + "</td>";
                        html += "<td>" + item.attr_value_name + "</td>";
                        html += "</tr>";
                    }
                    $("#attr_fomat").html(html);
                    if (html){
                        $("#attr_fomats").show();
                        $("#attr_fomatw").show();
                    }else{
                        $("#attr_fomats").hide();
                        $("#attr_fomatw").hide();
                    }

                }
            }
        })
        $(".original-price").text(price_count.toFixed(2));
    }

    $("#bl_price").blur(function() {
        var bl_price = $(this).val();
        if (bl_price < 0) {
            layer.msg("价格不能小于0，可保留两位小数");
        }
    });

    /**
     * 计算组合套餐价格、原价、节省价
     */
    $(".combined-price").blur(function() {
        priceCount();
    });

    // 两个浮点数相减
    function accSub(num1, num2){
        var r1, r2, m;

        try{
            r1 = num1.toString().split(".")[1].length;
        }catch(e){
            r1 = 0;
        }

        try{
            r2 = num2.toString().split(".")[1].length;
        }catch(e){
            r2 = 0;
        }

        m = Math.pow(10, Math.max(r1, r2));
        n = (r1 >= r2) ? r1 : r2;
        return (Math.round(num1 * m - num2 * m) / m).toFixed(2);
    }

    //渲染商品主图列表
    function refreshGoodsImage() {
        var goods_image_template = $("#goodsImage").html();
        laytpl(goods_image_template).render(goodsImage, function (html) {
            $(".js-goods-image").html(html);
            //加载图片放大
            loadImgMagnify();

            if (goodsImage.length) {

                //预览
                $(".js-goods-image .js-preview").click(function () {
                    $(this).parent().prev().find("img").click();
                });

                //图片删除
                $(".js-goods-image .js-delete").click(function () {
                    var index = $(this).attr("data-index");
                    goodsImage.splice(index, 1);
                    refreshGoodsImage();
                });

                // 拖拽
                $('.js-goods-image .item').arrangeable({
                    //拖拽结束后执行回调
                    callback: function (e) {
                        var indexBefore = $(e).attr("data-index");//拖拽前的原始位置
                        var indexAfter = $(e).index();//拖拽后的位置
                        var temp = goodsImage[indexBefore];
                        goodsImage[indexBefore] = goodsImage[indexAfter];
                        goodsImage[indexAfter] = temp;

                        refreshGoodsImage();
                    }
                });
            }

            //最多传十张图
            if (goodsImage.length < GOODS_IMAGE_MAX) {
                $(".js-add-goods-image").show();
            } else {
                $(".js-add-goods-image").hide();
            }

        });
    }


</script>
{/block}