{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" href="SHOP_CSS/order_list.css"/>
<style>
    .ns-table-tab .layui-tab-content {padding-top: 0;}
</style>
{/block}
{block name="main"}
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show"  lay-filter="order_list">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">搜索方式：</label>
                    <div class="layui-input-inline">
                        <select name="order_label" >
                            {foreach $order_label_list as $k => $label_val}
                            <option value="{$k}">{$label_val}</option>
                            {/foreach}
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="search" autocomplete="off" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">支付时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="pstart_time" placeholder="开始时间" id="pstart_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="pend_time" placeholder="结束时间" id="pend_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">发货时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="dstart_time" placeholder="开始时间" id="dstart_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="dend_time" placeholder="结束时间" id="dend_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">下单时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(7, this);return false;">近7天</button>
                    <button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(30, this);return false;">近30天</button>
                </div>
            </div>

            <div class="layui-form-item" {if !$user_type}hidden{/if}>
            <div class="layui-inline">
                <label class="layui-form-label">订单类型：</label>
                <div class="layui-input-inline">
                    <select name="order_type" lay-filter="order_type">
                        {foreach $order_type_list as $order_type_k => $order_type_val}
                        <option value="{$order_type_val.type}">{$order_type_val.name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">订单状态：</label>
                <div class="layui-input-inline">
                    <select name="order_status" lay-filter="order_status">
                        <option value="">全部</option>
                        {foreach $order_status_list as $k => $status_val}
                        <option value="{$status_val.status}">{$status_val.name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">营销类型：</label>
                <div class="layui-input-inline">
                    <select name="promotion_type" lay-filter="promotion_type">
                        <option value="">全部</option>
                        {foreach $promotion_type as $promotion_type_k => $promotion_type_val}
                        <option value="{$promotion_type_val.type}">{$promotion_type_val.name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
    </div>
    <div class="layui-form-item" {if !$user_type}hidden{/if}>
    <div class="layui-inline">
        <label class="layui-form-label">付款方式：</label>
        <div class="layui-input-inline">
            <select name="pay_type" >
                <option value="">全部</option>
                {foreach pay_type_list as $pay_type_k => $pay_type_v}
                <option value="{$pay_type_k}">{$pay_type_v}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="layui-inline">
        <label class="layui-form-label">订单来源：</label>
        <div class="layui-input-inline">
            <select name="order_from">
                <option value="">全部</option>
                {foreach $order_from_list as $order_from_k => $order_from_v}
                <option value="{$order_from_k}">{$order_from_v['name']}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="layui-inline">
        <label class="layui-form-label">机构：</label>
        <div class="layui-input-inline">
            <select name="userlist" lay-search="">
                <option value="">全部</option>
                {foreach $userlist as $order_from_k => $order_from_v}
                <option value="{$order_from_v['uid']}">{$order_from_v['username']}</option>
                {/foreach}
            </select>
        </div>
    </div>
</div>

<div class="ns-form-row">
    <button class="layui-btn ns-bg-color" lay-submit id="btn_search"lay-filter="btn_search">筛选</button>
    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    {if $user['source_uid']==0}
    <button class="layui-btn layui-btn-primary" onclick="alert('点击确定订单数据将开始下载，请耐心等待，请勿重复点击导出！');" lay-submit lay-filter="batch_export" >批量导出</button>
    <button class="layui-btn layui-btn-primary" onclick="alert('点击确定订单数据将开始下载，请耐心等待，请勿重复点击导出！');" lay-submit lay-filter="batch_exportt" >商品导出</button>
    <button class="layui-btn layui-btn-primary" onclick="alert('点击确定订单数据将开始下载，请耐心等待，请勿重复点击导出！');" lay-submit lay-filter="batch_exporttt" >订单分类</button>
    <button class="layui-btn layui-btn-primary" onclick="alert('点击确定订单数据将开始下载，请耐心等待，请勿重复点击导出！');" lay-submit lay-filter="batch_exportttt" >帐单明细</button>
    <button class="layui-btn layui-btn-primary">查看已生成报表</button>
    {/if}
</div>
<input type="hidden" name="status" val=""/>
<input type="hidden" name="page" val=""/>
</form>
</div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="order_tab">
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="">全部订单</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 列表 -->
        <div id="order_list"></div>
    </div>
</div>

<div id="order_page"></div>
<div id="order_operation"></div>
{/block}
{block name="script"}
{include file="order/order_common_action" /}
<!-- 修改订单收货地址 -->
{include file="order/order_action" /}
<!-- 发货 -->
{include file="order/order_delivery_action" /}

<!-- 外卖发货 -->
{include file="localorder/local_order_delivery_action" /}
<script src="SHOP_JS/order_list.js"></script>
<script>
    var laypage,element, form, hash_url;
    var is_refresh = false;
    var order_type_status_json = {:json_encode($order_type_list)};
    /**
     *通过hash获取页数
     */
    function getHashPage(){
        var page = 1;
        var hash = location.hash;

        var hash_arr = hash.split("&");
        $.each(hash_arr,function(index, itemobj){
            var item_arr = itemobj.split("=");
            if(item_arr.length == 2){
                if(item_arr[0].indexOf("page") != "-1"){
                    page = item_arr[1];
                }
            }
        });
        return page;
    }

    //从hash中获取数据
    function getHashData(){
        var hash = ns.urlReplace(location.hash);
        var data= [];

        var hash_arr = hash.split("&");

        var form_json = {
            "end_time" : "",
            "dend_time" : "",
            "pend_time" : "",
            "order_from" : "",
            "order_label" : $("select[name=order_label]").val(),
            "order_name" : "",
            "order_status" : "",
            "promotion_type" : "",
            "pay_type" : "",
            "search" : "",
            "start_time" : "",
            "dstart_time" : "",
            "pstart_time" : "",
            "order_type" : 'all',
            "page" : ""
        };
        if(hash_arr.length > 0){
            // page = hash_arr[0].replace('#!page=', '');
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    $.each(form_json,function(key, form_val){
                        if(item_arr[0].indexOf(key) != "-1"){
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }
        resetOrderStatus(form_json.order_type, 2);
        form.val("order_list", form_json);
        setOrderStatusTab(form_json.order_status);
        return form_json;
    }

    /**
     * 获取哈希值order_type
     */
    function getHashOrderType(){
        var hash = ns.urlReplace(location.hash);
        var hash_arr = hash.split("&");
        var order_type = "all";
        if(hash_arr.length > 0){
            // page = hash_arr[0].replace('#!page=', '');
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    if(item_arr[0].indexOf("order_type") != "-1") {
                        order_type = item_arr[1];
                    }
                }
            })
        }
        return order_type;
    }
    layui.use(['laypage','laydate','form', 'element'], function(){
        form = layui.form;
        laypage = layui.laypage;
        element = layui.element;
        var laydate = layui.laydate;
        form.render();

        //渲染时间
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#dstart_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#dend_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#pstart_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#pend_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        //监听筛选事件
        form.on('submit(btn_search)', function(data){
            is_refresh = true;
            data.field.page = 1;
            resetOrderStatus(data.field.order_type, 2);
            setHashOrderList(data.field);
            setOrderStatusTab(data.field.order_status);
            return false;
        });

        //批量导出
        form.on('submit(batch_export)', function(data){
            data.field.order_type = 1;
            location.href = ns.url("shop/order/exportOrderGoods",data.field);
            return false;
        });

        form.on('submit(batch_exportt)', function(data){
            data.field.order_type = 1;
            location.href = ns.url("shop/order/exportOrderGoodss",data.field);
            return false;
        });

        form.on('submit(batch_exporttt)', function(data){
            data.field.order_type = 1;
            location.href = ns.url("shop/order/exportOrderr",data.field);
            return false;
        });

        form.on('submit(batch_exportttt)', function(data){
            data.field.order_type = 1;
            location.href = ns.url("shop/order/exportOrderrr",data.field);
            return false;
        });


        //订单类型
        form.on('select(order_type)', function(data){

            resetOrderStatus(data.value, 1);
            return false;
        });

        //监听Tab切换，以改变地址hash值
        element.on('tab(order_tab)', function(){
            var status = this.getAttribute('lay-id');
            form.val("order_list", {"order_status":status});

            var hash_data = getHashList();
            hash_data.order_status = status;
            hash_data.page = 1;
            setHashOrderList(hash_data);
        });
        getHashData();
        getOrderList();//筛选

    });

    function setOrderStatusTab(order_status){
        $(".layui-tab-title li").removeClass("layui-this");
        $(".layui-tab-title li").each(function(){
            var status = $(this).attr("lay-id");
            if(status == order_status){
                $(this).addClass("layui-this")
            }
        });
    }
    //重置状态tab 选项卡
    function resetOrderStatus(order_type, is_tab){
        var hash_order_type = getHashOrderType();
        if(hash_order_type != order_type || is_refresh == false){
            if(is_tab != 1 || is_refresh == false) {
                $(".layui-tab-title li").not(':first').remove();
                $(".layui-tab-title li").find(":first").addClass("layui-this");
            }
            if(is_tab != 2 || is_refresh == false){
                $("select[name=order_status] option").not(':first').remove();
            }

            var status_item = order_type_status_json[order_type]["status"];
            $.each(status_item,function(index, itemobj){
                if(is_tab != 1 || is_refresh == false) {
                    $(".layui-tab-title").append('<li lay-id="' + index + '">' + itemobj + '</li>');
                }
                if(is_tab != 2 || is_refresh == false) {
                    $("select[name=order_status]").append('<option value="' + index + '">' + itemobj + '</option>');
                }
            });
            form.render('select');
        }
    }

    //哈希值 订单数据
    function setHashOrderList(data){
        var hash = "";
        $.each(data,function(index, itemobj){
            if(itemobj != "" && itemobj != "all"){
                if(hash == ""){
                    hash += "#!"+index +"="+itemobj;
                }else{
                    hash += "&"+index +"="+itemobj;
                }
            }
        });
        // window.location.href = hash;
        hash_url = hash;
        location.hash = hash;
        getOrderList();
    }

    function getHashList(){
        var hash = ns.urlReplace(location.hash);
        var data= [];
        var hash_arr = hash.split("&");

        var form_json = {
            "end_time" : "",
            "dend_time" : "",
            "pend_time" : "",
            "order_from" : "",
            "order_label" : $("select[name=order_label]").val(),
            "order_name" : "",
            "order_status" : "",
            "promotion_type" : "",
            "pay_type" : "",
            "search" : "",
            "start_time" : "",
            "dstart_time" : "",
            "pstart_time" : "",
            "order_type" : 'all',
            "page" : ""
        };
        if(hash_arr.length > 0){
            // page = hash_arr[0].replace('#!page=', '');
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    $.each(form_json,function(key, form_val){
                        if(item_arr[0].indexOf(key) != "-1"){
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }

        return form_json;
    }

    var order = new Order();
    function getOrderList(){
        var url = ns.url("shop/order/lists", ns.urlReplace(location.hash.replace('#!', '')));

        $.ajax({
            type : 'get',
            dataType: 'json',
            url :url,
            success : function(res){
                if(res.code == 0){
                    order.setData(res.data);
                    $("#order_list").html(order.fetch());
                    //执行一个laypage实例
                    laypage.render({
                        elem: 'order_page',
                        count: res.data.count,
                        curr: getHashPage(),
                        layout: ['count', 'prev', 'page', 'next'],
                        // hash: 'page',
                        jump: function(obj, first){
                            //首次不执行
                            if(!first){
                                var hash_data = getHashData();
                                hash_data.page = obj.curr;
                                setHashOrderList(hash_data);
                                // $("#btn_search").click();//筛选
                            }
                        }
                    });
                }else{
                    layer.msg(res.message);
                }
            }
        });
    }

    /**
     * 七天时间
     */
    function datePick(date_num,event_obj){
        $(".date-picker-btn").removeClass("selected");
        $(event_obj).addClass('selected');
        // alert(new Date().format("yyyy-MM-dd hh:mm"));
        var now_date = new Date();

        Date.prototype.Format = function (fmt,date_num) { //author: meizz
            this.setDate(this.getDate()-date_num);
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };
        // var now_time =  new Date().Format("yyyy-MM-dd HH:mm:ss",0);//当前日期
        var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
        var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
        $("input[name=start_time]").val(before_time,0);
        $("input[name=end_time]").val(now_time,date_num-1);

    }
</script>
{/block}