<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="__STATIC__/js/map_address.js"></script>
<script type="text/javascript" src="{$http_type}://webapi.amap.com/maps?v=1.4.6&amp;key=66d777244fce2401c784bdaa1da8decd"></script>
<script type="text/javascript" src="{$http_type}://webapi.amap.com/demos/js/liteToolbar.js"></script>
<style>
    .update-address-html .order-map{width:876px;height:380px}
</style>

<!-- 修改收货地址模态 -->
<div id="update_address_box" class="update-address-box"></div>
<script type="text/html" id="update_address_html">

<div class="layui-form update-address-html" id='update_address'lay-filter="update_address">
    <input type="hidden" name="order_id" value="{{ d.order_id }}"/>
    <!--自提点地址-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">* </span>收货地址</label>
        <div class="layui-input-inline area-select">
            <select name="province_id" lay-filter="province_id" lay-verify="province_id">
                <option value="">请选择省份</option>

            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="city_id"  lay-filter="city_id" lay-verify="city_id">
                <option value="">请选择城市</option>
            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="district_id"  lay-filter="district_id" lay-verify="district_id">
                <option value="">请选择区/县</option>
            </select>
        </div>
        <!--<div class="layui-input-inline area-select ns-len-mid">-->
            <!--<select name="community_id" lay-filter="community_id" lay-verify="community_id">-->
                <!--<option value="">请选择街道</option>-->
            <!--</select>-->
        <!--</div>-->
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-inline" >
            <input type="text" name="address"  placeholder="请填写具体地址。" lay-verify="address" autocomplete="off" class="layui-input address-content ns-len-long" value="{{# if(d.address != undefined){}}{{ d.address}}{{#  } }}">
            <!--<input type = "hidden" name="longitude" lay-verify="required" class="layui-input" value="{{# if(d.longitude != undefined){}}{{ d.longitude}}{{#  } }}"/>-->
            <!--<input type = "hidden" name="latitude" lay-verify="required" class="layui-input"value="{{# if(d.latitude != undefined){}}{{ d.latitude}}{{#  } }}"/>-->
        </div>
        <!--<button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>-->
    </div>
    <!--地图定位-->
    <!--<div class="layui-form-item">-->
        <!--<label class="layui-form-label">地图定位</label>-->
        <!--<div class="layui-input-block ns-special-length">-->
            <!--<div id="container" class="order-map"></div>-->
        <!--</div>-->
    <!--</div>-->
    <!--联系人方式-->
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">收货人</label>
            <div class="layui-input-block">
                <input type="text" name="name" lay-verify="required" placeholder="请填写收货联系人" autocomplete="off" class="layui-input selffetch-input ns-len-mid" value="{{# if(d.name != undefined){}}{{ d.name}}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">手机号码</label>
            <div class="layui-input-block">
                <input type="text" name="mobile" lay-verify="phone" placeholder="请填写手机号码" autocomplete="off" class="layui-input selffetch-input ns-len-mid" value="{{# if(d.mobile != undefined){}}{{d.mobile }}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">固定号码</label>
            <div class="layui-input-block">
                <input type="text" name="telephone" placeholder="请填写固定号码" autocomplete="off" class="layui-input selffetch-input ns-len-mid" value="{{# if(d.telephone != undefined){}}{{d.telephone }}{{#  } }}">
            </div>
        </div>
    </div>
    <button class="layui-btn"  lay-submit id="submit_address" lay-filter="submit_address" style="display:none;">保存</button>
</div>
</script>
<script>

var map_class, form;
/**
 * 订单地址修改
 */
function orderAddressUpdate(order_id) {
    $.ajax({
        type: "post",
        url: ns.url("shop/order/getOrderInfo"),
        async: true,
        dataType: 'json',
        data: {
            "order_id": order_id
        },
        success: function (res) {
            if(res.code == 0){
                if (res.data.doctor == 1 && res.data.order_status > 0){
                    layer.msg('订单配货中，修改地址需要联系管理员', {time: 2000, icon: 5});
                    return;
                }
                //获取模板
                var getTpl = $("#update_address_html").html();
                //渲染模板
                var data = res.data;
                laytpl(getTpl).render(data, function(html) {
                    layer.open({
                        type: 1,
                        shadeClose: true,
                        shade: 0.3,
                        offset: 'auto',
                        scrollbar: true,
                        fixed: false,
                        title: "编辑收货地址",
                        area: ['1000px'],
                        btn: ['确定', '取消'],
                        yes: function(index, layero){
                            $("#submit_address").click();
                        },
                        btn2: function(index, layero){
                            layer.close(index);
                        },
                        content:  html,
                        cancel: function(){
                            //右上角关闭回调
                            //return false 开启该代码可禁止点击该按钮关闭
                        },
                        success: function(layero, index){

                            //初始化省级地址
                            getAreaList(0, 1);
                            var repeat_flag = false;//防重复标识
                            form.render();
                            var initdata = {province_id : res.data.province_id, city_id : res.data.city_id, district_id : res.data.district_id, community_id : res.data.community_id};
                            initAddress(initdata, "update_address");

                            if($.isEmptyObject(data) == true){
                                var latlng = {lat:'',lng:''};
                            }else{
                                var latlng = {lat:data.latitude,lng:data.longitude};
                            }
                            // map_class = new mapClass("container",latlng);
                            form.render();
							
                            form.on('submit(submit_address)', function(data){
								if(data.field.province_id == ''){
									layer.msg('请选择省份', {icon: 5, anim: 6});
								    return;
								}
								if(data.field.city_id == ''){
									layer.msg('请选择城市', {icon: 5, anim: 6});
									return;
								}
								if(data.field.district_id == ''){
								    layer.msg('请选择区/县', {icon: 5, anim: 6});
								    return;
								}
								if(data.field.address == ''){
								    layer.msg('请输入详细地址', {icon: 5, anim: 6});
								    return;
								}
                                var province_name = $("option[value='" + data.field.province_id + "']").text();
                                var city_name = $("option[value='" + data.field.city_id + "']").text();
                                var district_name = $("option[value='" + data.field.district_id + "']").text();
                                var community_name = $("option[value='" + data.field.community_id + "']").text();
                                data.field.province_name = province_name;
                                data.field.city_name = city_name;
                                data.field.district_name = district_name;
                                data.field.community_name = community_name;
                                data.field.full_address = province_name + city_name + district_name + community_name + data.field.address;

								if(repeat_flag)return;
								repeat_flag = true;
                                $.ajax({
                                    url: ns.url("shop/order/editaddress"),
                                    type: "POST",
                                    dataType: "JSON",
                                    async: false,
                                    data: data.field,
                                    success: function (res) {
                                        layer.msg(res.message);
                                        if(res.code == 0){
                                            layer.closeAll();
                                            location.reload();
                                        }else{
                                            repeat_flag = false;
                                        }
                                    }
                                });
                                return false;
                            });
                        }
                    });
                    form.render();
                });
            }
        }
    })

}

/**
 * 重新渲染表单
 */
function refreshFrom(){
    form.render();
    orderAddressChange();//改变地址
    // map_class.mapChange();
}
/**
 * 获取地区列表
 * @param pid
 * @param level
 */
function getAreaList(pid, level){
    if(level <= 5){
        $.ajax({
            type : "post",
            url : ns.url("shop/address/getAreaList"),
            data : {
                'level' : level,
                'pid' : pid
            },
            dataType: 'json',
            async : false,
            success : function(res) {
                if(res.code == 0){
                    if(level == 1){
                        $("select[name=province_id] option:gt(0)").remove();
                        $("select[name=city_id] option:gt(0)").remove();
                        $("select[name=district_id] option:gt(0)").remove();
                        $.each(res.data, function(name, value) {
                            $("select[name=province_id]").append("<option value='"+value.id+"'>"+value.name+"</option>");
                        });
                    }else if(level == 2){
                        $("select[name=city_id] option:gt(0)").remove();
                        $("select[name=district_id] option:gt(0)").remove();
                        $.each(res.data, function(name, value) {
                            $("select[name=city_id]").append("<option value='"+value.id+"'>"+value.name+"</option>");
                        });
                    }else if(level == 3){
                        $("select[name=district_id] option:gt(0)").remove();
                        $.each(res.data, function(name, value) {
                            $("select[name=district_id]").append("<option value='"+value.id+"'>"+value.name+"</option>");
                        });

                    }else if(level == 4){
                        $("select[name=community_id] option:gt(0)").remove();
                        $.each(res.data, function(name, value) {
                            $("select[name=community_id]").append("<option value='"+value.id+"'>"+value.name+"</option>");
                        });
                    }
                }else{
                    layer.msg(res.message);
                }
                form.render();
            }
        });
    }
    
}

//动态改变订单地址赋值
function orderAddressChange(){
    map_class.address.province = $("select[name=province_id]").val();
    map_class.address.province_name = $("select[name=province_id] option:selected").text();
    map_class.address.city = $("select[name=city_id]").val();
    map_class.address.city_name = $("select[name=city_id] option:selected").text();
    map_class.address.district = $("select[name=district_id]").val();
    map_class.address.district_name = $("select[name=district_id] option:selected").text();
    map_class.address.township = $("select[name=community_id]").val();
    map_class.address.township_name = $("select[name=community_id] option:selected").text();
    map_class.address.address = $("input[name=address]").val()
}

/**
 * 地址下拉框（主要用于坐标记录）
 */
function selectCallBack(){
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
}
//地图点击回调时间
function mapChangeCallBack(){
    $("input[name=address]").val(map_class.address.address);//详细地址
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
    $.ajax({
        type : "post",
        url : ns.url("shop/address/getGeographicId"),
        dataType: 'json',
        async : true,
        data : {
            "address" : map_class.address.area
        },
        success : function(data) {
            map_class.address.province = data.province_id;
            map_class.address.city = data.city_id;
            map_class.address.district = data.district_id;
            map_class.address.township = data.community_id;
            map_class.map_change = false;
            form.val("update_address", {
                "province_id": data.province_id // "name": "value"
            });
            $("select[name=province_id]").change();
            form.val("update_address", {
                "city_id": data.city_id // "name": "value"
            });
            $("select[name=city_id]").change();
            form.val("update_address", {
                "district_id": data.district_id // "name": "value"
            });
            $("select[name=district_id]").change();
            form.val("update_address", {
                "community_id": data.community_id // "name": "value"
            });
            refreshFrom();//重新渲染form
            map_class.map_change = true;
        }
    })
}
</script>