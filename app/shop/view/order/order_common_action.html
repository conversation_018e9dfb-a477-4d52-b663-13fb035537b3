<script type="text/javascript">
var laytpl;
var form;
var repeat_pay_flag=false;
//渲染模板引擎
layui.use(['laytpl','form'], function(){
    laytpl = layui.laytpl;
    form = layui.form;
	form.render();
});
/**
 * 订单操作
 * @param fun
 * @param order_data
 */
function orderAction(fun, order_id){
    eval(fun+"("+order_id+")");
}

function checkorder(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/getOrderInfo"),
        async : true,
        data : {order_id : order_id},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                if (res.data.pay_status == 1) {
                    layer.msg('支付成功');
                    location.reload();
                }

            } else {
                layer.msg(res.message);
            }
        }
    })
}
/**
 * 订单支付二维码
 **/
function orderPay(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/getOrderInfo"),
        async : true,
        data : {order_id : order_id, order_money :100},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                if (res.data.pay_weixin && res.data.pay_aliay){
                    var html = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta content="telephone=no, address=no" name="format-detection"/>' +
                        '<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/> ' +
                        '<meta name="apple-mobile-web-app-capable" content="yes"/> ' +
                        '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/> ' +
                        '<title>订单支付</title> ' +
                        '<link href="/public/static/css/main.css" rel="stylesheet"/></head> ' +
                        '<body> ' +
                        '<div class="p-header" style=" background-color: white;"> ' +
                        '<div class="w"> ' +
                        '<div id="logo"> ' +
                        '</div> ' +
                        '</div> ' +
                        '</div> ' +
                        '<div class="main" style="background-color:white"> ' +
                        '<div class="w"> ' +
                        '<div class="order"> ' +
                        '<div class="o-left"> ' +
                        '<h3 class="o-title">您的处方单产品来自两个海关，需分别支付。以便处方尽快处理！处方号： '+ res.data.order_id+'</h3> '  +
                        '</div> ' +
                        '<div class="o-right"> ' +
                        '<div class="o-price"> ' +
                        '</div> ' +
                        '</div> <div class="clr"></div> </div> ' +
                        '<div class="payment"> ' +
                        '<div class="pay-wechat" style="float: left;width: 50%;"> ' +
                        '<div class="p-w-hd">分装订单支付</div> ' +
                        '<div class="p-w-bd" style="padding-left:80px;"> ' +
                        '<div class="p-w-box"> ' +
                        '<div class="pw-box-hd"> ' +
                        '<img alt="模式二扫码支付" src="' + res.data.pay_weixin +
                        '" style="width:298px;height:298px;"/> ' +
                        '</div> ' +
                        '<div class="pw-box-ft"> <p>请使用微信扫一扫</p> <p>扫描二维码支付</p> ' +
                        '</div> ' +
                        '</div> ' +
                        '</div></div><div class="pay-wechat" style="float: left;width: 50%;"><div class="p-w-hd">整瓶订单支付</div> <div class="p-w-bd" style="padding-left:80px;"> <div class="p-w-box"> <div class="pw-box-hd"> ' +
                        '<img alt="模式二扫码支付" src="' + res.data.pay_aliay +'" style="width:298px;height:298px;"/> ' +
                        '</div> <div class="pw-box-ft"> <p>请使用微信扫一扫</p> <p>扫描二维码支付</p> </div> </div> </div> </div>' +
                        '</div> </div> </div> ' +
                        '</body> </html>';
                }

                if (!res.data.pay_weixin && res.data.pay_aliay){
                    var html = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta content="telephone=no, address=no" name="format-detection"/>' +
                        '<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/> ' +
                        '<meta name="apple-mobile-web-app-capable" content="yes"/> ' +
                        '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/> ' +
                        '<title>订单支付</title> ' +
                        '<link href="/public/static/css/main.css" rel="stylesheet"/></head> ' +
                        '<body> ' +
                        '<div class="p-header" style=" background-color: white;"> ' +
                        '<div class="w"> ' +
                        '<div id="logo"> ' +
                        '</div> ' +
                        '</div> ' +
                        '</div> ' +
                        '<div class="main" style="background-color:white"> ' +
                        '<div class="w"> ' +
                        '<div class="order"> ' +
                        '<div class="o-left"> ' +
                        '<h3 class="o-title">请尽快支付。以便处方尽快处理！处方号： '+ res.data.order_id+'</h3> '  +
                        '</div> ' +
                        '<div class="o-right"> ' +
                        '<div class="o-price"> ' +
                        '</div> ' +
                        '</div> <div class="clr"></div> </div> ' +
                        '<div class="payment"> ' +
                        '<div class="pay-wechat" style="width: 100%;"><div class="p-w-hd">整瓶订单支付</div> <div class="p-w-bd" style="padding-left:80px;"> <div class="p-w-box"> <div class="pw-box-hd"> ' +
                        '<img alt="模式二扫码支付" src="' + res.data.pay_aliay +'" style="width:298px;height:298px;"/> ' +
                        '</div> <div class="pw-box-ft"> <p>请使用微信扫一扫</p> <p>扫描二维码支付</p> </div> </div> </div> </div>' +
                        '</div> </div> </div> ' +
                        '</body> </html>';
                }

                if (res.data.pay_weixin && !res.data.pay_aliay){
                    var html = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta content="telephone=no, address=no" name="format-detection"/>' +
                        '<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/> ' +
                        '<meta name="apple-mobile-web-app-capable" content="yes"/> ' +
                        '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/> ' +
                        '<title>订单支付</title> ' +
                        '<link href="/public/static/css/main.css" rel="stylesheet"/></head> ' +
                        '<body> ' +
                        '<div class="p-header" style=" background-color: white;"> ' +
                        '<div class="w"> ' +
                        '<div id="logo"> ' +
                        '</div> ' +
                        '</div> ' +
                        '</div> ' +
                        '<div class="main" style="background-color:white"> ' +
                        '<div class="w"> ' +
                        '<div class="order"> ' +
                        '<div class="o-left"> ' +
                        '<h3 class="o-title">请尽快支付。以便处方尽快处理！处方号： '+ res.data.order_id+'</h3> '  +
                        '</div> ' +
                        '<div class="o-right"> ' +
                        '<div class="o-price"> ' +
                        '</div> ' +
                        '</div> <div class="clr"></div> </div> ' +
                        '<div class="payment"> ' +
                        '<div class="pay-wechat" style="float: left;width: 50%;"> ' +
                        '<div class="p-w-hd">分装订单支付</div> ' +
                        '<div class="p-w-bd" style="padding-left:80px;"> ' +
                        '<div class="p-w-box"> ' +
                        '<div class="pw-box-hd"> ' +
                        '<img alt="模式二扫码支付" src="' + res.data.pay_weixin +
                        '" style="width:298px;height:298px;"/> ' +
                        '</div> ' +
                        '<div class="pw-box-ft"> <p>请使用微信扫一扫</p> <p>扫描二维码支付</p> ' +
                        '</div> ' +
                        '</div> ' +
                        '</div></div>' +
                        '</div> </div> </div> ' +
                        '</body> </html>';
                }


                setInterval("checkorder("+res.data.order_id+")", 3000);
                var layerIndex = layer.open({
                    title: '订单支付',
                    skin: 'layer-tips-class',
                    type: 1,
                    area: ['1100px', '630px'],
                    content: html,
                });
            } else {
                layer.msg(res.message);
            }
        }
    })
}

function orderBiaoqian(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/printGood"),
        async : true,
        data : {order_id : order_id},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                // 自定义弹窗内容
                var html = '<div class="remark-container" style="padding: 20px;">' +
                    '<div class="form-group">' +
                    '   <label style="display: block; margin-bottom: 5px;">客户姓名：</label>' +
                    '   <input type="text" id="kehu" class="layui-input" placeholder="客户姓名" value="' + (res.data.kehu || '') + '">' +
                    '</div>' +
                    '<div class="form-group">' +
                    '   <label style="display: block; margin-bottom: 5px;">调理时间(天)：</label>' +
                    '   <input type="text" id="remark-extra" class="layui-input" placeholder="调理时间" value="' + (res.data.remark_extra || '') + '">' +
                    '</div>' +
                    '<div class="form-group">' +
                    '   <label style="display: block; margin-bottom: 5px;">定制时间：</label>' +
                    '   <input type="text" id="remark-date" class="layui-input" placeholder="定制时间" value="' + (res.data.create_time || '') + '" style="margin-bottom: 15px;">' +
                    '</div>' +
                    '</div>';
                // 使用layer.open自定义弹窗
                var index = layer.open({
                    type: 1,
                    title: '封口单设置',
                    content: html,
                    area: ['500px', '400px'],
                    btn: ['确定', '取消'],
                    shadeClose: true,
                    success: function(layero, index) {
                        // 初始化日期选择器
                        layui.use('laydate', function() {
                            var laydate = layui.laydate;
                            laydate.render({
                                elem: '#remark-date',
                                type: 'date',
                                format: 'yyyy-MM-dd',
                                value: res.data.create_time || new Date()
                            });
                        });
                    },
                    yes: function(index, layero) {
                        var date = $('#remark-date').val();
                        var extra = $('#remark-extra').val();
                        var kehu = $('#kehu').val();

                        if (!date) {
                            layer.msg('请选择定制时间');
                            return false;
                        }

                        var redirectUrl = ns.url("shop/order/printgood") + "?order_id=" + order_id+"&biaoqiandate="+date+"&biaoqiantiaoli="+extra+"&kehu="+kehu ;
                        window.open(redirectUrl, '_blank');
                    },
                    cancel: function() {
                        var redirectUrl = ns.url("shop/order/printgood") + "?order_id="+ order_id;
                        window.open(redirectUrl, '_blank');
                    }
                });
            } else {
                layer.msg(res.message);
            }
        }
    })
}

function orderFahuo(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/printOrder"),
        async : true,
        data : {order_id : order_id},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                // 自定义弹窗内容
                var html = '<div class="remark-container" style="padding: 20px;">' +
                    '<div class="form-group">' +
                    '   <label style="display: block; margin-bottom: 5px;">调理时间(天)：</label>' +
                    '   <input type="text" id="remark-extra" class="layui-input" placeholder="调理时间" value="' + (res.data.remark_extra || '') + '">' +
                    '</div>' +
                    '</div>';
                // 使用layer.open自定义弹窗
                var index = layer.open({
                    type: 1,
                    title: '发货单设置',
                    content: html,
                    area: ['500px', '400px'],
                    btn: ['确定', '取消'],
                    shadeClose: true,
                    success: function(layero, index) {

                    },
                    yes: function(index, layero) {
                        var extra = $('#remark-extra').val();
                        var redirectUrl = ns.url("shop/order/printOrder") + "?order_id=" + order_id+"&biaoqiantiaoli="+extra ;
                        window.open(redirectUrl, '_blank');
                    },
                    cancel: function() {
                        var redirectUrl = ns.url("shop/order/printOrder") + "?order_id="+ order_id;
                        window.open(redirectUrl, '_blank');
                    }
                });
            } else {
                layer.msg(res.message);
            }
        }
    })
}

function orderShuoming(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/printOrderr"),
        async : true,
        data : {order_id : order_id},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                // 自定义弹窗内容
                var html = '<div class="remark-container" style="padding: 20px;">' +
                    '<div class="form-group">' +
                    '   <label style="display: block; margin-bottom: 5px;">调理时间(天)：</label>' +
                    '   <input type="text" id="remark-extra" class="layui-input" placeholder="调理时间" value="' + (res.data.remark_extra || '') + '">' +
                    '</div>' +
                    '</div>';
                // 使用layer.open自定义弹窗
                var index = layer.open({
                    type: 1,
                    title: '说明单设置',
                    content: html,
                    area: ['500px', '400px'],
                    btn: ['确定', '取消'],
                    shadeClose: true,
                    success: function(layero, index) {
                    },
                    yes: function(index, layero) {
                        var extra = $('#remark-extra').val();

                        var redirectUrl = ns.url("shop/order/printOrderr") + "?order_id=" + order_id+"&biaoqiantiaoli="+extra;
                        window.open(redirectUrl, '_blank');
                    },
                    cancel: function() {
                        var redirectUrl = ns.url("shop/order/printOrderr") + "?order_id="+ order_id;
                        window.open(redirectUrl, '_blank');
                    }
                });
            } else {
                layer.msg(res.message);
            }
        }
    })
}

/**
 *订单备注
 **/
function orderRemark(order_id){
    $.ajax({
        type : "post",
        url : ns.url("shop/order/getOrderInfo"),
        async : true,
        data : {order_id : order_id},
        dataType: 'json',
        success : function(res) {
            if (res.code == 0) {
                if (res.data.doctor == 1 && res.data.order_status > 0){
                    layer.msg('订单配货中，增加备注需联系管理员', {time: 2000, icon: 5});
                    return;
                }
                if (res.data.doctor != 1){
                    res.data.remark = res.data.remark_mai;
                }
                layer.prompt({
                    formType: 2,
                    value: res.data.remark,
                    title: '备注',
                    area: ['400px', '100px'], //自定义文本域宽高
                    yes: function(index, layero){
                        var value = layero.find(".layui-layer-input").val();
                        if(value.trim().length == 0){
                            layer.msg("请填写备注！");
                            return false;
                        }
                        $.ajax({
                            type: "post",
                            url: ns.url("shop/order/orderRemark"),
                            async: true,
                            dataType: 'json',
                            data: {order_id : order_id, remark : value},
                            success: function (res) {
                                layer.msg(res.message, {}, function () {
                                    if (res.code == 0) {
                                        layer.close(index);
                                        location.reload();
                                    }
                                });
                            }
                        })
                    }
                });
            } else {
                layer.msg(res.message);
            }
        }
    })
}

/**
 * 关闭订单
 * @param order_id
 */
function orderClose(order_id){
	layer.confirm('确定要关闭该订单吗?', function() {
		$.ajax({
			url: ns.url("shop/order/close"),
			data: {order_id : order_id},
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				layer.msg(res.message);
				
				if (res.code == 0) {
					location.reload();
				}
			}
		});
	}, function () {
		layer.close();
	});
}

/**
 * 关闭订单
 * @param order_id
 */
function orderImage(order_id){
    layer.confirm('确定要删除该照片吗?', function() {
        $.ajax({
            url: ns.url("shop/order/orderImage"),
            data: {order_id : order_id},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);

                if (res.code == 0) {
                    location.reload();
                }
            }
        });
    }, function () {
        layer.close();
    });
}


/**
* 删除订单
* @param order_id
*/
function offlinePay(order_id){
    var repeat_flag = false;
    if (repeat_pay_flag){
        layer.msg('请不要重复支付');
    }
    layer.confirm('确定线下支付该订单吗?，请认真核对订单信息，线下支付完成后无法修改订单信息', function() {
        if(!repeat_flag) {
            repeat_flag = true;
            repeat_pay_flag = true;
            $.ajax({
                url: ns.url("shop/order/offlinePay"),
                data: {order_id: order_id},
                dataType: 'JSON',
                type: 'POST',
                success: function (res) {
                    repeat_pay_flag = false;
                    layer.msg(res.message);

                    if (res.code == 0) {
                        location.reload();
                    }
                }
            });
        }
    }, function () {
        layer.close();
    });
}
/**
 * 删除订单
 * @param order_id
 */
function orderDelete(order_id){
    var repeat_flag = false;
    layer.confirm('确定要删除该订单吗?', function() {
        if(!repeat_flag) {
            repeat_flag = true;
            $.ajax({
                url: ns.url("shop/order/delete"),
                data: {order_id: order_id},
                dataType: 'JSON',
                type: 'POST',
                success: function (res) {
                    layer.msg(res.message);

                    if (res.code == 0) {
                        location.reload();
                    }
                }
            });
        }
    }, function () {
        layer.close();
    });
}
</script>
<!-- 修改订单价格 -->
{include file="order/order_adjust_price" /}