<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印封口单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <div class="print-page">
        <div id="printarea">
            <style type="text/css">
                html,
                body {
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                }

                .shrink2 {
                    text-indent: 2em;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.7);
                }

                #maipage {
                    width: calc(100% - 20%);
                    padding: 90px 10% 20px 10%;
                    font-size: 20px;
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.8);
                    height:260mm;
                }

                .noshrink {
                    line-height: 1.6em;
                }

                .shrink2 {
                    text-indent: 2em;
                    line-height: 1.6em;
                }

                .shrink3 {
                    text-indent: 3em;
                    line-height: 1.6em;
                }

                .sectionbottom {
                    margin-bottom: 1.8em;
                }

                .textshrink {
                    margin-left: 20px;
                }

                .pagefot {
                    text-align: center;
                    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif;
                    font-size: 12px;
                }

                body {
                    background-color: white;
                }

                .w {
                    width: 1200px;
                    margin: 0 auto;
                    min-height: 1800px;
                }

                h3 {
                    padding-left: 5px;
                }

                .box1 {
                    /* width: 600px; */
                    width: 500px;
                    margin-left: -27px;
                    margin-top: 70px;
                }

                .box1>div {
                    width: 450px;
                    margin: 0 auto;
                }

                .box1 h1 {
                    text-align: center;
                    color: #1E1F1E;
                    font-size: 55px;
                }

                .box1 .tips {
                    color: #444443;
                    padding-top: 5px;
                    padding-bottom: 25px;
                    letter-spacing: 2.5px;
                }

                .box1 .arrange {
                    padding: 15px 10px;
                    border-radius: 15px;
                    margin-bottom: 60px;
                }

                .box1 .arrange .title {
                    color: #1B1B1B;
                    font-weight: 600;
                    font-size: 28px;
                }

                .box1 .arrange .tag {
                    color: #6C6D6C;
                    display: inline-block;
                    font-weight: 600;
                    height: 28px;
                    width: 270px;
                    margin-bottom: 10px;
                    font-size: 24px;
                }

                .box1 .details {
                    color: #1B1B1B;
                    font-size: 30px;
                    font-weight: 600;
                    border-bottom: 3px solid #1B1B1B;
                    padding-bottom: 7px;
                }

                .directions {
                    /* padding-bottom: 58px; */
                }

                .directions h1 {
                    text-align: left;
                }

                .directions .step {
                    overflow: hidden;
                    padding: 20px 0 25px 0;
                }

                .directions .step>div {
                    float: left;
                    width: 30.333333%;
                    padding: 7px;
                    text-align: center;
                }

                .directions .step img {
                    width: 100%;
                    padding-bottom: 15px;
                }

                .directions .step i {
                    font-style: normal;
                    font-size: 25px;
                    font-weight: 600;
                    color: #5E5E5D;
                    padding-bottom: 15px;
                    display: inline-block;
                }

                .directions .step span {
                    display: inline-block;
                    width: 80%;
                    text-align: left;
                    color: #444443;
                    font-size: 14px;
                }

                .directions .key {
                    color: #444443;
                    font-size: 14px;
                    padding-bottom: 107px;
                }

                .notes .item {
                    background-color: #DBDADA;
                    border-radius: 15px;
                    padding: 15px 5px;
                }

                .notes .item ul {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }

                .notes .item ul li {
                    display: flex;
                    padding: 0 0 15px 0;
                    font-size: 14px;
                }

                .notes .item img {
                    width: 25px;
                    padding: 5px 15px 5px 5px;
                }


                /* 右边的盒子 */

                .box2 {
                    width: 600px;
                    /* width: 578px; */
                    padding-left: 17px;
                }

                .morning {
                    margin-top: 30px;
                    border: 1px solid #535354;
                    border-radius: 15px 15px 0 0;
                    padding: 15px;
                    overflow: hidden;
                }

                .breakfast,
                .dinner {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    padding: 15px;
                    overflow: hidden;
                }

                .bedtime {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    border-radius: 0 0 15px 15px;
                    padding: 15px;
                    overflow: hidden;
                }

                .morning .text {
                    background-color: #CAA723;
                }

                .breakfast .text {
                    background-color: #C291A8;
                }

                .dinner .text {
                    background-color: #BD9E2B;
                }

                .bedtime .text {
                    background-color: #C190A7;
                }

                .box2>div>div {
                    float: left;
                }

                .title-lelt {
                    width: 96px;
                }

                .title-lelt span {
                    font-size: 23px;
                    letter-spacing: 5px;
                }

                .title-lelt i {
                    font-size: 10px;
                    font-style: normal;
                }

                .title-lelt img {
                    width: 100%;
                    padding-top: 15px;
                }

                .title-right {
                    width: 450px;
                    padding-left: 20px;
                }

                .title-right>span {
                    display: inline-block;
                    padding-bottom: 5px;
                    padding-left: 7px;
                }

                .title-right .list {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                    display: flex;
                    flex-wrap: wrap;
                }

                .list>li {
                    width: 30.333333%;
                    padding: 5px;
                }

                .list .text {
                    color: white;
                    height: 85px;
                    border-radius: 5px 5px 0px 0px;
                    text-align: center;
                }

                .list .text span {
                    font-size: 14px;
                }

                .list .text span:nth-child(1) {
                    display: inline-block;
                    padding-top: 15px;
                }

                .list .img {
                    background-color: white;
                    color: #999797;
                    height: 65px;
                    border: 1px solid #999797;
                    border-top: 0px;
                    border-radius: 0 0 5px 5px;
                    text-align: center;
                    font-weight: 600;
                }

                .list .img img {
                    width: 50px;
                    padding-top: 10px;
                    height: 25px;
                }
            </style>
            <div class="w">
                <div class="content">
                    <div class="box1" style="">
                        <div class="details">
                            <span>{$order_detail.name}</span>
                        </div>
                        <div class="arrange">
                            <div>
                                <span class="title">客户编码：</span>
                                <span class="tag">VIP{$timee}{$order_detail.member_id}</span><br/>
                                <span class="title">处方编码：</span>
                                <span class="tag">RXcp{$order_detail.order_no}</span><br/>
                                <span class="title">调理时间：</span>
                                <span class="tag" >{$attr_class_info.day}天</span><br/>
                                <span class="title">定制时间：</span>
                                <span class="tag">{$order_detail.create_time|date="Y-m-d"}</span><br/>
                                <span class="title" style="font-size: 26px">最佳使用时间：自分包时间起4个月</span><br/>
                                <span class="title" style="font-family: cursive">服用过程中如有不适情况，请及时与您的专属健康管理师联系，感谢您的配合！</span>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });
    });
</script>
</html>