<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印处方单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <div class="print-page">
        <div id="printarea">
            <style type="text/css">
                html,
                body {
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                }

                .shrink2 {
                    text-indent: 2em;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.7);
                }

                #maipage {
                    width: calc(100% - 20%);
                    padding: 90px 10% 20px 10%;
                    font-size: 20px;
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.8);
                    height:260mm;
                }

                .noshrink {
                    line-height: 1.6em;
                }

                .shrink2 {
                    text-indent: 2em;
                    line-height: 1.6em;
                }

                .shrink3 {
                    text-indent: 3em;
                    line-height: 1.6em;
                }

                .sectionbottom {
                    margin-bottom: 1.8em;
                }

                .textshrink {
                    margin-left: 20px;
                }

                .pagefot {
                    text-align: center;
                    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif;
                    font-size: 12px;
                }

                body {
                    background-color: white;
                }

                .w {
                    width: 1200px;
                    margin: 0 auto;
                    min-height: 1800px;
                }

                .content {
                    display: flex;
                }

                h3 {
                    padding-left: 5px;
                }

                .box1 {
                    /* width: 600px; */
                    width: 500px;
                    margin-left: 30px;
                }

                .box1>div {
                    width: 470px;
                    margin: 0 auto;
                }

                .box1 h1 {
                    text-align: center;
                    color: #1E1F1E;
                    font-size: 55px;
                }

                .box1 .tips {
                    color: #444443;
                    padding-top: 5px;
                    padding-bottom: 25px;
                    letter-spacing: 2.5px;
                }

                .box1 .arrange {
                    background-color: #E3EBF7;
                    padding: 15px 10px;
                    border-radius: 15px;
                    margin-bottom: 60px;
                }

                .box1 .arrange .title {
                    color: #1B1B1B;
                    font-weight: 600;
                }

                .box1 .arrange .tag {
                    color: #6C6D6C;
                    display: inline-block;
                    border-bottom: 1px solid #C5C8D1;
                    font-weight: 600;
                    height: 28px;
                    width: 270px;
                    margin-bottom: 20px;
                }

                .box1 .details {
                    color: #1B1B1B;
                    font-size: 14px;
                    font-weight: 600;
                    border-bottom: 1px solid #ABA5A1;
                    padding-bottom: 7px;
                }

                .directions {
                    /* padding-bottom: 58px; */
                }

                .directions h1 {
                    text-align: left;
                }

                .directions .step {
                    overflow: hidden;
                    padding: 20px 0 25px 0;
                }

                .directions .step>div {
                    float: left;
                    width: 30.333333%;
                    padding: 7px;
                    text-align: center;
                }

                .directions .step img {
                    width: 100%;
                    padding-bottom: 15px;
                }

                .directions .step i {
                    font-style: normal;
                    font-size: 25px;
                    font-weight: 600;
                    color: #5E5E5D;
                    padding-bottom: 15px;
                    display: inline-block;
                }

                .directions .step span {
                    display: inline-block;
                    width: 80%;
                    text-align: left;
                    color: #444443;
                    font-size: 14px;
                }

                .directions .key {
                    color: #444443;
                    font-size: 14px;
                    padding-bottom: 107px;
                }

                .notes .item {
                    background-color: #DBDADA;
                    border-radius: 15px;
                    padding: 15px 5px;
                }

                .notes .item ul {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }

                .notes .item ul li {
                    display: flex;
                    padding: 0 0 15px 0;
                    font-size: 14px;
                }

                .notes .item img {
                    width: 25px;
                    padding: 5px 15px 5px 5px;
                }


                /* 右边的盒子 */

                .box2 {
                    width: 600px;
                    /* width: 578px; */
                    padding-left: 17px;
                }

                .morning {
                    margin-top: 30px;
                    border: 1px solid #535354;
                    border-radius: 15px 15px 0 0;
                    padding: 15px;
                    overflow: hidden;
                }

                .breakfast,
                .dinner {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    padding: 15px;
                    overflow: hidden;
                }

                .bedtime {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    border-radius: 0 0 15px 15px;
                    padding: 15px;
                    overflow: hidden;
                }

                .morning .text {
                    background-color: #CAA723;
                }

                .breakfast .text {
                    background-color: #C291A8;
                }

                .dinner .text {
                    background-color: #BD9E2B;
                }

                .bedtime .text {
                    background-color: #C190A7;
                }

                .box2>div>div {
                    float: left;
                }

                .title-lelt {
                    width: 96px;
                }

                .title-lelt span {
                    font-size: 23px;
                    letter-spacing: 5px;
                }

                .title-lelt i {
                    font-size: 10px;
                    font-style: normal;
                }

                .title-lelt img {
                    width: 100%;
                    padding-top: 15px;
                }

                .title-right {
                    width: 450px;
                    padding-left: 20px;
                }

                .title-right>span {
                    display: inline-block;
                    padding-bottom: 5px;
                    padding-left: 7px;
                }

                .title-right .list {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                    display: flex;
                    flex-wrap: wrap;
                }

                .list>li {
                    width: 30.333333%;
                    padding: 5px;
                }

                .list .text {
                    color: white;
                    height: 85px;
                    border-radius: 5px 5px 0px 0px;
                    text-align: center;
                }

                .list .text span {
                    font-size: 14px;
                }

                .list .text span:nth-child(1) {
                    display: inline-block;
                    padding-top: 15px;
                }

                .list .img {
                    background-color: white;
                    color: #999797;
                    height: 65px;
                    border: 1px solid #999797;
                    border-top: 0px;
                    border-radius: 0 0 5px 5px;
                    text-align: center;
                    font-weight: 600;
                }

                .list .img img {
                    width: 50px;
                    padding-top: 10px;
                    height: 25px;
                }
            </style>
            <div class="w">
                <div class="content">
                    <div class="box1">
                        <h1>Dear, {$order_detail.name}</h1>
                        <div class="tips">
                            <p>&emsp;&emsp;请您详细查阅以下资料以悉知每份餐包营养素内容及</p>
                            <p>服用时间。服用过程中如遇各种问题请与您的专属医生及</p>
                            <p>健康管理师联络。</p>
                        </div>
                        <div class="arrange">
                            <div>
                                <span class="title">本期营养素干预重点：</span>
                                <span class="tag" {if !$attr_class_info['zhongdian']}style="color:transparent;"{/if}>{if !$attr_class_info['zhongdian']}无{/if}{$attr_class_info.zhongdian}</span><br/>
                                <span class="title">本期营养素服用时间：</span>
                                <span class="tag" {if !$time}style="color:transparent;"{/if}>{if !$time}无{/if}{$time}</span><br/>
                                <span class="title">总体营养素干预进程：</span>
                                <span class="tag" >{$attr_class_info.progress}({$attr_class_info.day}天)</span><br/>
                                <span class="title">您的专属健康管理师：</span>
                                <span class="tag" {if !$attr_class_info['jiankangguanli']}style="color:transparent;"{/if}>{if !$attr_class_info['jiankangguanli']}无{/if}{$attr_class_info.jiankangguanli}</span>
                            </div>
                        </div>
                        <div class="details">
                            <span>营养素详细信息请参考包装内产品信息说明</span>
                        </div>
                        <div class="directions">
                            <h3>DIRECTIONS<br/>使用指南</h3>
                            <div class="step">
                                <div>
                                    <i>1</i>
                                    <img src="SHOP_IMG/print/1.png">
                                    <span>从盒子侧面开口中拉出营养素餐包</span>
                                </div>
                                <div>
                                    <i>2</i>
                                    <img src="SHOP_IMG/print/2.png">
                                    <span>沿线将需要的餐包撕下</span>
                                </div>
                                <div>
                                    <i>3</i>
                                    <img src="SHOP_IMG/print/3.png">
                                    <span>从餐包连接处撕开</span>
                                </div>
                            </div>
                            <div class="key">
                                <span>*本品为个性化定制产品，内含干燥剂。最佳使用日期为2个月，开封后请尽快服用。</span>
                            </div>
                        </div>
                        <div class="notes">
                            <h3>NOTES<br/>注意事项</h3>
                            <div class="item">
                                <ul>
                                    <li>
                                        <div><img src="SHOP_IMG/print/11.png"></div>
                                        <div>本品仅限个人服用，请勿转赠他人。请放置于安全位置，勿让孩童接触。</div>
                                    </li>
                                    <li>
                                        <div><img src="SHOP_IMG/print/22.png"></div>
                                        <div>本品为独立包装，如发现包装破损、产品异常等问题请勿服用。</div>
                                    </li>
                                    <li>
                                        <div><img src="SHOP_IMG/print/33.png"></div>
                                        <div>服用后如有不适反应，请及时联系您的专属健康管理师或医生。</div>
                                    </li>
                                    <li>
                                        <div><img src="SHOP_IMG/print/55.png"></div>
                                        <div>孕妇、哺乳期妇女、正在服用药物等特殊情况，请一定与您的医生沟通确认，在医生的指导下使用本品。</div>
                                    </li>
                                    <li>
                                        <div><img src="SHOP_IMG/print/66.png"></div>
                                        <div>本产品需在干燥、阴凉的室温下(15°C~25°C) 保存，如无特殊说明，无需放置冰箱。</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="box2">
                        {if condition="$earlymoning"}
                        <div class="morning" {if !$evening && !$night && !$aftnoon && !$moning && !$canjian} style="border-bottom-right-radius:15px;border-bottom-left-radius:15px;" {/if}>
                            <div class="title-lelt">
                                <span>晨起包</span><br/>
                                <i>Morning Pcak</i>
                                <div>
                                    <img src="SHOP_IMG/print/morning.png">
                                </div>
                            </div>
                            <div class="title-right">
                                <span>{$attr_class_info['earlymoning_time']}</span>
                                <ul class="list">
                                    {foreach $earlymoning as $k => $vo}
                                    <li>
                                        <div class="text">
                                            <span>{$vo['goods_chi']}</span><br/>
                                            <span>{$vo['sku_name']}</span>
                                        </div>
                                        <div class="img" style="border: 1px solid #CAA723;">
                                            <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                            <div>{$vo['num']}粒/日</div>
                                        </div>
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        {/if}
                        {if condition="$moning"}
                        <div class="breakfast"  style="{if !$earlymoning}border-top-right-radius:15px;border-top-left-radius:15px;{/if}{if !$evening && !$night && !$aftnoon && !$canjian}border-bottom-right-radius:15px;border-bottom-left-radius:15px; {/if}">
                            <div class="title-lelt">
                                <span>早餐包</span><br/>
                                <i>Breakfast Pcak</i>
                                <div>
                                    <img src="SHOP_IMG/print/breakfast.png">
                                </div>
                            </div>
                            <div class="title-right">
                                <span>{$attr_class_info['moning_time']}</span>
                                <ul class="list">
                                    {foreach $moning as $k => $vo}
                                    <li>
                                        <div class="text">
                                            <span>{$vo['goods_chi']}</span><br/>
                                            <span>{$vo['sku_name']}</span>
                                        </div>
                                        <div class="img"  style="border: 1px solid #C291A8;">
                                            <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                            <div>{$vo['num']}粒/日</div>
                                        </div>
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        {/if}
                    {if condition="$canjian"}
                    <div class="bedtime"  style="{if !$earlymoning && !$moning}border-top-right-radius:15px;border-top-left-radius:15px;{/if} {if !$evening && !$night && !$aftnoon}border-bottom-right-radius:15px;border-bottom-left-radius:15px;{/if}" >
                        <div class="title-lelt">
                            <span>餐间包</span><br/>
                            <i>Food Pack</i>
                            <div>
                                <img src="SHOP_IMG/print/canjian.png" style="width: 100px;height:70px;">
                            </div>
                        </div>
                        <div class="title-right">
                            <span>{$attr_class_info['canjian_time']}</span>
                            <ul class="list">
                                {foreach $canjian as $k => $vo}
                                <li>
                                    <div class="text">
                                        <span>{$vo['goods_chi']}</span><br/>
                                        <span>{$vo['sku_name']}</span>
                                    </div>
                                    <div class="img"  style="border: 1px solid #BD9E2B;">
                                        <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                        <div>{$vo['num']}粒/日</div>
                                    </div>
                                </li>
                                {/foreach}
                            </ul>
                        </div>
                    </div>
                    {/if}
                        {if condition="$aftnoon"}
                        <div class="dinner"  style="{if !$earlymoning && !$moning && !$canjian}border-top-right-radius:15px;border-top-left-radius:15px;{/if} {if !$evening && !$night}border-bottom-right-radius:15px;border-bottom-left-radius:15px;{/if}" >
                            <div class="title-lelt">
                                <span>午餐包</span><br/>
                                <i>Lunch Pack</i>
                                <div>
                                    <img src="SHOP_IMG/print/package3.png" style="width: 100px;height:70px;">
                                </div>
                            </div>
                            <div class="title-right">
                                <span>{$attr_class_info['aftnoon_time']}</span>
                                <ul class="list">
                                    {foreach $aftnoon as $k => $vo}
                                    <li>
                                        <div class="text">
                                            <span>{$vo['goods_chi']}</span><br/>
                                            <span>{$vo['sku_name']}</span>
                                        </div>
                                        <div class="img"  style="border: 1px solid #BD9E2B;">
                                            <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                            <div>{$vo['num']}粒/日</div>
                                        </div>
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        {/if}
                        {if condition="$night"}
                        <div class="dinner"   style="{if !$earlymoning && !$moning && !$aftnoon && !$canjian}border-top-right-radius:15px;border-top-left-radius:15px;{/if}  {if !$evening}border-bottom-right-radius:15px;border-bottom-left-radius:15px;{/if}" >
                            <div class="title-lelt">
                                <span>晚餐包</span><br/>
                                <i>Dinner Pcak</i>
                                <div>
                                    <img src="SHOP_IMG/print/dinner.png">
                                </div>
                            </div>
                            <div class="title-right">
                                <span>{$attr_class_info['night_time']}</span>
                                <ul class="list">
                                    {foreach $night as $k => $vo}
                                    <li>
                                        <div class="text">
                                            <span>{$vo['goods_chi']}</span><br/>
                                            <span>{$vo['sku_name']}</span>
                                        </div>
                                        <div class="img"  style="border: 1px solid #BD9E2B;">
                                            <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                            <div>{$vo['num']}粒/日</div>
                                        </div>
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        {/if}
                        {if condition="$evening"}
                        <div class="bedtime" {if !$earlymoning && !$moning && !$aftnoon && !$night && !$canjian} style="border-top-right-radius:15px;border-top-left-radius:15px;" {/if} >
                            <div class="title-lelt">
                                <span>睡前包</span><br/>
                                <i>Bedtime Pcak</i>
                                <div>
                                    <img src="SHOP_IMG/print/bedtime.png">
                                </div>
                            </div>
                            <div class="title-right">
                                <span>{$attr_class_info['sleep_time']}</span>
                                <ul class="list">
                                    {foreach $evening as $k => $vo}
                                    <li>
                                        <div class="text">
                                            <span>{$vo['goods_chi']}</span><br/>
                                            <span>{$vo['sku_name']}</span>
                                        </div>
                                        <div class="img"  style="border: 1px solid #C190A7;">
                                            <div><img src="{:img($vo.sku_image)}" style="width: 50px;height:20px;"></div>
                                            <div>{$vo['num']}粒/日</div>
                                        </div>
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
            <div id="maipage">
                <div class="noshrink sectionbottom">
                    亲爱的客户：
                </div>
                <div class="shrink2">
                    感谢您与我们同行。
                </div>
                <div class="shrink2 sectionbottom">
                    祝贺您选择功能医学维护生命健康，
                    希望通过我们的专业指导和您的不懈努力，
                    通过营养矫正身体生理失衡，同时践行良好的生活饮食方式，重塑健康人生。
                </div>
                <div class="shrink2 sectionbottom">
                    健康不是没有疾病而是人体充满活力的状态，真诚祈望每个人都拥有健康且充满活力的高质量人生。
                </div>
                <div class="shrink2 sectionbottom">
                    谨此献给那些渴望更加健康、快乐、强壮、舒适和高效的人。
                </div>
                <div class="shrink3">
                    <span>By</span>
                    <span class="textshrink">你的健康管理团队</span>
                </div>
                <div class="shrink3">
                    <span>医生</span>
                    <span class="textshrink">{$doctor}</span>
                </div>
                <div class="shrink3">
                    <span>健康管理师</span>
                    <span class="textshrink">{$attr_class_info.jiankangguanli}</span>
                </div>
                <div class="shrink3 sectionbottom">
                    <span>日期</span>
                    <span class="textshrink">{$order_detail.create_time|date="Y年m月d日"}</span>
                </div>
                <div class="shrink3">
                    个性化营养执行团队
                </div>
                <div class="shrink3">
                    <span>药剂师</span>
                    <span class="textshrink">个性化营养素团队</span>
                </div>
                <div class="shrink3">
                    <span>营养师</span>
                    <span class="textshrink">个性化营养素团队</span>
                </div>
                <div class="shrink3 sectionbottom" style="margin-bottom: 3em;">
                    <span>日期</span>
                    <span class="textshrink">{$order_detail.create_time|date="Y年m月d日"}</span>
                </div>
                <div class="pagefot">
                    产品使用过程中如有任何疑问请联系您的健康管理师
                </div>
            </div>
        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });
    });
</script>
</html>