<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="__STATIC__/css/seller_center.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        body {
            background: #FFF none;
        }
    </style>
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印发货单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <div class="print-btn" id="printbtn" title="选择喷墨或激光打印机<br/>根据下列纸张描述进行<br/>设置并打印发货单据"><i></i><a href="javascript:void(0);">打印</a></div>
    <div class="a5-size"></div>
    <dl class="a5-tip">
        <dt>
            <h1>A5</h1>
            <em>Size: 210mm x 148mm</em></dt>
        <dd>当打印设置选择A5纸张、横向打印、无边距时每张A5打印纸可输出1页订单。</dd>
    </dl>
    <div class="a4-size"></div>
    <dl class="a4-tip">
        <dt>
            <h1>A4</h1>
            <em>Size: 210mm x 297mm</em></dt>
        <dd>当打印设置选择A4纸张、竖向打印、无边距时每张A4打印纸可输出2页订单。</dd>
    </dl>
    <div class="print-page">
        <div id="printarea">
            <div class="orderprint" style="width: 95%;">
                <div class="top" style="width: 100%;">
                    <div class="full-title">{$order_detail.site_name} 发货单</div>
                </div>
                <table class="buyer-info">
                    <tr>
                        <td class="w200">疗程:({$attr_class_info.day})天</td>
                        <td class="w200">机构名称:{$bp_name}</td>
                    </tr>
                    <tr>
                        <td>下单日期：{$order_detail.create_time|date="Y-m-d"}</td>
                        <td class="w200">客户姓名：{$order_detail.name}</td>
                        <td class="w200">电话:{$order_detail.mobile}</td>
                    </tr>

                    <tr>
                        <td class="w200">医生/健管师：{$attr_class_info.jiankangguanli}</td>
                        <td colspan="3">地址：{$order_detail.full_address} {$order_detail.address}</td>
                    </tr>

                </table>
                <table class="order-info">
                    <thead>
                    <tr>
                        <th class="w50">服用时间</th>
                        <th>品牌</th>
                        <th>名称</th>
                        <th class="w50">图片</th>
                        <th class="w50">用量</th>
                        <th class="w50">天数</th>
                        <th class="w70">总量</th>
                    </tr>
                    </thead>
                    <tbody>
                    {php}
                    $total_goods_num = 0;
                    {/php}
                    {if condition="$earlymoning"}
                    {php}
                    $chenqi_num = 0;
                    {/php}
                    {foreach $earlymoning as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $chenqi_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #aacbeb;">晨起包</td>
                        <td style="background-color: #aacbeb;">{$vo['category_name']}</td>
                        <td style="background-color: #aacbeb;">{$vo['sku_name']}</td>
                        <td style="background-color: #aacbeb;"><img src="{:img($vo.sku_image)}" style="width: 50px"></td>
                        <td style="background-color: #aacbeb;">{$vo['num']}粒</td>
                        <td style="background-color: #aacbeb;">{$attr_class_info.day}</td>
                        <td style="background-color: #aacbeb;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">晨起包合计</th>
                        <th></th>
                        <th>{$chenqi_num}</th>
                        <th></th>
                        <th></th>
                        <!--<th>{$chenqi_num*$attr_class_info['day']}</th>-->
                    </tr>
                    {/if}
                    {if condition="$moning"}
                    {php}
                    $zaocan_num = 0;
                    {/php}
                    {foreach $moning as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $zaocan_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #c6b5e5;">早餐包</td>
                        <td style="background-color: #c6b5e5;">{$vo['category_name']}</td>
                        <td style="background-color: #c6b5e5;">{$vo['sku_name']}</td>
                        <td style="background-color: #c6b5e5;"><img src="{:img($vo.sku_image)}" style="width: 50px"></td>
                        <td style="background-color: #c6b5e5;">{$vo['num']}粒</td>
                        <td style="background-color: #c6b5e5;">{$attr_class_info.day}</td>
                        <td style="background-color: #c6b5e5;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">早餐包合计</th>
                        <th></th>
                        <th>{$zaocan_num}</th>
                        <th></th>
                        <th></th>
                        <!--<th>{$zaocan_num*$attr_class_info['day']}</th>-->
                    </tr>
                    {/if}
                    {if condition="$canjian"}
                    {php}
                    $canjian_num = 0;
                    {/php}
                    {foreach $canjian as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $canjian_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #e1bda7;">餐间包</td>
                        <td style="background-color: #e1bda7;">{$vo['category_name']}</td>
                        <td style="background-color: #e1bda7;">{$vo['sku_name']}</td>
                        <td style="background-color: #e1bda7;"><img src="{:img($vo.sku_image)}" style="width: 50px;"></td>
                        <td style="background-color: #e1bda7;">{$vo['num']}粒</td>
                        <td style="background-color: #e1bda7;">{$attr_class_info.day}</td>
                        <td style="background-color: #e1bda7;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">餐间包合计</th>
                        <th></th>
                        <th>{$canjian_num}</th>
                        <th></th>
                        <th></th>
                        <!--<th>{$canjian_num*$attr_class_info['day']}</th>-->
                    </tr>
                    {/if}
                    {if condition="$aftnoon"}
                    {php}
                    $wucan_num = 0;
                    {/php}
                    {foreach $aftnoon as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $wucan_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #ffea64;">午餐包</td>
                        <td style="background-color: #ffea64;">{$vo['category_name']}</td>
                        <td style="background-color: #ffea64;">{$vo['sku_name']}</td>
                        <td style="background-color: #ffea64;"><img src="{:img($vo.sku_image)}" style="width: 50px;"></td>
                        <td style="background-color: #ffea64;">{$vo['num']}粒</td>
                        <td style="background-color: #ffea64;">{$attr_class_info.day}</td>
                        <td style="background-color: #ffea64;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">午餐包合计</th>
                        <th></th>
                        <th>{$wucan_num}</th>
                        <th></th>
                        <th></th>
                        <!--<th>{$wucan_num*$attr_class_info['day']}</th>-->
                    </tr>
                    {/if}


                    {if condition="$night"}
                    {php}
                    $wancan_num = 0;
                    {/php}
                    {foreach $night as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $wancan_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #96d4e1;">晚餐包</td>
                        <td style="background-color: #96d4e1;">{$vo['category_name']}</td>
                        <td style="background-color: #96d4e1;">{$vo['sku_name']}</td>
                        <td style="background-color: #96d4e1;"><img src="{:img($vo.sku_image)}" style="width: 50px;"></td>
                        <td style="background-color: #96d4e1;">{$vo['num']}粒</td>
                        <td style="background-color: #96d4e1;">{$attr_class_info.day}</td>
                        <td style="background-color: #96d4e1;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">晚餐包合计</th>
                        <th></th>
                        <th>{$wancan_num}</th>
                        <th></th>
                        <th></th>
                        <!--<th>{$wancan_num*$attr_class_info['day']}</th>-->
                    </tr>
                    {/if}
                    {if condition="$evening"}
                    {php}
                    $evening_num = 0;
                    {/php}
                    {foreach $evening as $k => $vo}
                    {php}
                    $total_goods_num += $vo['num'];
                    $evening_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #f7d1d8;">睡前包</td>
                        <td style="background-color: #f7d1d8;">{$vo['category_name']}</td>
                        <td style="background-color: #f7d1d8;">{$vo['sku_name']}</td>
                        <td style="background-color: #f7d1d8;"><img src="{:img($vo.sku_image)}" style="width: 50px;"></td>
                        <td style="background-color: #f7d1d8;">{$vo['num']}粒</td>
                        <td style="background-color: #f7d1d8;">{$attr_class_info.day}</td>
                        <td style="background-color: #f7d1d8;">{$vo['num']*$attr_class_info['day']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">睡前包合计</th>
                        <th></th>
                        <th>{$evening_num}</th>
                        <th></th>
                        <th></th>
                    </tr>
                    {/if}
                    {if condition="$teshu"}
                    {foreach $teshu as $k => $vo}
                    {php}
                    $teshu_num = 0;
                    {/php}
                    {php}
                    $total_goods_num += $vo['num'];
                    $teshu_num += $vo['num'];
                    {/php}
                    <tr>
                        <td style="background-color: #00AE72;">周转包</td>
                        <td style="background-color: #00AE72;">{$vo['category_name']}</td>
                        <td style="background-color: #00AE72;">{$vo['sku_name']}</td>
                        <td style="background-color: #00AE72;"><img src="{:img($vo.sku_image)}" style="width: 50px;"></td>
                        <td style="background-color: #00AE72;">{$vo['num']}</td>
                        <td style="background-color: #00AE72;"></td>
                        <td style="background-color: #00AE72;">{$vo['num']}</td>
                    </tr>
                    {/foreach}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">周转包合计</th>
                        <th></th>
                        <th>{$teshu_num}</th>
                        <th></th>
                        <th></th>
                    </tr>
                    {/if}
                    <tr>
                        <th></th>
                        <th colspan="2" class="tl">合计</th>
                        <th></th>
                        <th>{$total_goods_num}</th>
                        <th></th>
                        <th>{$total_goods_num*$attr_class_info['day']}</th>
                    </tr>
                    </tbody>
                    <tfoot>
                    <tr>
                        <th colspan="7"><span>执行:</span><span>核对：</span><span>执行日期:</span>
                    </tr>
                    <tr>
                        <th colspan="7"><span>二次查验:{$order_detail.remarkfenkou}</span>{if condition="$attr_class_info['fenji']"}<span>粉剂:{$order_detail.remarkfenji}</span>{/if}
                    </tr>
                    <tr>
                        <th colspan="7"><span style="width: 100%">买家备注:{$order_detail.remark}</span>
                    </tr>
                    <tr>
                        <th colspan="7"><span style="width: 100%">卖家备注:{$order_detail.remark_mai}</span>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });
    });
</script>
</html>