
{notempty name="order_detail"}
<div class="print-layout">
    <div class="print-page">
        <div id="printarea">
            <div class="orderprint" style="width: 95%;">
                <style type="text/css">
                    table
                    {
                        border-collapse:collapse;
                    }
                    table, td, th
                    {
                        border:1px solid #B4C6E7;
                        line-height:18px;
                        font-size:10px;
                        padding:5px;
                        color: #364A93;
                    }
                    .headert td{
                        font-size: 18px;
                    }
                    .shuli{ margin:0 auto;writing-mode:vertical-lr;letter-spacing:8px;}
                </style>
                {if $logo}
                <div style="width:100px;height:30px;">
                    <img src="/{$logo}" style="width:100%">
                </div>
                {/if}
                <div style="background:url(http://b.clinicsupplement.com/bei.png) no-repeat center top;height: 120px;width: 720px;background-size: 96% 90%;margin-left: 170px;">
                    <div style="font-size:0.80em; color: #364A93;margin-left:370px;font-weight: bold;"></div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:29px;font-weight: bold;">{$order_detail.name}</div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:6px;font-weight: bold;">{$attr_class_info.day}天</div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:5px;font-weight: bold;">{$order_detail.create_time|date="Y-m-d"}</div>
                </div>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        {if $visit[0] == 1}<td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>{/if}
                        {if $visit[1] == 1}<td width="10%" align="center" ><b>图片</b></td>{/if}
                        {if $visit[2] == 1}<td width="5%" align="center" > <b>剂量</b></td>{/if}
                        {if $visit[3] == 1}<td width="20%" align="center" ><b>主要成分</b></td>{/if}
                        {if $visit[4] == 1}<td width="50%" align="center" ><b>产品说明</b></td>{/if}
                    </tr>
                    </thead>
                    {if condition="$earlymoning"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($earlymoning);
                    {/php}
                    {foreach $earlymoning as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #aacbeb;"><p class="shuli">晨起包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                    {if condition="$moning"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($moning);
                    {/php}
                    {foreach $moning as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #c6b5e5;"><p class="shuli">早餐包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                    {if condition="$canjian"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($canjian);
                    {/php}
                    {foreach $canjian as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #e1bda7;"><p class="shuli">餐间包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                    {if condition="$aftnoon"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($aftnoon);
                    {/php}
                    {foreach $aftnoon as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #ffea64;"><p class="shuli">午餐包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                    {if condition="$night"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($night);
                    {/php}
                    {foreach $night as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #96d4e1;"><p class="shuli">晚餐包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                    {if condition="$evening"}
                    {php}
                    $chenqi_num = 0;
                    $count = count($evening);
                    {/php}
                    {foreach $evening as $k => $vo}
                    {php}
                    $chenqi_num = $chenqi_num+1;
                    {/php}
                    {if $chenqi_num == 1}
                    <tr>
                        <td rowspan="{$count}" align="center" style="background-color: #f7d1d8;"><p class="shuli">睡前包</p></td>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {else/}
                    <tr>
                        {if $visit[0] == 1}<td align="center">{if $visit[5] == 1}{$vo['sku_name']}<br />{/if}{$vo['goods_chi']}</td>{/if}
                        {if $visit[1] == 1}<td align="center"><img src="{:img($vo.sku_image)}" width="70px" ></td>{/if}
                        {if $visit[2] == 1}<td align="center">{$vo['num']}</td>{/if}
                        {if $visit[3] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>{/if}
                        {if $visit[4] == 1}<td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>{/if}
                    </tr>
                    {/if}
                    {/foreach}
                    {/if}
                </table>
            </div>
        </div>
    </div>
</div>
{/notempty}
