<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="__STATIC__/css/seller_center.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        body {
            background: #FFF none;
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        /* 页面预览容器样式 */
        #all-pages-container .page-preview:not(:first-child) .products-column {
            margin-top: 4mm;
        }

        /* 原始打印布局样式 */
        .print-layout {
            font-size:12px;
            background:#FAFAFA;
            border: solid 1px #CCC;
            position:relative;
            width:210mm;
            height:297mm;
            padding:5mm 50mm 5mm 5mm;
            margin: 20px auto;
            box-shadow: 2px 2px 2px rgba(204,204,204,0.5);
        }

        .print-layout .a5-size, .print-layout .a4-size {
            background:#FFF;
            border: dashed 1px #ccc;
            width: 210mm;
            position:absolute;
            top:5mm;
            left:5mm;
            padding:1px;
        }

        .print-layout .a5-size {
            height:148mm;
            z-index:2;
        }

        .print-layout .a4-size {
            height:148mm;
            z-index:1;
        }

        .print-layout .a5-tip, .print-layout .a4-tip{
            color:#333;
            width:37mm;
            position: absolute;
            z-index:2;
            right:8mm;
        }

        .print-layout .a5-tip {
            top:50mm;
        }

        .print-layout .a4-tip {
            top:160mm;
        }

        .print-layout dl dt h1 {
            font-family:"Arial Black", Gadget, sans-serif;
            font-size:72px;
            line-height:72px;
        }

        .print-layout dl dt em {
            font-family: Arial;
            font-size:11px;
            line-height:20px;
            background: #333;
            color: #FFF;
            padding: 0 8px;
            height:20px;
            border-radius:10px;
            -webkit-text-size-adjust:none;
        }

        .print-layout .a5-tip dd, .print-layout .a4-tip dd {
            line-height:24px;
        }

        .print-layout .print-page {
            width: 210mm;
            height:297mm;
            position:absolute;
            z-index:3;
            top:5mm;
            left:5mm;
            margin:1px;
            overflow:auto;
        }

        .orderprint {
            background: #FFFFFF;
            width: 188mm;
            height:100%;
            margin-bottom:unset !important;
            padding:0mm 8mm 0mm 10mm !important;
            color:#000000;
            position:relative;
        }

        /* 上半部分 - 头部信息区域 */
        .orderprint .header-section {
            height: 90mm;
            padding: 4mm 4mm 2mm 4mm;
            position: relative;
            background: #FFFFFF;
            display: flex;
        }

        /* 左侧信息栏 */
        .orderprint .header-left-column {
            flex: 1;
            padding-right: 6mm;
        }

        /* Logo区域 */
        .orderprint .logo-area {
            margin-bottom: 5mm;
        }

        .orderprint .logo-container {
            width: 34mm;
            margin-bottom: 10mm;
            padding-top: 2mm;
            padding-left: 2mm;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .orderprint .logo-container img {
            max-width: 100%;
            max-height: 100%;
        }

        /* 标题区域 */
        .orderprint .title-area {
            margin-bottom: 5mm;
            text-align: center;
        }

        .orderprint .main-title {
            font-size: 28px;
            font-weight: normal;
            color: #333;
            margin-bottom: 2mm;
        }

        .orderprint .customer-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 0;
        }

        /* 右侧指导栏 */
        .orderprint .header-right-column {
            flex: 1;
            padding-left: 2mm;
        }

        /* 订单信息区域 */
        .orderprint .order-info-area {
            margin-top: 5mm;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .orderprint .info-item {
            margin-bottom: 2mm;
            font-size: 12px;
            color: #333;
            text-align: left;
            width: 50mm;
        }

        .orderprint .info-label {
            font-weight: bold;
            display: inline-block;
            width: 18mm;
        }

        /* 使用指导区域 */
        .orderprint .usage-guide {
            /* height: 100%; */
        }

        .orderprint .usage-title {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3mm;
            padding-bottom: 2mm;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .orderprint .usage-title-right {
            font-size: 10px;
            font-weight: 500;
            color: #FFF9EF;
            background-color: #45172E;
            border-radius: 50px;
            padding: 2px 8px;
        }

        .orderprint .usage-content {
            width: 100%;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
        }

        .orderprint .usage-content ul {
            margin: 0;
            list-style: none;
        }

        .orderprint .usage-content li {
            margin-bottom: 2mm;
        }

        .orderprint .usage-content .title-span {
            font-weight: bold;
            color: #333;
        }

        /* 搜狗浏览器兼容性修复 - 仅在屏幕显示时生效 */
        @media screen {
            .sogou-browser .orderprint .header-section {
                min-height: 100mm !important;
                height: auto !important;
            }

            .sogou-browser .orderprint .header-right-column {
                min-height: 85mm;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
            }

            .sogou-browser .orderprint .usage-guide {
                margin-bottom: 3mm;
                flex-shrink: 0;
            }

            .sogou-browser .orderprint .usage-content {
                overflow: visible !important;
                max-height: none !important;
            }

            .sogou-browser .orderprint .separator-text-top {
                right: 0mm !important;
                width: 55mm !important;
            }

            .sogou-browser .orderprint .separator-text-bottom {
                right: 0mm !important;
                width: 55mm !important;
            }
        }

        /* 分隔区域 */
        .orderprint .separator-section {
            position: relative;
            height: 10mm;
            background: #FFFFFF;
            display: flex;
            align-items: center;
            padding-top: 10mm;
        }

        .orderprint .separator-line {
            position: absolute;
            left: 0;
            right: 0;
            top: 50%;
            height: 1px;
            background: #333;
            z-index: 1;
        }

        .orderprint .separator-text-top {
            position: absolute;
            right: 6mm;
            top: 4mm;
            font-size: 10px;
            color: #333;
            z-index: 2;
            text-align: left;
            width: 40mm;
        }

        .orderprint .separator-text-bottom {
            position: absolute;
            right: 6mm;
            bottom: 4mm;
            font-size: 10px;
            color: #333;
            z-index: 2;
            text-align: left;
            width: 40mm;
        }

        /* 多页预览容器 */
        #all-pages-container {
            background: #FFF9EF;
        }

        .page-preview {
            margin-bottom: 20mm;
            page-break-after: always;
            position: relative;
            min-height: 297mm; /* A4纸张高度 */
            width: 210mm; /* A4纸张宽度 */
            background: #FFFFFF;
            padding: 0;
        }

        .page-preview:last-child {
            margin-bottom: 0;
        }

        .page-preview .orderprint {
            height: 100%;
            min-height: 297mm;
        }



        /* 屏幕显示优化 */
        @media screen {
            .page-navigation {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 1000 !important;
                background: white !important;
                padding: 10px !important;
                border: 1px solid #ccc !important;
                border-radius: 5px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            }

            .page-navigation button {
                padding: 5px 10px !important;
                margin: 0 2px !important;
                border: 1px solid #ddd !important;
                background: #f8f9fa !important;
                cursor: pointer !important;
                border-radius: 3px !important;
            }

            .page-navigation button:hover {
                background: #e9ecef !important;
            }

            .page-navigation button:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
            }

            /* 页面预览间距 */
            .page-preview {
                border: 1px dashed #ccc;
                margin-bottom: 20px;
                position: relative;
            }

            .page-preview::before {
                content: "页面 " attr(data-page);
                position: absolute;
                top: -15px;
                left: 10px;
                background: white;
                padding: 2px 8px;
                font-size: 12px;
                color: #666;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
        }

        /* 下半部分 - 产品展示区域 */
        .orderprint .products-section, .page-preview .products-section {
            min-height: 148mm;
            display: flex;
            align-items: flex-start;
            background: #FFFFFF;
            counter-reset: product-counter;
        }

        /* 后续页面的产品区域有更多空间 */
        .page-preview .products-section.continuation-page {
            min-height: 240mm;
        }

        .orderprint .products-column {
            flex: 1;
            padding: 2mm;
            align-self: flex-start;
            /* border-right: 1px solid #E0E0E0; */
        }

        .orderprint .products-column:last-child {
            border-right: none;
        }

        .orderprint .column-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            text-align: left;
            margin-bottom: 2mm;
            margin-top: 4mm;
            padding-left: 2mm;
            padding-bottom: 1mm;
            position: relative;
            page-break-inside: avoid !important;
            break-inside: avoid !important;
            -webkit-column-break-inside: avoid !important;
            display: inline-block;
            width: 100%;
            box-sizing: border-box;
            overflow: visible;
        }

        /* 页面底部logo样式 */
        .orderprint .page-bottom-logo {
            position: absolute;
            bottom: 5mm;
            right: 5mm;
            width: 25mm;
            z-index: 100;
        }

        .orderprint .page-bottom-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .orderprint .column-title-decorator {
            position: absolute;
            top: -2mm;
            left: 2mm;
            width: 15px;
            height: 4px;
            background: #333;
            display: block;
        }

        .orderprint .column-title-text {
            display: inline-block;
            position: relative;
        }

        /* 产品项目样式 */
        .orderprint .product-group {
            margin-bottom: 4mm;
        }

        .orderprint .group-header {
            background: #E8E8E8;
            padding: 1.5mm 2mm;
            border-radius: 2mm;
            margin-bottom: 2mm;
            text-align: center;
            font-weight: bold;
            color: #333;
            font-size: 10px;
        }

        .orderprint .product-item {
            display: flex;
            counter-increment: product-counter;
            min-height: 20mm;
            align-items: stretch;
            position: relative;
        }

        /* 使用伪元素创建更稳定的边框线 */
        .orderprint .product-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #999;
            z-index: 10;
        }

        .orderprint .products-column .column-title + .product-item::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #999;
            z-index: 10;
        }

        /* .orderprint .product-item::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #999;
            z-index: 10;
        } */

        .orderprint .product-item.group-blue {
            background: #E7F7FF;
        }

        .orderprint .product-item.group-pink {
            background: #F9E4E7;
        }

        .orderprint .product-left {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            margin-right: 1.5mm;
            margin-left: 1.5mm;
            flex-shrink: 0;
        }

        .orderprint .product-image {
            width: 12mm;
            height: calc(100% - 8mm);
            border-radius: 1mm;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1mm;
            flex: 1;
        }

        .orderprint .product-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .orderprint .product-details {
            flex: 1;
            font-size: 8px;
            line-height: 1.3;
            background: #FFF9EF;
            padding: 2mm;
            /* border-radius: 1mm; */
        }

        .orderprint .product-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 1mm;
            font-size: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .orderprint .product-name-left {
            flex: 1;
            margin-right: 2mm;
        }

        .orderprint .product-name-right {
            flex-shrink: 0;
            font-size: 8px;
            color: #666;
            text-align: right;
            position: absolute;
            right: 10px;
            top: 24px;
        }

        .orderprint .product-image-right {
            position: absolute;
            top: 18px;
            right: -12px;
            width: 80px;
            height: 30px;
            z-index: 10;
            overflow: hidden; /* 加上 overflow: hidden 是个好习惯，确保裁剪生效 */
        }

        /* 容器内图片的样式 */
        .orderprint .product-image-right img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* 核心：裁剪多余部分，填满容器 */
            /* 注意：这里的圆角不再需要，因为父容器的 overflow:hidden 会自动裁剪 */
        }

        .orderprint .product-dosage {
            color: #666;
            font-size: 8px;
            text-align: center;
            white-space: nowrap;
            margin-bottom: 4mm;
            flex-shrink: 0;
        }

        .orderprint .product-ingredients {
            color: #555;
            margin-bottom: 1mm;
            font-size: 7px;
            line-height: 1.2;
        }

        .orderprint .product-description {
            color: #777;
            font-size: 7px;
            line-height: 1.5;
        }

        .orderprint .product-label {
            font-weight: bold;
            display: block;
            margin-bottom: 0.5mm;
            position: relative;
            padding-bottom: 1mm;
            font-size: 8px;
            color: #333;
        }

        .orderprint .product-label::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #999;
        }

        /* 原始打印按钮样式 */
        .print-layout .print-btn {
            background:#FFF;
            border: solid 1px #ccc;
            position: absolute;
            z-index: 3;
            top:10mm;
            right:10mm;
            line-height:32px;
            padding:5px 10px;
            border-radius: 5px;
            box-shadow: 2px 2px 0 rgba(153,153,153,0.2);
            cursor: pointer;
        }

        .print-layout .print-btn:hover {
            background: #555;
            box-shadow: none;
            border-color: #555;
        }

        .print-layout .print-btn i {
            background: url(__STATIC__/img/ncsc_bg_img.png) scroll no-repeat 0 -460px;
            vertical-align: middle;
            display: inline-block;
            width: 32px;
            height: 32px;
        }

        .print-layout .print-btn a {
            font-family:"microsoft yahei";
            font-size: 20px;
            padding: 0 0 0 10px;
            color: #555;
            font-weight:600;
            display:inline-block;
            vertical-align: middle;
        }

        .print-layout .print-btn:hover a, .print-layout .print-btn a:hover {
            color: #FFF;
            text-decoration:none;
        }




    </style>
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };

        // 搜狗浏览器检测和兼容性处理
        (function() {
            var userAgent = navigator.userAgent.toLowerCase();
            var isSogou = userAgent.indexOf('metasr') > -1 || userAgent.indexOf('sogou') > -1;

            if (isSogou) {
                document.documentElement.className += ' sogou-browser';

                // 等待DOM加载完成后应用内联样式
                document.addEventListener('DOMContentLoaded', function() {
                    applySogouStyles();
                });

                // 如果DOM已经加载完成，立即应用样式
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', applySogouStyles);
                } else {
                    applySogouStyles();
                }
            }

            function applySogouStyles() {
                // 查找分隔文本元素并应用内联样式
                var separatorTextTop = document.querySelector('.orderprint .separator-text-top');
                var separatorTextBottom = document.querySelector('.orderprint .separator-text-bottom');

                if (separatorTextTop) {
                    separatorTextTop.style.setProperty('right', '0mm', 'important');
                    separatorTextTop.style.setProperty('width', '55mm', 'important');
                }

                if (separatorTextBottom) {
                    separatorTextBottom.style.setProperty('right', '0mm', 'important');
                    separatorTextBottom.style.setProperty('width', '55mm', 'important');
                }
            }
        })();
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印说明单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <div class="print-btn" id="printbtn" title="选择喷墨或激光打印机<br/>根据下列纸张描述进行<br/>设置并打印发货单据"><i></i><a href="javascript:void(0);">打印</a></div>
    <div class="a5-size"></div>
    <dl class="a5-tip">
        <dt>
            <h1>A5</h1>
            <em>Size: 210mm x 148mm</em></dt>
        <dd>当打印设置选择A5纸张、横向打印、无边距时每张A5打印纸可输出1页订单。</dd>
    </dl>
    <div class="a4-size"></div>
    <dl class="a4-tip">
        <dt>
            <h1>A4</h1>
            <em>Size: 210mm x 297mm</em></dt>
        <dd>当打印设置选择A4纸张、竖向打印、无边距时每张A4打印纸可输出2页订单。</dd>
    </dl>
    <div class="print-page">
        <div id="printarea">
            <style type="text/css">
                /* 打印区域专用样式 */
                * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                body {
                    background: #FFFFFF !important;
                    margin: 0;
                    padding: 0;
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                }

                #all-pages-container .page-preview:not(:first-child) .products-column {
                    margin-top: 4mm;
                }

                .orderprint {
                    background: #FFFFFF !important;
                    width: 188mm;
                    height: 100%;
                    margin-bottom: unset !important;
                    padding: 0mm 8mm 0mm 10mm !important;
                    color: #000000;
                    position: relative;
                }

                /* 头部信息区域 */
                .orderprint .header-section {
                    height: 90mm;
                    padding: 4mm 4mm 2mm 4mm;
                    position: relative;
                    background: #FFFFFF !important;
                    display: flex;
                }

                .orderprint .header-left-column {
                    flex: 1;
                    padding-right: 6mm;
                }

                .orderprint .header-right-column {
                    flex: 1;
                    padding-left: 2mm;
                }

                .orderprint .logo-area {
                    margin-bottom: 5mm;
                }

                .orderprint .logo-container {
                    width: 34mm;
                    margin-bottom: 10mm;
                    padding-top: 2mm;
                    padding-left: 2mm;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                }

                .orderprint .logo-container img {
                    max-width: 100%;
                    max-height: 100%;
                }

                .orderprint .title-area {
                    margin-bottom: 5mm;
                    text-align: center;
                }

                .orderprint .main-title {
                    font-size: 28px;
                    font-weight: normal;
                    color: #333 !important;
                    margin-bottom: 2mm;
                }

                .orderprint .customer-name {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333 !important;
                    margin-bottom: 0;
                }

                .orderprint .order-info-area {
                    margin-top: 5mm;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .orderprint .info-item {
                    margin-bottom: 2mm;
                    font-size: 12px;
                    color: #333 !important;
                    text-align: left;
                    width: 50mm;
                }

                .orderprint .info-label {
                    font-weight: bold;
                    display: inline-block;
                    width: 18mm;
                }

                .orderprint .usage-guide {
                    margin-bottom: 5mm;
                }

                .orderprint .usage-title {
                    font-size: 12px;
                    font-weight: bold;
                    color: #333 !important;
                    margin-bottom: 3mm;
                    padding-bottom: 2mm;
                    border-bottom: 1px solid #333 !important;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .orderprint .usage-title-right {
                    font-size: 10px;
                    font-weight: 500;
                    color: #FFF9EF !important;
                    background-color: #45172E !important;
                    border-radius: 50px;
                    padding: 2px 8px;
                }

                .orderprint .usage-content {
                    width: 100%;
                    font-size: 10px;
                    line-height: 1.4;
                    color: #333 !important;
                }

                .orderprint .usage-content ul {
                    margin: 0;
                    list-style: none;
                }

                .orderprint .usage-content li {
                    margin-bottom: 2mm;
                }

                .orderprint .usage-content .title-span {
                    font-weight: bold;
                    color: #333 !important;
                }

                /* 分隔区域 */
                .orderprint .separator-section {
                    position: relative;
                    height: 10mm;
                    background: #FFFFFF !important;
                    display: flex;
                    align-items: center;
                    padding-top: 10mm;
                }

                .orderprint .separator-line {
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 50%;
                    height: 1px;
                    background: #333 !important;
                    z-index: 1;
                }

                .orderprint .separator-text-top {
                    position: absolute;
                    right: 6mm;
                    top: 4mm;
                    font-size: 10px;
                    color: #333 !important;
                    z-index: 2;
                    text-align: left;
                    width: 40mm;
                }

                .orderprint .separator-text-bottom {
                    position: absolute;
                    right: 6mm;
                    bottom: 4mm;
                    font-size: 10px;
                    color: #333 !important;
                    z-index: 2;
                    text-align: left;
                    width: 40mm;
                }

                /* 产品展示区域 */
                .orderprint .products-section {
                    min-height: 148mm;
                    display: flex;
                    align-items: flex-start;
                    background: #FFFFFF !important;
                    counter-reset: product-counter;
                }

                .orderprint .products-section.continuation-page {
                    min-height: 240mm;
                }

                .orderprint .products-column {
                    flex: 1;
                    padding: 2mm;
                    align-self: flex-start;
                }

                .orderprint .column-title {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333 !important;
                    text-align: left;
                    margin-bottom: 2mm;
                    margin-top: 4mm;
                    padding-left: 2mm;
                    padding-bottom: 1mm;
                    position: relative;
                    page-break-inside: avoid !important;
                    break-inside: avoid !important;
                    display: inline-block;
                    width: 100%;
                    box-sizing: border-box;
                    overflow: visible;
                }

                /* 页面底部logo样式 - 打印专用 */
                .orderprint .page-bottom-logo {
                    position: absolute;
                    bottom: 5mm;
                    right: 5mm;
                    width: 25mm;
                    z-index: 100;
                }

                .orderprint .page-bottom-logo img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }

                .orderprint .column-title-decorator {
                    position: absolute;
                    top: -2mm;
                    left: 2mm;
                    width: 15px;
                    height: 4px;
                    background: #333 !important;
                    display: block;
                }

                .orderprint .column-title-text {
                    display: inline-block;
                    position: relative;
                }

                .orderprint .product-item {
                    display: flex;
                    counter-increment: product-counter;
                    min-height: 20mm;
                    align-items: stretch;
                    position: relative;
                    page-break-inside: avoid !important;
                }

                .orderprint .product-item::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: #999 !important;
                    z-index: 10;
                }

                .orderprint .products-column .column-title + .product-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: #999;
                    z-index: 10;
                }

                /* .orderprint .product-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: #999;
                    z-index: 10;
                } */

                .orderprint .product-item.group-blue {
                    background: #E7F7FF !important;
                }

                .orderprint .product-item.group-pink {
                    background: #F9E4E7 !important;
                }

                .orderprint .product-left {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: space-between;
                    margin-right: 1.5mm;
                    margin-left: 1.5mm;
                    flex-shrink: 0;
                }

                .orderprint .product-image {
                    width: 12mm;
                    height: calc(100% - 8mm);
                    border-radius: 1mm;
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 1mm;
                    flex: 1;
                }

                .orderprint .product-image img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }

                .orderprint .product-details {
                    flex: 1;
                    font-size: 8px;
                    line-height: 1.3;
                    background: #FFF9EF !important;
                    padding: 2mm;
                }

                .orderprint .product-name {
                    font-weight: bold;
                    color: #333 !important;
                    margin-bottom: 1mm;
                    font-size: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                }

                .orderprint .product-name-left {
                    flex: 1;
                    margin-right: 2mm;
                }

                .orderprint .product-name-right {
                    flex-shrink: 0;
                    font-size: 8px;
                    color: #666 !important;
                    text-align: right;
                    position: absolute;
                    right: 10px;
                    top: 24px;
                }

                .orderprint .product-image-right {
                    position: absolute;
                    top: 18px;
                    right: -12px;
                    width: 80px;
                    height: 30px;
                    z-index: 10;
                    overflow: hidden; /* 加上 overflow: hidden 是个好习惯，确保裁剪生效 */
                }

                /* 容器内图片的样式 */
                .orderprint .product-image-right img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover; /* 核心：裁剪多余部分，填满容器 */
                    /* 注意：这里的圆角不再需要，因为父容器的 overflow:hidden 会自动裁剪 */
                }

                .orderprint .product-dosage {
                    color: #666 !important;
                    font-size: 8px;
                    text-align: center;
                    white-space: nowrap;
                    margin-bottom: 4mm;
                    flex-shrink: 0;
                }

                .orderprint .product-ingredients {
                    color: #555 !important;
                    margin-bottom: 1mm;
                    font-size: 7px;
                    line-height: 1.2;
                }

                .orderprint .product-description {
                    color: #777 !important;
                    font-size: 7px;
                    line-height: 1.5;
                }

                .orderprint .product-label {
                    font-weight: bold;
                    display: block;
                    margin-bottom: 0.5mm;
                    position: relative;
                    padding-bottom: 1mm;
                    font-size: 8px;
                    color: #333 !important;
                }

                .orderprint .product-label::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: #999 !important;
                }


            </style>








        <!-- 多页产品展示区域 -->
        <div id="all-pages-container">
            {php}
            // 重构的智能分页算法
            $all_products_ordered = [];
            $time_groups = [
                'earlymoning' => '晨起包',
                'moning' => '早餐包',
                'canjian' => '餐间包',
                'aftnoon' => '午餐包',
                'night' => '晚餐包',
                'evening' => '睡前包'
            ];

            // 收集所有产品并按预定义顺序排列
            foreach($time_groups as $var_name => $group_name) {
                if(isset($$var_name) && is_array($$var_name)) {
                    foreach($$var_name as $vo) {
                        $vo['group_name'] = $group_name;
                        $vo['group_key'] = $var_name;
                        $all_products_ordered[] = $vo;
                    }
                }
            }

            // 分配餐包颜色（交替使用蓝色和粉色）
            $group_colors = [];
            $color_index = 0;
            $colors = ['group-blue', 'group-pink'];
            $seen_groups = [];

            foreach($all_products_ordered as &$product) {
                if(!in_array($product['group_name'], $seen_groups)) {
                    $seen_groups[] = $product['group_name'];
                    $group_colors[$product['group_name']] = $colors[$color_index % 2];
                    $color_index++;
                }
                $product['group_color'] = $group_colors[$product['group_name']];
            }
            unset($product); // 解除引用

            // 智能分页算法配置
            $page_config = [
                'first_page_height' => 188, // 第一页产品区域可用高度（mm）
                'continuation_page_height' => 240, // 后续页面产品区域可用高度（mm）
                'column_width' => 50, // 每栏宽度百分比
                'title_height' => 10, // 餐包标题高度（mm）
                'product_min_height' => 20, // 产品最小高度（mm）
                'product_base_height' => 25, // 基础产品高度（mm）
                'product_detailed_height' => 35, // 详细内容产品高度（mm）
                'margin_height' => 3, // 产品间距（mm）
                'safety_margin' => 8 // 安全边距（mm）
            ];

            // 计算产品实际高度的函数
            function calculateProductHeight($product, $config, $visit) {
                $base_height = $config['product_min_height'];

                // 根据显示的内容计算高度
                $content_lines = 0;

                if ($visit[3] == 1 && !empty($product['introduction'])) {
                    $intro_length = mb_strlen($product['introduction'], 'UTF-8');
                    $content_lines += ceil($intro_length / 40); // 假设每行40字符
                }

                if ($visit[4] == 1 && !empty($product['introd'])) {
                    $desc_length = mb_strlen($product['introd'], 'UTF-8');
                    $content_lines += ceil($desc_length / 40);
                }

                // 根据内容行数调整高度
                if ($content_lines > 6) {
                    return $config['product_detailed_height'] + ($content_lines - 6) * 3;
                } else if ($content_lines > 3) {
                    return $config['product_detailed_height'];
                } else if ($content_lines > 0) {
                    return $config['product_base_height'];
                } else {
                    return $base_height;
                }
            }

            // 多页分页算法
            $pages = [];
            $current_page = 0;
            $current_column = 'left'; // 'left' 或 'right'
            $left_height = 0;
            $right_height = 0;
            $global_title_shown = []; // 全局已显示的标题

            // 初始化第一页
            $pages[0] = ['left' => [], 'right' => []];

            foreach($all_products_ordered as $product) {
                // 计算当前产品的高度
                $product_height = calculateProductHeight($product, $page_config, $visit);

                // 检查是否需要显示餐包标题
                $title_height = 0;
                $show_title = false;
                if (!in_array($product['group_name'], $global_title_shown)) {
                    $title_height = $page_config['title_height'];
                    $show_title = true;
                    $global_title_shown[] = $product['group_name'];
                }

                $product['show_title'] = $show_title;
                $total_height = $product_height + $title_height + $page_config['margin_height'];

                // 根据当前页面类型确定可用高度
                $current_available_height = ($current_page == 0) ?
                    $page_config['first_page_height'] :
                    $page_config['continuation_page_height'];

                // 检查当前栏是否能容纳
                $can_fit_left = ($left_height + $total_height) <= ($current_available_height - $page_config['safety_margin']);
                $can_fit_right = ($right_height + $total_height) <= ($current_available_height - $page_config['safety_margin']);

                if ($current_column == 'left') {
                    if ($can_fit_left) {
                        // 放入左栏
                        $pages[$current_page]['left'][] = $product;
                        $left_height += $total_height;
                    } else if ($can_fit_right) {
                        // 左栏满了，放入右栏
                        $current_column = 'right';
                        $pages[$current_page]['right'][] = $product;
                        $right_height += $total_height;
                    } else {
                        // 两栏都满了，开始新页面
                        $current_page++;
                        $pages[$current_page] = ['left' => [], 'right' => []];
                        $current_column = 'left';
                        $left_height = $total_height;
                        $right_height = 0;
                        $pages[$current_page]['left'][] = $product;
                    }
                } else { // current_column == 'right'
                    if ($can_fit_right) {
                        // 放入右栏
                        $pages[$current_page]['right'][] = $product;
                        $right_height += $total_height;
                    } else {
                        // 右栏满了，开始新页面
                        $current_page++;
                        $pages[$current_page] = ['left' => [], 'right' => []];
                        $current_column = 'left';
                        $left_height = $total_height;
                        $right_height = 0;
                        $pages[$current_page]['left'][] = $product;
                    }
                }
            }
            {/php}

            <!-- 多页预览显示 -->
            {foreach $pages as $page_index => $page_data}
            <div class="page-preview" data-page="{$page_index + 1}">

                <div class="orderprint">
                    {if $page_index == 0}
                    <!-- 第一页：包含完整的头部信息区域 -->
                    <!-- 上半部分：头部信息区域 -->
                    <div class="header-section">
                        <!-- 左侧信息栏 -->
                        <div class="header-left-column">
                            <!-- Logo区域 -->
                            <div class="logo-area">
                                <div class="logo-container">
                                    <img src="__STATIC__/img/shop/printorderr_c_log.png" alt="Logo">
                                </div>
                            </div>

                            <!-- 标题区域 -->
                            <div class="title-area">
                                <div class="main-title">TO</div>
                                <div class="customer-name">{$order_detail.name}</div>
                            </div>

                            <!-- 订单信息区域 -->
                            <div class="order-info-area">
                                <div class="info-item">
                                    <span class="info-label">调理时间:</span>
                                    <span>{$attr_class_info.day}天</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">下单时间:</span>
                                    <span>{$order_detail.create_time|date="Y年m月d日"}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">订单编号:</span>
                                    <span>2025069784758695</span>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧使用指导栏 -->
                        <div class="header-right-column">
                            <div class="usage-guide">
                                <div class="usage-title">
                                    <span>注意事项</span>
                                    <span class="usage-title-right">请务必仔细阅读</span>
                                </div>
                                <div class="usage-content">
                                    <ul style="padding-left: unset">
                                        <li><span class="title-span">个性定制</span>: 产品按照您个人需求定制，请勿分享。</li>
                                        <li><span class="title-span">孩童误食</span>: 请匆让儿童接触和误食，以免发生危险。</li>
                                        <li><span class="title-span">特殊情况</span>: 请勿超过推荐剂量。服用期间出现疾病、怀孕等健康变化，请咨询医师确认适用性。</li>
                                        <li><span class="title-span">不良反应</span>: 请勿与其他保健品一同服用，出现不良状况请立即停用，并联系您的健康管理师。</li>
                                        <li><span class="title-span">存储环境</span>: 请将本品置于阴凉干燥处保存。最佳存储条件温度0-25℃，湿度小于40%。</li>
                                        <li><span class="title-span">保存时间</span>: 请于保质期内服用，包装日期等信息详见卡板背面，开封后请立即服用，免受到污染。</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="usage-guide">
                                <div class="usage-title">服用指导</div>
                                <div class="usage-content">
                                    <ul style="list-style: unset; padding-left: 12px">
                                        <li>每日按时间段服用，建议在餐前30分钟或餐后1小时服用。</li>
                                        <li>请用温开水送服，避免用茶水、咖啡等饮品。</li>
                                        <li>如有不适请及时联系医师，并停止服用相关产品。</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分隔区域 -->
                    <div class="separator-section">
                        <div class="separator-line"></div>
                        <div class="separator-text-top">此页背面还有一些信息哦>>></div>
                        <div class="separator-text-bottom">图片仅供参考，产品以实物为准</div>
                    </div>
                    {/if}

                    <!-- 产品展示区域 -->
                    <div class="products-section{if $page_index > 0} continuation-page{/if}">
                        <!-- 左栏 -->
                        <div class="products-column">
                            {foreach $page_data['left'] as $k => $vo}
                            {if $vo['show_title']}
                            <div class="column-title">
                                <span class="column-title-decorator"></span>
                                <span class="column-title-text">{$vo['group_name']}</span>
                            </div>
                            {/if}
                            <div class="product-item {$vo['group_color']}">
                                <div class="product-left">
                                    {if $visit[1] == 1}
                                    <div class="product-image">
                                        <img src="{:img($vo.sku_image)}" alt="{$vo['goods_chi']}">
                                    </div>
                                    {/if}
                                    {if $visit[2] == 1}
                                    <div class="product-dosage">剂量：{$vo['num']}</div>
                                    {/if}
                                </div>
                                <div class="product-details">
                                    {if $visit[0] == 1}
                                    <div class="product-name">
                                        <div class="product-name-left">
                                            {if $visit[5] == 1}{$vo['sku_name']}{/if}
                                            {$vo['goods_chi']}
                                        </div>
                                        {if $vo['category_image']}
                                        <div class="product-image-right">
                                            <img src="{$vo['category_image']}" alt="{$vo['category_name']}" style="max-height: 6mm; max-width: 16mm;">
                                        </div>
                                        {else}
                                        <div class="product-name-right">
                                            {$vo['category_name']}
                                        </div>
                                        {/if}
                                    </div>
                                    {/if}
                                    {if $visit[3] == 1}
                                    <div class="product-ingredients">
                                        <span class="product-label">主要成分</span>
                                        <div style="font-size: 7px;">{$vo['introduction']}</div>
                                    </div>
                                    {/if}
                                    {if $visit[4] == 1}
                                    <div class="product-description">
                                        <span class="product-label">产品说明</span>
                                        <div style="font-size: 7px;">{$vo['introd']}</div>
                                    </div>
                                    {/if}
                                </div>
                            </div>
                            {/foreach}
                        </div>

                        <!-- 右栏 -->
                        <div class="products-column" style="margin-left: 2mm;">
                            {foreach $page_data['right'] as $k => $vo}
                            {if $vo['show_title']}
                            <div class="column-title">
                                <span class="column-title-decorator"></span>
                                <span class="column-title-text">{$vo['group_name']}</span>
                            </div>
                            {/if}
                            <div class="product-item {$vo['group_color']}">
                                <div class="product-left">
                                    {if $visit[1] == 1}
                                    <div class="product-image">
                                        <img src="{:img($vo.sku_image)}" alt="{$vo['goods_chi']}">
                                    </div>
                                    {/if}
                                    {if $visit[2] == 1}
                                    <div class="product-dosage">剂量：{$vo['num']}</div>
                                    {/if}
                                </div>
                                <div class="product-details">
                                    {if $visit[0] == 1}
                                    <div class="product-name">
                                        <div class="product-name-left">
                                            {if $visit[5] == 1}{$vo['sku_name']}{/if}
                                            {$vo['goods_chi']}
                                        </div>
                                        {if $vo['category_image']}
                                        <div class="product-image-right">
                                            <img src="{$vo['category_image']}" alt="{$vo['category_name']}" style="max-height: 6mm; max-width: 16mm;">
                                        </div>
                                        {else}
                                        <div class="product-name-right">
                                            {$vo['category_name']}
                                        </div>
                                        {/if}
                                    </div>
                                    {/if}
                                    {if $visit[3] == 1}
                                    <div class="product-ingredients">
                                        <span class="product-label">主要成分</span>
                                        <div style="font-size: 7px;">{$vo['introduction']}</div>
                                    </div>
                                    {/if}
                                    {if $visit[4] == 1}
                                    <div class="product-description">
                                        <span class="product-label">产品说明</span>
                                        <div style="font-size: 7px;">{$vo['introd']}</div>
                                    </div>
                                    {/if}
                                </div>
                            </div>
                            {/foreach}
                        </div>
                    </div>

                    <!-- 从第二页开始添加右下角logo -->
                    {if $page_index > 0}
                    <div class="page-bottom-logo">
                        <img src="http://www.baoyao3.com/public/static/img/shop/printorderr_c_log.png" alt="Logo">
                    </div>
                    {/if}
                </div>
            </div>
            {/foreach}
        </div>
            </div> <!-- orderprint -->
        </div> <!-- printarea -->
    </div> <!-- print-page -->
</div> <!-- print-layout -->
{/notempty}
</body>
<script>
    $(function(){
        // 页面导航功能
        var currentPage = 1;
        var totalPages = $('.page-preview').length;

        // 添加页面导航控件
        if (totalPages > 1) {
            var navHtml = '<div class="page-navigation" style="position: fixed; top: 20px; right: 20px; z-index: 1000; background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">' +
                '<button id="prevPage" style="margin-right: 5px;">上一页</button>' +
                '<span id="pageInfo">第 ' + currentPage + ' 页 / 共 ' + totalPages + ' 页</span>' +
                '<button id="nextPage" style="margin-left: 5px;">下一页</button>' +
                '<button id="showAllPages" style="margin-left: 10px;">显示所有页</button>' +
                '</div>';
            $('body').append(navHtml);

            // 初始只显示第一页
            showPage(1);

            // 绑定导航事件
            $('#prevPage').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    showPage(currentPage);
                }
            });

            $('#nextPage').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    showPage(currentPage);
                }
            });

            $('#showAllPages').click(function() {
                $('.page-preview').show();
                $('#pageInfo').text('显示所有页面');
            });
        }

        function showPage(pageNum) {
            $('.page-preview').hide();
            $('.page-preview').eq(pageNum - 1).show();
            $('#pageInfo').text('第 ' + pageNum + ' 页 / 共 ' + totalPages + ' 页');

            // 更新按钮状态
            $('#prevPage').prop('disabled', pageNum <= 1);
            $('#nextPage').prop('disabled', pageNum >= totalPages);
        }

        $("#printbtn").click(function(){
            // 在打印前确保搜狗浏览器样式已应用
            var userAgent = navigator.userAgent.toLowerCase();
            var isSogou = userAgent.indexOf('metasr') > -1 || userAgent.indexOf('sogou') > -1;

            if (isSogou) {
                // 强制应用搜狗浏览器样式
                var separatorTextTop = document.querySelector('.orderprint .separator-text-top');
                var separatorTextBottom = document.querySelector('.orderprint .separator-text-bottom');

                if (separatorTextTop) {
                    separatorTextTop.style.setProperty('right', '0mm', 'important');
                    separatorTextTop.style.setProperty('width', '55mm', 'important');
                }

                if (separatorTextBottom) {
                    separatorTextBottom.style.setProperty('right', '0mm', 'important');
                    separatorTextBottom.style.setProperty('width', '55mm', 'important');
                }
            }

            // 打印前显示所有页面
            $('.page-preview').show();

            // 稍微延迟执行打印，确保样式已应用
            setTimeout(function() {
                $("#printarea").printArea();

                // 打印完成后恢复页面显示状态
                setTimeout(function() {
                    if (totalPages > 1) {
                        showPage(currentPage);
                    }
                }, 500);
            }, 100);
        });
    });
</script>
</html>