{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<!-- 页面导航 -->
<div class="layui-breadcrumb" style="margin-bottom: 15px;">
    <a href="{:url('shop/orderattr/gudinglists')}">机构处方模板</a>
    <span lay-separator="">/</span>
    <cite>分类管理</cite>
</div>

{if isset($table_not_exists) && $table_not_exists}
<div class="layui-card">
    <div class="layui-card-header">数据库表未创建</div>
    <div class="layui-card-body">
        <div class="layui-alert layui-alert-danger">
            <p>处方模板分类功能需要先执行数据库更新脚本。</p>
            <p>请执行项目根目录下的 <code>update.sql</code> 文件中的SQL语句来创建相关数据表。</p>
            <p>或者点击下面的按钮自动初始化数据库：</p>
            <button class="layui-btn layui-btn-danger" onclick="initDatabase()">自动初始化数据库</button>
        </div>
    </div>
</div>
{else/}
<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>处方模板分类用于对处方模板进行分类管理</li>
            <li>支持多级分类，便于模板的组织和查找</li>
            <li>点击分类名称可以查看子分类，使用"返回上级分类"按钮返回</li>
            <li>使用"返回处方模板"按钮可以回到处方模板列表页面</li>
            <li>删除分类前请确保该分类下没有子分类和处方模板</li>
        </ul>
    </div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
    <button class="layui-btn layui-btn-primary" onclick="location.href=ns.url('shop/orderattr/gudinglists')">
        <i class="layui-icon layui-icon-return"></i> 返回处方模板
    </button>
    <button class="layui-btn ns-bg-color" onclick="addCategory()">添加分类</button>
    <button class="layui-btn layui-btn-warm" id="backToParent" onclick="backToParentCategory()" style="display:none;">
        <i class="layui-icon layui-icon-return"></i> 返回上级分类
    </button>
    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_keys" placeholder="请输入分类名称" autocomplete="off" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
        <div class="layui-input-inline">
            <select name="parent_id" lay-filter="parent_filter">
                <option value="">全部分类</option>
                <option value="0">顶级分类</option>
            </select>
        </div>
    </div>
</div>

<!-- 当前位置导航 -->
<div id="breadcrumb" class="layui-breadcrumb" style="margin-bottom: 10px; display: none;">
    <a href="javascript:void(0);" onclick="showAllCategories()">全部分类</a>
    <span lay-separator="">/</span>
    <span id="currentCategoryName">当前分类</span>
</div>

<!-- 列表 -->
<table id="category_list" lay-filter="category_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </div>
</script>

<!-- 分类图片显示 -->
<script type="text/html" id="categoryImage">
    {{# if(d.category_image_url) { }}
        <img src="{{d.category_image_url}}" style="width:40px;height:40px;object-fit:cover;" />
    {{# } else { }}
        <span style="color:#ccc;">无图片</span>
    {{# } }}
</script>

<!-- 显示状态 -->
<script type="text/html" id="showStatus">
    {{# if(d.status == 1) { }}
        <span class="layui-badge layui-bg-green">显示</span>
    {{# } else { }}
        <span class="layui-badge">隐藏</span>
    {{# } }}
</script>

<!-- 排序编辑 -->
<script type="text/html" id="editSort">
    <input name="sort" type="number" onchange="editSort({{d.category_id}},this)" value="{{d.sort}}" placeholder="请输入排序" class="layui-input edit-sort ns-len-short">
</script>

<!-- 添加分类弹窗 -->
<script type="text/html" id="addCategory">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>分类名称：</label>
            <div class="layui-input-block">
                <input name="category_name" type="text" placeholder="请输入分类名称" lay-verify="required" class="layui-input ns-len-mid">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">上级分类：</label>
            <div class="layui-input-block">
                <select name="parent_id" lay-filter="parent_select">
                    <option value="0">顶级分类</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">分类图片：</label>
            <div class="layui-input-block">
                <div class="ns-upload-img">
                    <input type="hidden" name="category_image" value="">
                    <div class="ns-upload-img-box">
                        <img src="" style="display:none;">
                        <div class="ns-upload-img-default">
                            <i class="layui-icon layui-icon-upload"></i>
                            <p>点击上传图片</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">排序：</label>
            <div class="layui-input-block">
                <input name="sort" type="number" value="0" placeholder="请输入排序" lay-verify="num" class="layui-input ns-len-short">
            </div>
            <p class="ns-word-aux mid">排序值必须为整数，数值越小越靠前</p>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">是否显示：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="显示" checked>
                <input type="radio" name="status" value="0" title="隐藏">
            </div>
        </div>
        
        <div class="ns-form-row mid">
            <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
            <button class="layui-btn layui-btn-primary" onclick="closeCategoryLayer()">返回</button>
        </div>
    </div>
</script>

<!-- 编辑分类弹窗 -->
<script type="text/html" id="editCategory">
    <div class="layui-form">
        <input type="hidden" name="category_id" value="{{d.category_id}}">
        
        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>分类名称：</label>
            <div class="layui-input-block">
                <input name="category_name" type="text" value="{{d.category_name}}" placeholder="请输入分类名称" lay-verify="required" class="layui-input ns-len-mid">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">上级分类：</label>
            <div class="layui-input-block">
                <select name="parent_id" lay-filter="parent_select_edit">
                    <option value="0">顶级分类</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">分类图片：</label>
            <div class="layui-input-block">
                <div class="ns-upload-img">
                    <input type="hidden" name="category_image" value="{{d.category_image}}">
                    <div class="ns-upload-img-box">
                        {{# if(d.category_image) { }}
                            <img src="{{ns.img(d.category_image)}}" style="display:block;">
                        {{# } else { }}
                            <img src="" style="display:none;">
                        {{# } }}
                        <div class="ns-upload-img-default" {{# if(d.category_image) { }}style="display:none;"{{# } }}>
                            <i class="layui-icon layui-icon-upload"></i>
                            <p>点击上传图片</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">排序：</label>
            <div class="layui-input-block">
                <input name="sort" type="number" value="{{d.sort}}" placeholder="请输入排序" lay-verify="num" class="layui-input ns-len-short">
            </div>
            <p class="ns-word-aux mid">排序值必须为整数，数值越小越靠前</p>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label mid">是否显示：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="显示" {{# if(d.status == 1) { }}checked{{# } }}>
                <input type="radio" name="status" value="0" title="隐藏" {{# if(d.status == 0) { }}checked{{# } }}>
            </div>
        </div>
        
        <div class="ns-form-row mid">
            <button class="layui-btn ns-bg-color" lay-submit lay-filter="saveEdit">保存</button>
            <button class="layui-btn layui-btn-primary" onclick="closeCategoryLayer()">返回</button>
        </div>
    </div>
</script>
{/if}
{/block}
{block name="script"}
<script>
    var laytpl, add_category_index = -1, edit_category_index = -1,
        form, table, upload;

    // 全局变量存储当前分类信息
    var currentParentId = 0;
    var currentCategoryName = '全部分类';
    var categoryHistory = []; // 分类导航历史

    // 初始化数据库
    function initDatabase() {
        layer.confirm('确定要初始化数据库吗？这将创建处方模板分类相关的数据表。', function() {
            $.ajax({
                url: ns.url("shop/PrescriptionCategory/initDatabase"),
                type: 'POST',
                dataType: 'JSON',
                success: function(res) {
                    layer.msg(res.message);
                    if (res.code == 0) {
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    }
                }
            });
        });
    }

    {if !isset($table_not_exists) || !$table_not_exists}
    layui.use(['form', 'laytpl', 'upload'], function() {
        var repeat_flag = false; //防重复标识
        laytpl = layui.laytpl;
        form = layui.form;
        upload = layui.upload;
        form.render();

        table = new Table({
            elem: '#category_list',
            url: ns.url("shop/PrescriptionCategory/lists"),
            cols: [
                [{
                    field: 'category_name',
                    title: '分类名称',
                    width: '20%',
                    unresize: 'false',
                    templet: function(d) {
                        var html = '<span style="cursor: pointer; color: #1E9FFF;" onclick="viewSubCategories(' + d.category_id + ', \'' + d.category_name + '\')">' + d.category_name + '</span>';
                        if (d.child_count > 0) {
                            html += ' <span class="layui-badge layui-bg-gray">' + d.child_count + '</span>';
                        }
                        return html;
                    }
                }, {
                    field: 'category_image',
                    title: '分类图片',
                    width: '10%',
                    unresize: 'false',
                    templet: '#categoryImage'
                }, {
                    field: 'level',
                    title: '级别',
                    width: '8%',
                    unresize: 'false'
                }, {
                    field: 'child_count',
                    title: '子分类数',
                    width: '10%',
                    unresize: 'false'
                }, {
                    field: 'template_count',
                    title: '模板数',
                    width: '10%',
                    unresize: 'false'
                }, {
                    field: 'is_show',
                    title: '状态',
                    width: '8%',
                    unresize: 'false',
                    templet: '#showStatus'
                }, {
                    unresize: 'false',
                    title: '排序',
                    width: '10%',
                    templet: '#editSort'
                }, {
                    title: '操作',
                    width: '15%',
                    toolbar: '#operation',
                    unresize: 'false'
                }]
            ]
        });

        // 监听工具栏操作
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit':
                    editCategory(data);
                    break;
                case 'delete':
                    deleteCategory(data.category_id);
                    break;
            }
        });

        // 搜索功能
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        // 添加分类表单提交
        form.on('submit(save)', function(data) {
            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: ns.url("shop/PrescriptionCategory/add"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.msg(res.message);
                    if (res.code == 0) {
                        table.reload();
                        layer.close(add_category_index);
                    }
                    repeat_flag = false;
                }
            });
            return false;
        });

        // 编辑分类表单提交
        form.on('submit(saveEdit)', function(data) {
            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: ns.url("shop/PrescriptionCategory/edit"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.msg(res.message);
                    if (res.code == 0) {
                        table.reload();
                        layer.close(edit_category_index);
                    }
                    repeat_flag = false;
                }
            });
            return false;
        });

        // 分类筛选
        form.on('select(parent_filter)', function(data) {
            var parentId = data.value;

            // 重置导航状态
            if (parentId === '') {
                // 显示全部分类
                showAllCategories();
            } else if (parentId === '0') {
                // 显示顶级分类
                currentParentId = 0;
                currentCategoryName = '顶级分类';
                categoryHistory = [];
                updateBreadcrumb();
                hideBackButton();

                table.reload({
                    page: {
                        curr: 1
                    },
                    where: {
                        parent_id: 0
                    }
                });
            } else {
                // 显示指定父级的子分类
                table.reload({
                    page: {
                        curr: 1
                    },
                    where: {
                        parent_id: parentId
                    }
                });
            }
        });

        // 表单验证
        form.verify({
            num: function(value) {
                if (value == '') {
                    return;
                }
                if (value % 1 != 0) {
                    return '排序数值必须为整数';
                }
                if (value < 0) {
                    return '排序数值必须大于等于0';
                }
            }
        });
    });

    // 监听单元格编辑
    function editSort(id, event) {
        var data = $(event).val();
        if (!new RegExp("^[0-9]\\d*$").test(data)) {
            layer.msg("排序号只能是非负整数");
            return;
        }
        $.ajax({
            type: 'POST',
            url: ns.url("shop/PrescriptionCategory/modifySort"),
            data: {
                sort: data,
                category_id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
                if (res.code == 0) {
                    table.reload();
                }
            }
        });
    }

    // 添加分类
    function addCategory() {
        var add_category = $("#addCategory").html();
        laytpl(add_category).render({}, function(html) {
            add_category_index = layer.open({
                title: '添加分类',
                skin: 'layer-tips-class',
                type: 1,
                area: ['600px'],
                content: html,
                success: function(layero, index) {
                    // 如果当前在查看某个分类的子分类，则默认设置父级分类
                    if (currentParentId > 0) {
                        setTimeout(function() {
                            layero.find('select[name="parent_id"]').val(currentParentId);
                            form.render('select');
                        }, 100);
                    }

                    // 重新渲染表单
                    form.render();

                    // 初始化图片上传
                    initImageUpload();
                },
                end: function() {
                    // 弹窗关闭时重置索引
                    add_category_index = -1;
                }
            });
        });
    }

    // 编辑分类
    function editCategory(data) {
        var edit_category = $("#editCategory").html();
        laytpl(edit_category).render(data, function(html) {
            edit_category_index = layer.open({
                title: '编辑分类',
                skin: 'layer-tips-class',
                type: 1,
                area: ['600px'],
                content: html,
                success: function(layero, index) {
                    // 重新渲染表单
                    form.render();

                    // 初始化图片上传
                    initImageUpload();
                },
                end: function() {
                    // 弹窗关闭时重置索引
                    edit_category_index = -1;
                }
            });
        });
    }

    // 删除分类
    function deleteCategory(category_id) {
        layer.confirm('删除分类后将无法恢复，请谨慎操作', function() {
            $.ajax({
                url: ns.url("shop/PrescriptionCategory/delete"),
                data: {
                    category_id: category_id
                },
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.msg(res.message);
                    if (res.code == 0) {
                        table.reload();
                    }
                }
            });
        });
    }

    // 关闭弹窗
    function closeCategoryLayer() {
        if (add_category_index > 0) {
            layer.close(add_category_index);
        }
        if (edit_category_index > 0) {
            layer.close(edit_category_index);
        }
    }

    // 初始化图片上传
    function initImageUpload() {
        upload.render({
            elem: '.ns-upload-img-box',
            url: ns.url("shop/upload/image"),
            accept: 'images',
            acceptMime: 'image/*',
            done: function(res) {
                if (res.code == 0) {
                    var imgBox = $(this.elem).closest('.ns-upload-img');
                    imgBox.find('input[name="category_image"]').val(res.data.path);
                    imgBox.find('img').attr('src', ns.img(res.data.path)).show();
                    imgBox.find('.ns-upload-img-default').hide();
                } else {
                    layer.msg(res.message);
                }
            }
        });
    }



    // 查看子分类
    function viewSubCategories(categoryId, categoryName) {
        // 保存当前状态到历史
        categoryHistory.push({
            parentId: currentParentId,
            categoryName: currentCategoryName
        });

        currentParentId = categoryId;
        currentCategoryName = categoryName;

        // 更新界面
        updateBreadcrumb();
        showBackButton();

        // 重新加载表格数据
        table.reload({
            page: {
                curr: 1
            },
            where: {
                parent_id: categoryId
            }
        });
    }

    // 返回上级分类
    function backToParentCategory() {
        if (categoryHistory.length > 0) {
            var lastState = categoryHistory.pop();
            currentParentId = lastState.parentId;
            currentCategoryName = lastState.categoryName;

            // 更新界面
            updateBreadcrumb();

            if (categoryHistory.length === 0) {
                hideBackButton();
            }

            // 重新加载表格数据
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    parent_id: currentParentId
                }
            });
        }
    }

    // 显示所有分类
    function showAllCategories() {
        currentParentId = 0;
        currentCategoryName = '全部分类';
        categoryHistory = [];

        updateBreadcrumb();
        hideBackButton();

        // 重新加载表格数据
        table.reload({
            page: {
                curr: 1
            },
            where: {
                parent_id: ''
            }
        });
    }

    // 更新面包屑导航
    function updateBreadcrumb() {
        if (currentParentId > 0) {
            $('#breadcrumb').show();
            $('#currentCategoryName').text(currentCategoryName);
        } else {
            $('#breadcrumb').hide();
        }
    }

    // 显示返回按钮
    function showBackButton() {
        $('#backToParent').show();
    }

    // 隐藏返回按钮
    function hideBackButton() {
        $('#backToParent').hide();
    }
    {/if}
</script>
{/block}
