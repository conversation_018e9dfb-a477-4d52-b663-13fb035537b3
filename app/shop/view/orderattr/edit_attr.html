{extend name="base"/}
{block name="resources"}
<style>
    #goods thead th{ background-color: #e6e6e6;}
    /* 优惠商品 */
    .goods-empty { width: 100%; display: flex; justify-content: center; align-items: center; }
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>每个活动可添加2到6个商品</li>
            <li>凡选择指定优惠的商品，在这个商品的详细页将出现发布的优惠套装</li>
            <li>特殊商品不能参加该活动</li>
        </ul>
    </div>
</div>

<div class="layui-form ns-form" id="add_active" lay-filter="storeform">
    <div class="layui-form-item">
        <label class="layui-form-label mid"><span class="required">*</span>模板名称：</label>
        <div class="layui-input-inline">
            <input id="class_name" name="class_name" type="text" value="{$attr_class_info['class_name']}" placeholder="请输入模板名称" lay-verify="required" class="layui-input ns-len-mid">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label mid">排序：</label>
        <div class="layui-input-block">
            <input id="sort" name="sort" type="number" value="{$attr_class_info['sort']}" placeholder="请输入排序" lay-verify="num" class="layui-input ns-len-short">
        </div>
        <div class="ns-word-aux mid">排序值必须为整数</div>
    </div>
    <input id="class_id" type="text" name="class_id" value="{$class_id}" hidden>
    <div class="layui-card-header ns-card-common">
        <span class="ns-card-title">处方详情</span>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">晨起包服用时间：</label>
        <div class="layui-input-block">
            <input type="text"maxlength="18" id="earlymoning_time" value="{$attr_class_info['earlymoning_time']}" name="earlymoning_time" autocomplete="off" class="layui-input ns-len-long">
        </div>
        <div class="ns-word-aux">
            <p>请认真填写，使顾客能清楚服用时间,如晨起空腹服用(限18个字符)</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">晨起包：</label>
        <div class="layui-input-block">
            <table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
                <colgroup>
                    <col width="30%">
                    <col width="15%">
                    <col width="20%">
                    <col width="10%">
                    <col width="10%">
                    <col width="15%">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>图片</th>
                    <th>价格</th>
                    <th>库存</th>
                    <th>数量</th>
                    <th class="operation">操作</th>
                </tr>
                </thead>
                <tbody>
                {if condition="$earlymoning"}
                {foreach name="$earlymoning" item="vo"}
                <tr data-sku_id="{$vo['sku_id']}">
                    <td>{$vo['sku_name']}</td>
                    <td><img width='60px' layer-src src="/{$vo['sku_image']}"/></td>
                    <td class='price-one'>{$vo['price']}</td>
                    <td>{$vo['stock']}</td>
                    <td><input name="earlymoring_{$vo['sku_id']}" type='number'  onchange='editSort(this)' value="{$vo['num']}" autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>
                    <td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="4">
                        <div class="goods-empty">未添加商品</div>
                    </td>
                </tr>
                {/if}
                </tbody>
            </table>
            <button class="layui-btn ns-bg-color" onclick="addGoods()">添加商品</button>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">早餐包服用时间：</label>
        <div class="layui-input-block">
            <input type="text"maxlength="18" id="moning_time"  value="{$attr_class_info['moning_time']}" name="moning_time" autocomplete="off" class="layui-input ns-len-long">
        </div>
        <div class="ns-word-aux">
            <p>请认真填写，使顾客能清楚服用时间，如早餐后10分钟服用(限18个字符)</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">早餐包：</label>
        <div class="layui-input-block">
            <table class="layui-table" id="goods_moning" lay-skin="line" lay-size="lg">
                <colgroup>
                    <col width="30%">
                    <col width="15%">
                    <col width="20%">
                    <col width="10%">
                    <col width="10%">
                    <col width="15%">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>图片</th>
                    <th>价格</th>
                    <th>库存</th>
                    <th>数量</th>
                    <th class="operation">操作</th>
                </tr>
                </thead>
                <tbody>
                {if condition="$moning"}
                {foreach name="$moning" item="vo"}
                <tr data-sku_id="{$vo['sku_id']}">
                    <td>{$vo['sku_name']}</td>
                    <td><img width='60px' layer-src src="/{$vo['sku_image']}"/></td>
                    <td class='price-one'>{$vo['price']}</td>
                    <td>{$vo['stock']}</td>
                    <td><input name="moning_{$vo['sku_id']}" type='number'  onchange='editSort(this)' value="{$vo['num']}" autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>
                    <td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="4">
                        <div class="goods-empty">未添加商品</div>
                    </td>
                </tr>
                {/if}
                </tbody>
            </table>
            <button class="layui-btn ns-bg-color" onclick="addGoods_moning()">添加商品</button>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">午餐包服用时间：</label>
        <div class="layui-input-block">
            <input type="text"maxlength="18" id="aftnoon_time" value="{$attr_class_info['aftnoon_time']}" name="aftnoon_time" autocomplete="off" class="layui-input ns-len-long">
        </div>
        <div class="ns-word-aux">
            <p>请认真填写，使顾客能清楚服用时间，如午餐后10分钟服用(限18个字符)</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">午餐包：</label>
        <div class="layui-input-block">
            <table class="layui-table" id="goods_aftnoon" lay-skin="line" lay-size="lg">
                <colgroup>
                    <col width="30%">
                    <col width="15%">
                    <col width="20%">
                    <col width="10%">
                    <col width="10%">
                    <col width="15%">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>图片</th>
                    <th>价格</th>
                    <th>库存</th>
                    <th>数量</th>
                    <th class="operation">操作</th>
                </tr>
                </thead>
                <tbody>
                {if condition="$aftnoon"}
                {foreach name="$aftnoon" item="vo"}
                <tr data-sku_id="{$vo['sku_id']}">
                    <td>{$vo['sku_name']}</td>
                    <td><img width='60px' layer-src src="/{$vo['sku_image']}"/></td>
                    <td class='price-one'>{$vo['price']}</td>
                    <td>{$vo['stock']}</td>
                    <td><input name="aftnoon_{$vo['sku_id']}" type='number'  onchange='editSort(this)' value="{$vo['num']}" autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>
                    <td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="4">
                        <div class="goods-empty">未添加商品</div>
                    </td>
                </tr>
                {/if}
                </tbody>
            </table>
            <button class="layui-btn ns-bg-color" onclick="addGoods_aftnoon()">添加商品</button>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">晚餐包服用时间：</label>
        <div class="layui-input-block">
            <input type="text"maxlength="18" id="night_time" value="{$attr_class_info['night_time']}" name="night_time" autocomplete="off" class="layui-input ns-len-long">
        </div>
        <div class="ns-word-aux">
            <p>请认真填写，使顾客能清楚服用时间，如晚餐后10分钟(限18个字符)</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">晚餐包：</label>
        <div class="layui-input-block">
            <table class="layui-table" id="goods_night" lay-skin="line" lay-size="lg">
                <colgroup>
                    <col width="30%">
                    <col width="15%">
                    <col width="20%">
                    <col width="10%">
                    <col width="10%">
                    <col width="15%">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>图片</th>
                    <th>价格</th>
                    <th>库存</th>
                    <th>数量</th>
                    <th class="operation">操作</th>
                </tr>
                </thead>
                <tbody>
                {if condition="$night"}
                {foreach name="$night" item="vo"}
                <tr data-sku_id="{$vo['sku_id']}">
                    <td>{$vo['sku_name']}</td>
                    <td><img width='60px' layer-src src="/{$vo['sku_image']}"/></td>
                    <td class='price-one'>{$vo['price']}</td>
                    <td>{$vo['stock']}</td>
                    <td><input name="night_{$vo['sku_id']}" type='number'  onchange='editSort(this)' value="{$vo['num']}" autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>
                    <td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="4">
                        <div class="goods-empty">未添加商品</div>
                    </td>
                </tr>
                {/if}
                </tbody>
            </table>
            <button class="layui-btn ns-bg-color" onclick="addGoods_night()">添加商品</button>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">睡前包服用时间：</label>
        <div class="layui-input-block">
            <input type="text"maxlength="18" id="sleep_time" value="{$attr_class_info['sleep_time']}" name="sleep_time" autocomplete="off" class="layui-input ns-len-long">
        </div>
        <div class="ns-word-aux">
            <p>请认真填写，使顾客能清楚服用时间，如睡前10分钟(限18个字符)</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">睡前包：</label>
        <div class="layui-input-block">
            <table class="layui-table" id="goods_evening" lay-skin="line" lay-size="lg">
                <colgroup>
                    <col width="30%">
                    <col width="15%">
                    <col width="20%">
                    <col width="10%">
                    <col width="10%">
                    <col width="15%">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>图片</th>
                    <th>价格</th>
                    <th>库存</th>
                    <th>数量</th>
                    <th class="operation">操作</th>
                </tr>
                </thead>
                <tbody>
                {if condition="$evening"}
                {foreach name="$evening" item="vo"}
                <tr data-sku_id="{$vo['sku_id']}">
                  <td>{$vo['sku_name']}</td>
                  <td><img width='60px' layer-src src="/{$vo['sku_image']}"/></td>
                  <td class='price-one'>{$vo['price']}</td>
                  <td>{$vo['stock']}</td>
                  <td><input name="evening_{$vo['sku_id']}" type='number'  onchange='editSort(this)' value="{$vo['num']}" autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>
                  <td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="4">
                        <div class="goods-empty">未添加商品</div>
                    </td>
                </tr>
                {/if}
                </tbody>
            </table>
            <button class="layui-btn ns-bg-color" onclick="addGoods_evening()">添加商品</button>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">* </span>是否需要二次查验：</label>
        <div class="layui-input-inline">
            <input type="radio" name="ccheck" value="1" title="查验不封口" {if $attr_class_info.ccheck==1} checked {/if}>
            <input type="radio" name="ccheck" value="2" title="不查验封口" {if $attr_class_info.ccheck==2} checked {/if}>
        </div>
    </div>

    <div class="layui-form-item" hidden>
        <label class="layui-form-label"><span class="required">* </span>是否包含粉剂：</label>
        <div class="layui-input-inline">
            <input type="radio" lay-filter="fenji" name="fenji" value="1" title="是">
            <input type="radio" lay-filter="fenji" name="fenji" value="2" title="否">
        </div>
    </div>

    <div id="c_fenji" class="layui-form-item" hidden>
        <label class="layui-form-label"><span class="required">* </span>粉剂是否分装：</label>
        <div class="layui-input-inline">
            <input type="radio" lay-filter="c_fenji" name="c_fenji" value="1" title="分装" {if $attr_class_info.c_fenji==1} checked {/if}>
            <input type="radio" lay-filter="c_fenji" name="c_fenji" value="2" title="不分装" {if $attr_class_info.c_fenji==2} checked {/if}>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">疗程天数：</label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="number"  onchange="editSort(this)" name="day" id="day" autocomplete="off" class="layui-input combined-price ns-len-short" value="{$attr_class_info['day']}" step="1"  min="1" >
            </div>
            <span class="layui-form-mid">天</span>
        </div>
        <div class="ns-word-aux">
            <p>该处方服用疗程天数</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">服务费：</label>
        <div class="layui-input-block">
            <p class="ns-input-text"><span class="service_money">{$attr_class_info['service_money']}</span>元</p>
        </div>
        <div class="ns-word-aux">
            <p>该处方所需服务费</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">价格：</label>
        <div class="layui-input-block">
            <p class="ns-input-text"><span class="original-price">{$attr_class_info['price']}</span>元</p>
        </div>
    </div>

    <div class="ns-form-row">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script>
    var earlymoning = '<?php echo json_encode($earlymoning_key) ?>';
    var moning = '<?php echo json_encode($moning_key) ?>';
    var aftnoon = '<?php echo json_encode($aftnoon_key) ?>';
    var night = '<?php echo json_encode($night_key) ?>';
    var evening = '<?php echo json_encode($evening_key) ?>';
    var form, selectGoodsSkuId = JSON.parse(earlymoning),selectGoodsSkuId_aftnoon = JSON.parse(aftnoon),selectGoodsSkuId_night = JSON.parse(night),selectGoodsSkuId_evening = JSON.parse(evening),selectGoodsSkuId_moning = JSON.parse(moning);
    layui.use("form", function() {
        form = layui.form;
        var repeat_flag = false; //防重复标识

        /**
         * 监听提交
         */
        form.on('submit(save)', function(data) {
            var ccheck = $("input[name='ccheck']:checked").val();
            var fenji = $("input[name='fenji']:checked").val();
            var c_fenji = $("input[name='c_fenji']:checked").val();

            if (!ccheck){
                return layer.confirm('请选择是否二次查验');
            }

            if (!fenji){
                return layer.confirm('请选择是否包含粉剂');
            }

            if (!c_fenji && fenji==1){
                return layer.confirm('请选择粉剂是否分装');
            }

            var sku_ids = selectGoodsSkuId.toString();
            var sku_ids_aftnoon = selectGoodsSkuId_aftnoon.toString();
            var sku_ids_night = selectGoodsSkuId_night.toString();
            var sku_ids_evening = selectGoodsSkuId_evening.toString();
            var sku_ids_moning = selectGoodsSkuId_moning.toString();
            sku_ids = sku_ids + ','+sku_ids_aftnoon + ','+ sku_ids_night + ',' + sku_ids_evening + ',' + sku_ids_moning;
            //获取商品数量
            var goods_name = [];
            var goods_nums = [];
            $("#goods").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_moning").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_night").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            $("#goods_evening").find("tbody td.price-one").each(function(i) {
                goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
                goods_nums.push($(this).parents("tr").find(".goods-num").val());
            });
            //前台会员账号
            var class_id = $("#class_id").val();
            var day = $("#day").val();
            var price = $(".original-price").text();
            var service_money = $(".service_money").text();
            var class_name = $("#class_name").val();
            var sort = $("#sort").val();
            var earlymoning_time = $('#earlymoning_time').val();
            var moning_time = $('#moning_time').val();
            var aftnoon_time = $('#aftnoon_time').val();
            var night_time = $('#night_time').val();
            var sleep_time = $('#sleep_time').val();

            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("shop/orderattr/editAttr"),
                data:   {sleep_time:sleep_time,night_time:night_time,aftnoon_time:aftnoon_time,moning_time:moning_time,earlymoning_time:earlymoning_time,price:price,class_name:class_name,sort:sort,day:day,class_id:class_id,sku_ids:sku_ids,goods_name:goods_name,goods_nums:goods_nums,ccheck:ccheck,fenji:fenji,c_fenji:c_fenji,service_money:service_money},
                async: false,
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('修改成功', {
                            title: '操作提示',
                            btn: ['返回列表', '继续添加'],
                            yes: function() {
                                location.href = ns.url("shop/orderattr/lists");
                            },
                            btn2: function() {
                                location.href = ns.url("shop/orderattr/lists");
                            }
                        });
                    } else {
                        layer.msg(res.message);
                    }
                }
            })
        });


        // 选择粉剂事件
        form.on("radio(c_fenji)", function (data) {
            priceCount();
        });

        form.verify({
            flo: function(value) {
                if (value == '') {
                    return;
                }
                var reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
                if (!reg.test(value)) {
                    return '价格不能小于0，可保留两位小数！'
                }
            }
        });
        priceCount();
    });

    /**
     * 计算总价
     */
    function priceCount() {
        var fenji = $("input[name='fenji']:checked").val();
        var c_fenji = $("input[name='c_fenji']:checked").val();
        var goods_name = [];
        var goods_nums = [];
        var goods_t_name = [];
        var goods_t_nums = [];
        var price_count = 0;
        $("#goods").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_moning").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_canjian").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_night").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_evening").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        var day = $(".combined-price").val();
        price_count = price_count * day;
        $("#goods_teshu").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            goods_t_name.push($(this).parents("tr").find(".goods-num").attr('name'));
            goods_t_nums.push($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });

        $.ajax({
            type: 'POST',
            dataType: 'JSON',
            url: ns.url("shop/order/fuwufei"),
            data:   {fenji:fenji,c_fenji:c_fenji,day:day,goods_name:goods_name,goods_nums:goods_nums,goods_t_name:goods_t_name,goods_t_nums:goods_t_nums},
            async: false,
            success: function(res) {
                repeat_flag = false;
                if (res.code == 0) {
                    price_count = Number(price_count) + Number(res.data.money);
                    $(".service_money").text(res.data.remark);
                    //处理粉剂是否需要分装
                    if(res.data.fenji==1){
                        $("input[name='fenji']").eq(0).attr("checked",true);
                        $('#c_fenji').show();
                    }else{
                        $("input[name='fenji']").eq(1).attr("checked",true);
                        $('#c_fenji').hide();
                    }
                }
            }
        })
        $(".original-price").text(price_count.toFixed(2));
    }

    /**
     * 计算组合套餐价格、原价、节省价
     */
    $(".combined-price").blur(function() {
        priceCount();
    });
    /**
     * 添加商品
     */
    function addGoods() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='earlymoring_" + item.sku_id + "' type='number'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId.push(item.sku_id);
                }
            }


            $("#goods tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_moning() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_moning tbody .goods-empty").length ? '' : $("#goods_moning tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='moning_" + item.sku_id + "' type='number' onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_moning(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_moning.push(item.sku_id);
                }
            }


            $("#goods_moning tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_moning, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_aftnoon() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_aftnoon tbody .goods-empty").length ? '' : $("#goods_aftnoon tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='aftnoon_" + item.sku_id + "' type='number'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_aftnoon(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_aftnoon.push(item.sku_id);
                }
            }


            $("#goods_aftnoon tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_aftnoon, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加晚餐商品
     */
    function addGoods_night() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_night tbody .goods-empty").length ? '' : $("#goods_night tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='night_" + item.sku_id + "' type='number'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_night(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_night.push(item.sku_id);
                }
            }


            $("#goods_night tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_night, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加睡前商品
     */
    function addGoods_evening() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_evening tbody .goods-empty").length ? '' : $("#goods_evening tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='evening_" + item.sku_id + "' type='number'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_evening(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_evening.push(item.sku_id);
                }
            }
            $("#goods_evening tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_evening, {mode: "sku", max_num: 0, min_num: 0});
    }

    // 监听单元格编辑
    function editSort(event){
        priceCount(); //计算出当前总价格
    }
    /**
     * 删除商品
     */
    function deleteGoods(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId) {
            if (selectGoodsSkuId[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId.splice(i, 1);
            }
        }
    }

    function deleteGoods_moning(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_moning) {
            if (selectGoodsSkuId_moning[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_moning.splice(i, 1);
            }
        }
    }

    function deleteGoods_aftnoon(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_aftnoon) {
            if (selectGoodsSkuId_aftnoon[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_aftnoon.splice(i, 1);
            }
        }
    }

    function deleteGoods_night(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_night) {
            if (selectGoodsSkuId_night[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_night.splice(i, 1);
            }
        }
    }

    function deleteGoods_evening(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_evening) {
            if (selectGoodsSkuId_evening[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_evening.splice(i, 1);
            }
        }
    }

    /**
     * 计算总价
     */
    /*function priceCount() {
        var price_count = 0;
        $("#goods").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_moning").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_aftnoon").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_night").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $("#goods_evening").find("tbody td.price-one").each(function(i) {
            var price_one = Number($(this).text());
            var goods_num = Number($(this).parents("tr").find(".goods-num").val());
            price_count += price_one*goods_num;
        });
        $(".original-price").text(price_count);

        if (price_count == 0) {
            var html = '<tr>' +
                '<td colspan="4">' +
                '<div class="goods-empty">未选择添加商品</div>' +
                '</td>' +
                '</tr>';

            $("#goods tbody").html(html);
        }
    }*/


    // 两个浮点数相减
    function accSub(num1, num2){
        var r1, r2, m;

        try{
            r1 = num1.toString().split(".")[1].length;
        }catch(e){
            r1 = 0;
        }

        try{
            r2 = num2.toString().split(".")[1].length;
        }catch(e){
            r2 = 0;
        }

        m = Math.pow(10, Math.max(r1, r2));
        n = (r1 >= r2) ? r1 : r2;
        return (Math.round(num1 * m - num2 * m) / m).toFixed(2);
    }

    //返回按钮
    function back() {
        location.href = ns.url("shop/orderattr/lists");
    }

</script>
{/block}