{extend name="base"/}
{block name="resources"}
<style>
    #goods thead th{ background-color: #e6e6e6;}
    /* 优惠商品 */
    .goods-empty { width: 100%; display: flex; justify-content: center; align-items: center; }
</style>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>核对处方收货地址</li>
            <li>认真填写，核对处方详细药品信息</li>
            <li>可保存该处方为模板，方便以后开相同处方操作</li>
        </ul>
    </div>
</div>
<div class="layui-form ns-form" lay-filter="storeform" >
    <div class="layui-form-item">
        <label class="layui-form-label mid"><span class="required">*</span>模板名称：</label>
        <div class="layui-input-inline">
            <input id="class_name" name="class_name" type="text" value="{$attr_class_info['class_name']}" placeholder="请输入模板名称" lay-verify="required" class="layui-input ns-len-mid">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label mid">适用人群：</label>
        <div class="layui-input-inline">
            <input id="age_type" name="age_type" type="text" value="{$attr_class_info['age_type']}" placeholder="请输入适用人群" class="layui-input ns-len-mid">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label mid">分类：</label>
        <div class="layui-input-inline">
            <select id="category_id" name="category_id" lay-filter="category_select">
                <option value="0">请选择分类</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label mid">头图：</label>
        <div class="layui-input-block">
            <div class="ns-upload-img">
                <input type="hidden" id="header_image" name="header_image" value="{$attr_class_info.header_image|default=''}">
                <div class="ns-upload-img-box">
                    {if !empty($attr_class_info.header_image)}
                        <img src="{$attr_class_info.header_image_url|default=''}" style="display:block;">
                    {else/}
                        <img src="" style="display:none;">
                    {/if}
                    <div class="ns-upload-img-default" {if !empty($attr_class_info.header_image)}style="display:none;"{/if}>
                        <i class="layui-icon layui-icon-upload"></i>
                        <p>点击上传头图</p>
                    </div>
                </div>
            </div>
            <div class="ns-word-aux">建议尺寸：400x300像素，支持jpg、png格式</div>
        </div>
    </div>

    <input id="class_id" type="text" name="class_id" value="{$class_id}" hidden>

                <div class="layui-form-item">
                    <label class="layui-form-label">处方详情：</label>
                    <div class="layui-input-block">
                        <table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
                            <colgroup>
                                <col width="30%">
                                <col width="15%">
                                <col width="10%">
                                <col width="10%">
                                <col width="10%">
                                <col width="10%">
                                <col width="15%">
                            </colgroup>
                            <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>图片</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>服用时间</th>
                                <th>数量</th>
                                <th class="operation">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <div class="goods-empty">未添加商品</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <button class="layui-btn ns-bg-color" onclick="addGoods()">添加商品</button>
                    </div>
                </div>

                <div class="layui-form-item earlymoring_time" style="display: none;">
                    <label class="layui-form-label">晨起包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="earlymoning_time" name="earlymoning_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如晨起空腹服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item moning_time" style="display: none">
                    <label class="layui-form-label">早餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="moning_time" name="moning_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如早餐后10分钟服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item canjian_time" style="display: none">
                    <label class="layui-form-label">餐间包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="canjian_time" name="canjian_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如餐间服用(限18个字符)</p>
                    </div>
                </div>

                <div class="layui-form-item aftnoon_time" style="display: none">
                    <label class="layui-form-label">午餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="aftnoon_time" name="aftnoon_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如午餐后10分钟服用(限18个字符)</p>
                    </div>
                </div>



                <div class="layui-form-item night_time" style="display: none">
                    <label class="layui-form-label">晚餐包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="night_time" name="night_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如晚餐后10分钟(限18个字符)</p>
                    </div>
                </div>



                <div class="layui-form-item evening_time" style="display: none">
                    <label class="layui-form-label">睡前包服用时间：</label>
                    <div class="layui-input-block">
                        <input type="text" maxlength="18" id="sleep_time" name="sleep_time" autocomplete="off" class="layui-input ns-len-long">
                    </div>
                    <div class="ns-word-aux">
                        <p>请认真填写，使顾客能清楚服用时间，如睡前10分钟(限18个字符)</p>
                    </div>
                </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">* </span>是否需要二次查验：</label>
        <div class="layui-input-inline">
            <input type="radio" name="ccheck" value="1" title="查验不封口" {if $attr_class_info.ccheck==1} checked {/if}>
            <input type="radio" name="ccheck" value="2" title="不查验封口" {if $attr_class_info.ccheck==2} checked {/if}>
        </div>
    </div>

    <div class="layui-form-item" hidden>
        <label class="layui-form-label"><span class="required">* </span>是否包含粉剂：</label>
        <div class="layui-input-inline">
            <input type="radio" lay-filter="fenji" name="fenji" value="1" title="是">
            <input type="radio" lay-filter="fenji" name="fenji" value="2" title="否">
        </div>
    </div>

    <div id="c_fenji" class="layui-form-item" hidden>
        <label class="layui-form-label"><span class="required">* </span>粉剂是否分装：</label>
        <div class="layui-input-inline">
            <input type="radio" lay-filter="c_fenji" name="c_fenji" value="1" title="分装" {if $attr_class_info.c_fenji==1} checked {/if}>
            <input type="radio" lay-filter="c_fenji" name="c_fenji" value="2" title="不分装" {if $attr_class_info.c_fenji==2} checked {/if}>
        </div>
    </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">疗程天数：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline">
                            <input type="number" onchange="editSort(this)" name="day" id="day" autocomplete="off" class="layui-input combined-price ns-len-short" value="1" step="1"  min="1" >
                        </div>
                        <span class="layui-form-mid">天</span>
                    </div>
                    <div class="ns-word-aux">
                        <p>该处方服用疗程天数</p>
                    </div>
                </div>


    <div class="layui-form-item">
        <label class="layui-form-label">服务费：</label>
        <div class="layui-input-block">
            <p class="ns-input-text"><span class="service_money">{$attr_class_info['service_money']}</span>元</p>
        </div>
        <div class="ns-word-aux">
            <p>该处方所需服务费</p>
        </div>
    </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">价格：</label>
                    <div class="layui-input-block">
                        <p class="ns-input-text"><span class="original-price">0.00</span>元</p>
                    </div>
                </div>

    <!-- 排序和详情描述字段 - 移动到最后 -->
    <div class="layui-form-item">
        <label class="layui-form-label mid">排序：</label>
        <div class="layui-input-block">
            <input id="sort" name="sort" type="number" value="{$attr_class_info['sort']}" placeholder="请输入排序" lay-verify="num" class="layui-input ns-len-short">
        </div>
        <div class="ns-word-aux mid">排序值必须为整数</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label mid">详情描述：</label>
        <div class="layui-input-block">
            <input type="hidden" id="rich_content" name="rich_content" value="{$attr_class_info.rich_content|default=''|htmlspecialchars_decode}" />
            <script id="rich_content_editor" type="text/plain" style="width:100%;height:400px;"></script>
        </div>
        <div class="ns-word-aux">可以添加处方模板的详细说明、使用方法、注意事项等信息</div>
    </div>

                <div class="ns-form-row">
                    {if $cankao != 1}
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
                    {/if}
                    <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
                </div>

    <input type="hidden" name="goods_image" value="" />
</div>
<!--商品主图列表-->
<script type="text/html" id="goodsImage">
    {{# if(d.length){ }}
    {{# for(var i=0;i<d.length;i++){ }}
    {{# if(d[i]){ }}
    <div class="item" data-index="{{i}}">
        <div class="img-wrap">
            <img src="{{ns.img(d[i])}}" layer-src>
        </div>
        <div class="operation">
            <i title="图片预览" class="iconfont iconreview js-preview"></i>
            <i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
        </div>
        {{# }else{ }}
        <div class="item empty">
            {{# } }}
        </div>
        {{# } }}
        {{# }else{ }}
        <div class="item empty"></div>
        {{# } }}
</script>
{/block}
{block name="script"}
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script>
    var form, selectGoodsSkuId = [],selectGoodsSkuId_aftnoon = [],selectGoodsSkuId_canjian = [],selectGoodsSkuId_night = [],selectGoodsSkuId_evening = [],selectGoodsSkuId_moning = [],selectGoodsSkuId_teshu = [];
    var stepTab  = false;
    var goodsImage = [];//商品主图
    var laytpl= false;
    const GOODS_IMAGE_MAX = 10;//商品主图数量
    var goodsContent;//商品详情
    var class_id = {$class_id};

    layui.use(['element','laytpl','form', 'laydate','upload'], function() {
        form = layui.form;
        var element = layui.element,
            laydate = layui.laydate,
            upload = layui.upload,
            repeat_flag = false; //防重复标识

        laytpl = layui.laytpl;
        var repeat_flag = false; //防重复标识

        stepTab = 'basic';

        element.tabChange('storeform', stepTab);

        //渲染商品主图列表
        refreshGoodsImage();

        laydate.render({
            elem: '#laydate'
        });

        //添加商品主图
        $(".js-add-goods-image").click(function () {
            openAlbum(function (data) {
                for (var i = 0; i < data.length; i++) {
                    if (goodsImage.length < GOODS_IMAGE_MAX) goodsImage.push(data[i].pic_path);
                }
                refreshGoodsImage();
            }, GOODS_IMAGE_MAX);
        });



        /**
         * 监听提交
         */
        form.on('submit(save)', function(data) {
            var ccheck = $("input[name='ccheck']:checked").val();
            var fenji = $("input[name='fenji']:checked").val();
            var c_fenji = $("input[name='c_fenji']:checked").val();

            if (!ccheck){
                return layer.confirm('请选择是否二次查验');
            }

            if (!fenji){
                return layer.confirm('请选择是否包含粉剂');
            }

            if (!c_fenji && fenji==1){
                return layer.confirm('请选择粉剂是否分装');
            }

            var goods_name = [];
            var goods_nums = [];
            //获取特殊产品
            var goods_t_name = [];
            var goods_t_nums = [];
            var goods_idd  = [];
            var e_mm =0;  var e_m =0;  var e_a =0; var e_c =0;  var e_n =0;  var e_e =0;
            selectGoodsSkuId = [];
            selectGoodsSkuId_moning = [];
            selectGoodsSkuId_aftnoon = [];
            selectGoodsSkuId_canjian = [];
            selectGoodsSkuId_night = [];
            selectGoodsSkuId_evening = [];
            selectGoodsSkuId_teshu = [];
            $("#goods").find("input[type='checkbox']").each(function(i) {
                //获取数量
                if ($(this).is(":checked")) {
                    //分类
                    var time = $(this).val();
                    var timee = time.split('_')[0];
                    if (timee=='earlymoring'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId.push(time.split('_')[1]);
                        e_mm = e_mm + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='moning'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_moning.push(time.split('_')[1]);
                        e_m = e_m + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='aftnoon'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_aftnoon.push(time.split('_')[1]);
                        e_a = e_a + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='canjian'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_canjian.push(time.split('_')[1]);
                        e_c = e_c + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='night'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_night.push(time.split('_')[1]);
                        e_n = e_n + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='evening'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_evening.push(time.split('_')[1]);
                        e_e = e_e + Number($("input[name='" + $(this).val() + "']").val());
                    }
                    if (timee=='teshu'){
                        goods_name.push($(this).val());
                        goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                        goods_idd.push(time.split('_')[1]);
                        selectGoodsSkuId_teshu.push(time.split('_')[1]);
                    }
                }
            });

            var sku_ids = selectGoodsSkuId.toString();
            var sku_ids_aftnoon = selectGoodsSkuId_aftnoon.toString();
            var sku_ids_canjian = selectGoodsSkuId_canjian.toString();
            var sku_ids_night = selectGoodsSkuId_night.toString();
            var sku_ids_evening = selectGoodsSkuId_evening.toString();
            var sku_ids_moning = selectGoodsSkuId_moning.toString();
            var sku_ids_teshu = selectGoodsSkuId_teshu.toString();


            //疗程天数
            var service_money = $(".service_money").text();
            var day = $("#day").val();
            var earlymoning_time = $('#earlymoning_time').val();
            var moning_time = $('#moning_time').val();
            var aftnoon_time = $('#aftnoon_time').val();
            var canjian_time = $('#canjian_time').val();
            var night_time = $('#night_time').val();
            var sleep_time = $('#sleep_time').val();
            var sku_ids_chenqi = sku_ids;
            sku_ids = sku_ids + ',' + sku_ids_moning + ','+sku_ids_aftnoon+ ','+sku_ids_canjian + ','+ sku_ids_night + ',' + sku_ids_evening + ',' + sku_ids_teshu;
            /*if (earlymoning_time.length==0 && sku_ids_chenqi){
                return layer.confirm('晨起包时间不能为空');
            }*/
            if (e_mm>8 && sku_ids_chenqi){
                return layer.confirm('晨起包产品不能超过8种');
            }
            /*if (moning_time.length==0 && sku_ids_moning){
                return layer.confirm('早餐包时间不能为空');
            }*/
            if (e_m>8 && sku_ids_moning){
                return layer.confirm('早餐包产品不能超过8种');
            }
            /*if (aftnoon_time.length==0 && sku_ids_aftnoon){
                return layer.confirm('午餐包时间不能为空');
            }
            if (canjian_time.length==0 && sku_ids_canjian){
                return layer.confirm('餐间包时间不能为空');
            }*/
            if (e_a>8 && sku_ids_aftnoon){
                return layer.confirm('午餐包产品不能超过8种');
            }
            if (e_c>8 && sku_ids_canjian){
                return layer.confirm('餐间包产品不能超过8种');
            }
            /*if (night_time.length==0 && sku_ids_night){
                return layer.confirm('晚餐包时间不能为空');
            }*/
            if (e_n>8 && sku_ids_night){
                return layer.confirm('晚餐包产品不能超过8种');
            }
            /*if (sleep_time.length==0 && sku_ids_evening){
                return layer.confirm('睡前包时间不能为空');
            }*/
            if (e_e>8 && sku_ids_evening){
                return layer.confirm('睡前包产品不能超过8种');
            }

            var price = $(".original-price").text();
            var sort = $("#sort").val();
            var class_name = $("#class_name").val();
            var age_type = $("#age_type").val();
            var category_id = $("#category_id").val();
            var header_image = $("#header_image").val();

            // 获取富文本编辑器内容
            var rich_content = '';
            if (richEditor) {
                rich_content = richEditor.getContent();
            }

            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("shop/orderattr/editgudingAttr"),
                data:   {canjian_time:canjian_time,sleep_time:sleep_time,night_time:night_time,aftnoon_time:aftnoon_time,moning_time:moning_time,earlymoning_time:earlymoning_time,price:price,class_name:class_name,age_type:age_type,category_id:category_id,header_image:header_image,rich_content:rich_content,sort:sort,day:day,class_id:class_id,sku_ids:sku_ids,goods_name:goods_name,goods_nums:goods_nums,ccheck:ccheck,fenji:fenji,c_fenji:c_fenji,service_money:service_money},
                async: false,
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('修改成功', {
                            title: '操作提示',
                            btn: ['返回列表', '继续添加'],
                            yes: function() {
                                location.href = ns.url("shop/orderattr/gudinglists");
                            },
                            btn2: function() {
                                location.href = ns.url("shop/orderattr/gudinglists");
                            }
                        });
                    } else {
                        layer.msg(res.message);
                    }
                }
            })
        });



        // 选择粉剂事件
        form.on("radio(c_fenji)", function (data) {
            priceCount();
        });

        form.verify({
            flo: function(value) {
                if (value == '') {
                    return;
                }
                var reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
                if (!reg.test(value)) {
                    return '价格不能小于0，可保留两位小数！'
                }
            }
        });
    });


    $.ajax({
        url: ns.url("shop/orderattr/ordera"),
        data: {class_id: class_id},
        dataType: 'JSON',
        type: 'POST',
        success: function (res) {
            $("#goods tbody").html('');
            var html = '';
            selectGoodsSkuId = [];
            var earlymoning  = res.sku_ids;
            for (var i in earlymoning) {
                var item = earlymoning[i];
                html += "<tr data-sku_id=" + item.sku_id + ">";
                html += "<td>" + item.sku_name + "</td>";
                html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                html += "<td class='price-one'>" + item.price + "</td>";
                html += "<td>" + item.stock + "</td>";
                html += "<td><div style='display: flex;width: 100px'><input "+item.earlymoring+" lay-ignore onchange='editSort(this)' class='choosee' style='display: flex;width: 26px;height: 26px;' type='checkbox' name='goods_service_ids' id='earlymoring_" + item.sku_id + "' value='earlymoring_" + item.sku_id + "' title='晨起' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>晨起～</span>" +
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.moning+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='moning_" + item.sku_id + "' value='moning_" + item.sku_id + "' title='早餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>早餐～</span>" +
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.canjian+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='canjian_" + item.sku_id + "' value='canjian_" + item.sku_id + "' title='餐间' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>餐间～</span>" +
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.aftnoon+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='aftnoon_" + item.sku_id + "' value='aftnoon_" + item.sku_id + "' title='午餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>午餐～</span>" +
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.night+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='night_" + item.sku_id + "' value='night_" + item.sku_id + "' title='晚餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>晚餐～</span>" +
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.evening+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='evening_" + item.sku_id + "' value='evening_" + item.sku_id + "' title='睡前' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>睡前～</span>"+
                    "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input "+item.teshu+" lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='teshu_" + item.sku_id + "' value='teshu_" + item.sku_id + "' title='出差周转' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>出差周转～</span></div>\n</td>";

                html += "<td><input style='height: 26px;visibility:"+item.earlymoring_s+";' name='earlymoring_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.earlymoring_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input style='height: 26px;visibility:"+item.moning_s+";margin-top: 10px;' name='moning_" + item.sku_id + "' type='number' min='1'  onchange='editSort(this)' value='"+item.moning_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:"+item.canjian_s+";margin-top: 10px;' name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.canjian_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:"+item.aftnoon_s+";margin-top: 10px;' name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.aftnoon_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:"+item.night_s+";margin-top: 10px;' name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.night_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:"+item.evening_s+";margin-top: 10px;' name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.evening_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:"+item.teshu_s+";margin-top: 10px;' name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='"+item.teshu_num+"' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                html += "</tr>";
                selectGoodsSkuId.push(item.sku_id);
            }
            $("#goods tbody").html(html);

            if (res.attr_class_info.earlymoning_time){
                $("#earlymoning_time").val(res.attr_class_info.earlymoning_time);
                $(".earlymoring_time").show();
            }
            if (res.attr_class_info.moning_time){
                $("#moning_time").val(res.attr_class_info.moning_time);
                $(".moning_time").show();
            }
            if (res.attr_class_info.aftnoon_time){
                $("#aftnoon_time").val(res.attr_class_info.aftnoon_time);
                $(".aftnoon_time").show();
            }
            if (res.attr_class_info.canjian_time){
                $("#canjian_time").val(res.attr_class_info.canjian_time);
                $(".canjian_time").show();
            }
            if (res.attr_class_info.night_time){
                $("#night_time").val(res.attr_class_info.night_time);
                $(".night_time").show();
            }
            if (res.attr_class_info.sleep_time){
                $("#sleep_time").val(res.attr_class_info.sleep_time);
                $(".evening_time").show();
            }

            $(".original-price").text(res.attr_class_info.price);
            $("#day").val(res.attr_class_info.day);
            priceCount();
        }
    });

    /**
     * 添加商品
     */
    function addGoods() {
        //记录当前值
        var goods_r=[];
        var goods_n=[];
        var goods_num;
        $("#goods").find("input[type='checkbox']").each(function(i) {
            //获取数量
            if ($(this).is(":checked")) {
                goods_r.push($(this).val());
                goods_num = Number($("input[name='" + $(this).val() + "']").val());
                goods_n.push(goods_num);
            }
        });
        console.log(goods_r);
        console.log(goods_n);
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><div style='display: flex;width: 100px'><input lay-ignore onchange='editSort(this)' class='choosee' style='display: flex;width: 26px;height: 26px;' type='checkbox' name='goods_service_ids' id='earlymoring_" + item.sku_id + "' value='earlymoring_" + item.sku_id + "' title='晨起' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>晨起～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='moning_" + item.sku_id + "' value='moning_" + item.sku_id + "' title='早餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>早餐～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='canjian_" + item.sku_id + "' value='canjian_" + item.sku_id + "' title='餐间' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>餐间～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='aftnoon_" + item.sku_id + "' value='aftnoon_" + item.sku_id + "' title='午餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>午餐～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='night_" + item.sku_id + "' value='night_" + item.sku_id + "' title='晚餐' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>晚餐～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='evening_" + item.sku_id + "' value='evening_" + item.sku_id + "' title='睡前' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>睡前～</span>" +
                        "</div>\n<div style='display: flex;width: 100px;margin-top: 10px;'><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox' style='display: flex;width: 26px;height: 26px;' name='goods_service_ids' id='teshu_" + item.sku_id + "' value='teshu_" + item.sku_id + "' title='出差周转' lay-skin='primary' onclick='checkboxOnclick(this)'><span style='margin-left:10px;'>出差周转～</span></div>\n</td>";

                    html += "<td><input style='height: 26px;visibility:hidden;' name='earlymoring_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input style='height: 26px;visibility:hidden;margin-top: 10px;' name='moning_" + item.sku_id + "' type='number' min='1'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:hidden;margin-top: 10px;' name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:hidden;margin-top: 10px;' name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:hidden;margin-top: 10px;' name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:hidden;margin-top: 10px;' name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'>\n<input  style='height: 26px;visibility:hidden;margin-top: 10px;' name='teshu_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId.push(item.sku_id);
                }
            }

            $("#goods tbody").html(html);
            for (var i = 0; i < goods_r.length; i++) {
                $("input[name='" + goods_r[i] + "']").val(goods_n[i]);

            }
        }, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 0});
        for (var i = 0; i < goods_r.length; i++) {
            $('#'+goods_r[i]).attr("checked","checked");
        }
        priceCount(); //计算出当前总价格
    }

    function checkboxOnclick(checkbox){
        if ( checkbox.checked == true){
            $("input[name='"+checkbox.value+"']").css("visibility","visible");
        }else{
            $("input[name='"+checkbox.value+"']").css("visibility","hidden");
        }
    }

    /**
     * 添加午餐商品
     */
    function addGoods_moning() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_moning tbody .goods-empty").length ? '' : $("#goods_moning tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='moning_" + item.sku_id + "' type='number' min='1'  onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_moning(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_moning.push(item.sku_id);
                }
            }


            $("#goods_moning tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_moning, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_aftnoon() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_aftnoon tbody .goods-empty").length ? '' : $("#goods_aftnoon tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='aftnoon_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_aftnoon(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_aftnoon.push(item.sku_id);
                }
            }


            $("#goods_aftnoon tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_aftnoon, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加午餐商品
     */
    function addGoods_canjian() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_canjian tbody .goods-empty").length ? '' : $("#goods_canjian tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='canjian_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_canjian(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_canjian.push(item.sku_id);
                }
            }


            $("#goods_canjian tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_canjian, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加晚餐商品
     */
    function addGoods_night() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_night tbody .goods-empty").length ? '' : $("#goods_night tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='night_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_night(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_night.push(item.sku_id);
                }
            }


            $("#goods_night tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_night, {mode: "sku", max_num: 0, min_num: 0});
    }

    /**
     * 添加睡前商品
     */
    function addGoods_evening() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods_evening tbody .goods-empty").length ? '' : $("#goods_evening tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sku_id=" + item.sku_id + ">";
                    html += "<td>" + item.sku_name + "</td>";
                    html += "<td><img width='60px' layer-src src='"+ ns.img(item.sku_image) +"'/></td>";
                    html += "<td class='price-one'>" + item.price + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "<td><input name='evening_" + item.sku_id + "' type='number' min='1'   onchange='editSort(this)' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'> </td>";
                    html += "<td class='operation'> <div class='ns-table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods_evening(this)'>删除商品</a></div></td>";
                    html += "</tr>";

                    selectGoodsSkuId_evening.push(item.sku_id);
                }
            }
            $("#goods_evening tbody").html(html);
            priceCount(); //计算出当前总价格

        }, selectGoodsSkuId_evening, {mode: "sku", max_num: 0, min_num: 0});
    }

    // 监听单元格编辑
    function editSort(event){
        priceCount(); //计算出当前总价格
    }
    /**
     * 删除商品
     */
    function deleteGoods(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId) {
            if (selectGoodsSkuId[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId.splice(i, 1);
            }
        }
    }

    function deleteGoods_moning(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_moning) {
            if (selectGoodsSkuId_moning[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_moning.splice(i, 1);
            }
        }
    }

    function deleteGoods_aftnoon(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_aftnoon) {
            if (selectGoodsSkuId_aftnoon[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_aftnoon.splice(i, 1);
            }
        }
    }

    function deleteGoods_canjian(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_canjian) {
            if (selectGoodsSkuId_canjian[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_canjian.splice(i, 1);
            }
        }
    }

    function deleteGoods_night(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_night) {
            if (selectGoodsSkuId_night[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_night.splice(i, 1);
            }
        }
    }

    function deleteGoods_evening(data) {
        var obj = $(data).parent().parent().parent();
        $(obj).remove();
        priceCount(); //计算出当前总价格
        for (var i in selectGoodsSkuId_evening) {
            if (selectGoodsSkuId_evening[i] == Number($(obj).attr("data-sku_id"))) {
                selectGoodsSkuId_evening.splice(i, 1);
            }
        }
    }

    /**
     * 计算总价
     */
    function priceCount() {
        var fenji = $("input[name='fenji']:checked").val();
        var c_fenji = $("input[name='c_fenji']:checked").val();
        var goods_name = [];
        var goods_nums = [];
        var goods_t_name = [];
        var goods_t_nums = [];
        var price_count = 0;
        var price_t_count = 0;
        //将所有时间调成不可见
        $('.earlymoring_time').hide();
        $('.moning_time').hide();
        $('.aftnoon_time').hide();
        $('.canjian_time').hide();
        $('.night_time').hide();
        $('.evening_time').hide();
        $("#goods").find("input[type='checkbox']").each(function(i) {
            //获取数量
            if ($(this).is(":checked")) {
                //显示时间
                var time = $(this).val();
                var timee = time.split('_')[0];
                if(timee=='teshu'){
                    var goods_num = Number($("input[name='" + $(this).val() + "']").val());
                    var price_one = Number($(this).parents("tr").find(".price-one").text());
                    goods_t_name.push($(this).val());
                    goods_t_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                    price_t_count += price_one * goods_num;
                }else{
                    var goods_num = Number($("input[name='" + $(this).val() + "']").val());
                    var price_one = Number($(this).parents("tr").find(".price-one").text());
                    goods_name.push($(this).val());
                    goods_nums.push(Number($("input[name='" + $(this).val() + "']").val()));
                    price_count += price_one * goods_num;
                }
                $("."+timee+"_time").show();
            }
        });
        var day = $(".combined-price").val();
        price_count = price_count * day;
        price_count = Number(price_count) + Number(price_t_count);
        $.ajax({
            type: 'POST',
            dataType: 'JSON',
            url: ns.url("shop/order/fuwufei"),
            data:   {fenji:fenji,c_fenji:c_fenji,day:day,goods_name:goods_name,goods_nums:goods_nums,goods_t_name:goods_t_name,goods_t_nums:goods_t_nums},
            async: false,
            success: function(res) {
                repeat_flag = false;
                if (res.code == 0) {
                    price_count = Number(price_count) + Number(res.data.money);
                    $(".service_money").text(res.data.remark);
                    //处理粉剂是否需要分装
                    if(res.data.fenji==1){
                        $("input[name='fenji']").eq(0).attr("checked",true);
                        $('#c_fenji').show();
                    }else{
                        $("input[name='fenji']").eq(1).attr("checked",true);
                        $('#c_fenji').hide();
                    }
                }
            }
        })
        $(".original-price").text(price_count.toFixed(2));


    }


    $("#bl_price").blur(function() {
        var bl_price = $(this).val();
        if (bl_price < 0) {
            layer.msg("价格不能小于0，可保留两位小数");
        }
    });

    /**
     * 计算组合套餐价格、原价、节省价
     */
    $(".combined-price").blur(function() {
        priceCount();
    });

    // 两个浮点数相减
    function accSub(num1, num2){
        var r1, r2, m;

        try{
            r1 = num1.toString().split(".")[1].length;
        }catch(e){
            r1 = 0;
        }

        try{
            r2 = num2.toString().split(".")[1].length;
        }catch(e){
            r2 = 0;
        }

        m = Math.pow(10, Math.max(r1, r2));
        n = (r1 >= r2) ? r1 : r2;
        return (Math.round(num1 * m - num2 * m) / m).toFixed(2);
    }

    //渲染商品主图列表
    function refreshGoodsImage() {
        var goods_image_template = $("#goodsImage").html();
        laytpl(goods_image_template).render(goodsImage, function (html) {
            $(".js-goods-image").html(html);
            //加载图片放大
            loadImgMagnify();

            if (goodsImage.length) {

                //预览
                $(".js-goods-image .js-preview").click(function () {
                    $(this).parent().prev().find("img").click();
                });

                //图片删除
                $(".js-goods-image .js-delete").click(function () {
                    var index = $(this).attr("data-index");
                    goodsImage.splice(index, 1);
                    refreshGoodsImage();
                });

                // 拖拽
                $('.js-goods-image .item').arrangeable({
                    //拖拽结束后执行回调
                    callback: function (e) {
                        var indexBefore = $(e).attr("data-index");//拖拽前的原始位置
                        var indexAfter = $(e).index();//拖拽后的位置
                        var temp = goodsImage[indexBefore];
                        goodsImage[indexBefore] = goodsImage[indexAfter];
                        goodsImage[indexAfter] = temp;

                        refreshGoodsImage();
                    }
                });
            }

            //最多传十张图
            if (goodsImage.length < GOODS_IMAGE_MAX) {
                $(".js-add-goods-image").show();
            } else {
                $(".js-add-goods-image").hide();
            }

        });
    }

    // 初始化富文本编辑器
    var richEditor;
    function initRichEditor() {
        richEditor = UE.getEditor('rich_content_editor', {
            initialFrameHeight: 400,
            serverUrl: ns.url("shop/ueditor/index")
        });

        richEditor.ready(function() {
            var content = $("#rich_content").val();
            if (content) {
                richEditor.setContent(content);
            }
        });

        richEditor.addListener("contentChange", function() {
            $("#rich_content").val(richEditor.getContent());
        });
    }

    // 初始化图片上传
    function initImageUpload() {
        layui.use(['upload'], function() {
            var upload = layui.upload;

            upload.render({
                elem: '.ns-upload-img-box',
                url: ns.url("shop/upload/image"),
                accept: 'images',
                acceptMime: 'image/*',
                done: function(res) {
                    if (res.code >= 0) {
                        var imgBox = $(this.elem).closest('.ns-upload-img');
                        imgBox.find('input[name="header_image"]').val(res.data.pic_path);
                        imgBox.find('img').attr('src', res.data.pic_path).show();
                        imgBox.find('.ns-upload-img-default').hide();
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        });
    }

    // 加载分类选项
    function loadCategoryOptions() {
        $.ajax({
            url: ns.url("shop/PrescriptionCategory/getOptions"),
            type: 'POST',
            dataType: 'JSON',
            success: function(res) {
                if (res.code == 0) {
                    var categorySelect = $("#category_id");
                    categorySelect.empty();
                    categorySelect.append('<option value="0">请选择分类</option>');

                    $.each(res.data, function(index, item) {
                        var selected = '';
                        if (item.value == "{$attr_class_info.category_id|default=0}") {
                            selected = 'selected';
                        }
                        categorySelect.append('<option value="' + item.value + '" ' + selected + '>' + item.text + '</option>');
                    });

                    // 重新渲染表单
                    layui.use(['form'], function() {
                        var form = layui.form;
                        form.render('select');
                    });
                }
            }
        });
    }

    // 页面加载完成后初始化
    $(document).ready(function() {
        // 初始化富文本编辑器
        setTimeout(function() {
            initRichEditor();
        }, 500);

        // 初始化图片上传
        initImageUpload();

        // 加载分类选项
        loadCategoryOptions();
    });

</script>

<!-- 引入UEditor富文本编辑器 -->
<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>

{/block}