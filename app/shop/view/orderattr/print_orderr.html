<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="__STATIC__/css/seller_center.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        body {
            background: #FFF none;
        }
    </style>
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印说明单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="$attr_class_info"}
<div class="print-layout">
    <div class="print-btn" id="printbtn" title="选择喷墨或激光打印机<br/>根据下列纸张描述进行<br/>设置并打印发货单据"><i></i><a href="javascript:void(0);">打印</a></div>
    <div class="a5-size"></div>
    <dl class="a5-tip">
        <dt>
            <h1>A5</h1>
            <em>Size: 210mm x 148mm</em></dt>
        <dd>当打印设置选择A5纸张、横向打印、无边距时每张A5打印纸可输出1页订单。</dd>
    </dl>
    <div class="a4-size"></div>
    <dl class="a4-tip">
        <dt>
            <h1>A4</h1>
            <em>Size: 210mm x 297mm</em></dt>
        <dd>当打印设置选择A4纸张、竖向打印、无边距时每张A4打印纸可输出2页订单。</dd>
    </dl>
    <div class="print-page">
        <div id="printarea">
            <div class="orderprint" style="width: 95%;">
                <style type="text/css">
                    table
                    {
                        border-collapse:collapse;
                    }
                    table, td, th
                    {
                        border:1px solid #B4C6E7;
                        line-height:18px;
                        font-size:10px;
                        padding:5px;
                        color: #364A93;
                    }
                    .headert td{
                        font-size: 18px;
                    }
                    .shuli{ margin:0 auto;writing-mode:vertical-lr;letter-spacing:8px;}
                </style>
                <div style="background:url(http://b.clinicsupplement.com/bei.png) no-repeat center top;height: 120px;width: 720px;background-size: 96% 90%;margin-left: 170px;">
                    <div style="font-size:0.80em; color: #364A93;margin-left:370px;font-weight: bold;"></div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:29px;font-weight: bold;">{$attr_class_info.class_name}</div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:6px;font-weight: bold;">{$attr_class_info.day}天</div>
                    <div style="font-size:1.00em; color: #364A93;margin-left:370px;padding-top:5px;font-weight: bold;"></div>
                </div>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[0] as $k => $v}
                        {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;" ></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                        {/foreach}
                    {/foreach}
                </table>
                {if isset($pages[1])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[1] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
                {if isset($pages[2])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[2] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
                {if isset($pages[3])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[3] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
                {if isset($pages[4])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[4] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
                {if isset($pages[5])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[5] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px" style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
                {if isset($pages[6])}
                <p class="normal" style="PAGE-BREAK-BEFORE:always;"></p>
                <table cellpadding="8" style="border-collapse:collapse;">
                    <thead>
                    <tr class="headert">
                        <td width="5%" align="center" ><b></b></td>
                        <td width="10%" align="center" style="line-height: 2.2"><b>产品</b></td>
                        <td width="10%" align="center" ><b>图片</b></td>
                        <td width="5%" align="center" > <b>剂量</b></td>
                        <td width="20%" align="center" ><b>主要成分</b></td>
                        <td width="50%" align="center" ><b>产品说明</b></td>
                    </tr>
                    </thead>
                    {foreach $pages[6] as $k => $v}
                    {foreach $v as $kk => $vo}
                    <tr>
                        {if $kk == 0}
                        {php}
                        $count = count($v);
                        $names = [1=>'晨起包',2=>'早餐包',3=>'午餐包',4=>'餐间包',5=>'晚餐包',6=>'睡前包'];
                        $name  = $names[$vo['type']];
                        {/php}
                        <td rowspan="{$count}" align="center" style="background-color: {$vo['color']};"><p class="shuli">{$name}</p></td>
                        {/if}
                        <td align="center">{$vo['sku_name']}<br />{$vo['goods_chi']}</td>
                        <td align="center"><img src="{:img($vo.sku_image)}" width="70px"  style="max-width:70px;max-height:70px;"></td>
                        <td align="center">{$vo['num']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introduction']}</td>
                        <td align="left" style="letter-spacing:1px;">{$vo['introd']}</td>
                    </tr>
                    {/foreach}
                    {/foreach}
                </table>
                {/if}
            </div>
        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });
    });
</script>
</html>