{extend name="base"/}
{block name="resources"}
<style>
    .template-detail-container {
        background: #fff;
        padding: 20px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .template-header {
        border-bottom: 1px solid #e6e6e6;
        padding-bottom: 20px;
        margin-bottom: 20px;
    }
    .template-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    .template-meta {
        color: #666;
        font-size: 14px;
    }
    .template-meta span {
        margin-right: 20px;
    }
    .template-image {
        text-align: center;
        margin: 20px 0;
    }
    .template-image img {
        max-width: 400px;
        max-height: 300px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .template-content {
        line-height: 1.8;
        color: #333;
        margin: 20px 0;
    }
    .template-content img {
        max-width: 100%;
        height: auto;
    }
    .back-btn {
        margin-top: 20px;
    }
</style>
{/block}
{block name="main"}
<div class="template-detail-container">
    <div class="template-header">
        <div class="template-title">{$template_info.class_name}</div>
        <div class="template-meta">
            <span>分类：{$template_info.category_name|default='未分类'}</span>
            <span>适用人群：{$template_info.age_type|default='通用'}</span>
            <span>价格：￥{$template_info.price|default='0.00'}</span>
            <span>创建时间：{$template_info.create_time|date='Y-m-d H:i:s'}</span>
        </div>
    </div>
    
    {if !empty($template_info.header_image)}
    <div class="template-image">
        <img src="{$template_info.header_image_url|default=''}" alt="{$template_info.class_name}">
    </div>
    {/if}
    
    {if !empty($template_info.rich_content)}
    <div class="template-content">
        {$template_info.rich_content|raw}
    </div>
    {/if}

    <div class="back-btn">
        <button class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
        {if $bp == 1}
        <button class="layui-btn ns-bg-color" onclick="location.href=ns.url('shop/orderattr/editgudingAttr?class_id={$template_info.class_id}')">编辑模板</button>
        {/if}
    </div>
</div>
{/block}
{block name="script"}
<script>
    // 页面加载完成后的处理
    $(document).ready(function() {
        // 处理富文本内容中的图片
        $('.template-content img').each(function() {
            $(this).css({
                'max-width': '100%',
                'height': 'auto',
                'display': 'block',
                'margin': '10px auto'
            });
        });
    });
</script>
{/block}
