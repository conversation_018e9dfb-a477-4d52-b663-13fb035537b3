{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" href="SHOP_CSS/order_detail.css"/>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/refund_detail.css" />
{/block}
{block name="main"}

<div class="order-detail">
    <div class="layui-row layui-col-space1 order-detail-info" >
        <div class="layui-col-md4 order-detail-left" >
            <div class="layui-card">
                <div class="layui-card-header nav-title">退款订单信息</div>
                <div class="layui-card-body">
                    <div class="layui-form">
                    	 <div class="layui-form-item">
                            <label class="layui-form-label">订单编号：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">{$order_info.order_no}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">退款编号：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">{$detail.refund_no}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">申请人：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">{$order_info.name}</div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">申请时间：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">
                                        <p>{:time_to_date($detail.refund_action_time)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-form-item order-detail-hr">

                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">付款方式：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">{$order_info.pay_type_name}</div>
                                </div>

                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">配送方式：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">
                                        <p>{$order_info['delivery_type_name']}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系电话：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">
                                        <p>{$order_info['mobile']}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item order-detail-hr">
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单类型：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">
                                        <p>{$order_info['order_type_name']}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">营销活动：</label>
                            <div class="layui-input-block">
                                <div class="layui-inline">
                                    <div class="layui-form-mid layui-word-aux">
                                        <p>{$order_info['promotion_type_name']}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md8 order-detail-operation">
            <div class="layui-card">
                <div class="layui-card-header">退款状态：{$detail.refund_status_name}</div>
                <div class="layui-card-body">
                    <p class="order-detail-tips"></p>
                    {if !empty($detail.refund_action)}
                    
					{foreach $detail.refund_action as $k => $v}
					<button type="button" class="layui-btn ns-bg-color" onclick="{$v.event}({$detail.order_goods_id});">{$v.title}</button>
					{/foreach}
                    
                    {/if}
                    <br>
                    <i class="layui-icon  layui-icon-about"></i>
                </div>
            </div>
        </div>
        <!--<div class="layui-col-md12">-->
        <!--<div class="layui-card">-->
        <!--<div class="layui-card-header">订单商品</div>-->
        <!--<div class="layui-card-body">-->
        <!---->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <div class="order-detail-dl">
            <dl>
                <dt>温馨提醒</dt>
                <dd>如果未发货，请点击同意退款给买家。</dd>
                <dd>如果实际已发货，请主动与买家联系。</dd>
                <dd>如果订单整体退款后，优惠券和余额会退还给买家。</dd>
            </dl>
        </div>
    </div>
</div>

<div>
    <div class="layui-row ns-form">
        <div class="layui-col-md4">
            <h4 class="refund-title">售后商品</h4>
            <ul class="refund-box">
                <li class="refund-item">
                    <div class="goods-item">
                        <div class="image-wrap">
							{if condition="$detail.sku_image"}
							<img alt="商品图片" layer-src src="{:img($detail.sku_image,'small')}">
							{/if}
                        </div>
                        <div class="detail-wrap">
                            <h4 class="title"><span>{$detail.sku_name}</span></h4>
                            <p class="gray"></p>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="layui-col-md4">
            <h4 class="refund-title">售后信息</h4>
            <ul class="refund-box">
                <li class="refund-item">
                    <label class="refund-label">退款方式：</label>
                    <div class="refund-content"><span class="refund-money">{if $detail.refund_type == 1}仅退款{else/}退货退款{/if}</span></div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">退款金额：</label>
                    <div class="refund-content"><span class="refund-money">￥{$detail.refund_apply_money}</span> </div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">联系方式：</label>
                    <div class="refund-content">{$order_info.mobile}</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">退款原因：</label>
                    <div class="refund-content">{$detail.refund_reason}</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">退款说明：</label>
                    <div class="refund-content">{$detail.refund_remark}</div>
                </li>
            </ul>
        </div>
        <div class="layui-col-md4">
            <h4 class="refund-title">购买信息</h4>
            <ul class="refund-box">
                <li class="refund-item">
                    <label class="refund-label">商品单价：</label>
                    <div class="refund-content"><span class="refund-money">￥{$detail.price}</span> x{$detail.num}件</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">实付金额：</label>
                    <div class="refund-content"><span class="refund-money">￥{$detail.real_goods_money}</span></div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">配送状态：</label>
                    <div class="refund-content">{$detail.delivery_status_name} </div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">订单编号：</label>
                    <div class="refund-content"> <a target="_blank"class="ns-text-color" href="{:addon_url('shop/order/detail',['order_id'=>$order_info['order_id']])}">{$order_info.order_no}</a></div>
                </li>
            </ul>
        </div>

    </div>

    {if $detail.refund_type == 2 && $detail.refund_status > 1 && $detail.refund_delivery_no != ''}
    <div class="refund-block ns-form">
        <h3 class="refund-block-title">退货物流</h3>
        <div class="refund-block-content">
            <ul class="refund-box">
                <li class="refund-item">
                    <label class="refund-label">物流公司：</label>
                    <div class="refund-content">{$detail.refund_delivery_name}</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">物流单号：</label>
                    <div class="refund-content">{$detail.refund_delivery_no}</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">物流说明：</label>
                    <div class="refund-content">{$detail.refund_delivery_remark}</div>
                </li>
                <li class="refund-item">
                    <label class="refund-label">是否入库：</label>
                    <div class="refund-content">{if $detail.is_refund_stock == 1}入库{else/}不入库{/if}</div>
                </li>
            </ul>
        </div>
    </div>
    {/if}
    <div class="refund-block ns-form">
        <h3 class="refund-block-title">售后日志</h3>
        <div class="refund-block-content">
            <ul class="layui-timeline">
                {foreach $detail['refund_log_list'] as $log_k => $log_item}
                <li class="layui-timeline-item">
                    {if $log_item["action_way"] == 1}
                    <span class="refund-way layui-timeline-axis refund-buyer">买</span>
                    {elseif $log_item["action_way"] == 2 /}
                    <span class="refund-way layui-timeline-axis seller-buyer ns-bg-color">商</span>
                    {else /}
                    <span class="refund-way layui-timeline-axis platform-buyer">平</span>
                    {/if}
                    <div class="layui-timeline-content layui-text">
                        <div class="layui-timeline-title">{$log_item.action}<span style="display:inline-block;float:right;margin-right:40px;">{:time_to_date($log_item.action_time)}</span></div>
                    </div>
                </li>
                {/foreach}
            </ul>
        </div>
    </div>
</div>

{/block}
{block name="script"}
<!-- 维权操作 -->
{include file="orderrefund/refund_action" /}
{/block}