<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>药房库存管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
    <link rel="stylesheet" href="__STATIC__/shop/css/pharmacy.css">
</head>
<body>
<div class="pharmacy-container">
    <!-- 顶部导航 -->
    <div class="pharmacy-header">
        <h1 class="pharmacy-title">药房库存管理系统</h1>
        <div class="pharmacy-nav">
            <div class="layui-tab" lay-filter="pharmacy-tab">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="split">拆零操作</li>
                    <li lay-id="production">生产计划</li>
                    <li lay-id="stockin">入库操作</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 顶部操作按钮区 -->
    <div class="pharmacy-toolbar">
        <button class="layui-btn layui-btn-normal" id="viewOperationLogBtn">
            <i class="layui-icon layui-icon-list"></i> 查看操作记录
        </button>
    </div>

    <!-- 主要内容区 -->
    <div class="pharmacy-main">
        <!-- 左侧操作区 -->
        <div class="left-panel">
            <!-- 操作区域 -->
            <div class="operation-section">
                <div class="section-title">操作区域</div>
                <div class="operation-content" id="operationContent">
                    <!-- 拆零操作界面 -->
                    <div class="operation-panel" id="splitPanel">
                        <div class="operation-form">
                            <div class="form-group">
                                <label>扫描或输入SKU编码：</label>
                                <input type="text" class="scanner-input" id="splitSkuInput" placeholder="请扫描或输入SKU编码" autofocus>
                            </div>
                            <div class="operation-tips">
                                <i class="layui-icon layui-icon-tips"></i>
                                <span>扫描后添加到列表，最后统一确认拆零</span>
                            </div>

                            <!-- 拆零项目列表 -->
                            <div class="items-list" id="splitItemsList">
                                <div class="list-header">
                                    <span>待拆零项目列表</span>
                                    <button class="layui-btn layui-btn-xs layui-btn-danger" id="clearSplitListBtn">清空列表</button>
                                </div>
                                <div class="list-content" id="splitListContent">
                                    <div class="empty-tip">暂无项目，请扫描添加</div>
                                </div>
                                <div class="list-footer">
                                    <div class="summary-info" id="splitSummary">
                                        总计：<span id="splitTotalBottles">0</span>瓶，涉及<span id="splitTotalProducts">0</span>种产品
                                    </div>
                                    <button class="layui-btn layui-btn-normal" id="confirmSplitBtn" disabled>确认拆零</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生产计划界面 -->
                    <div class="operation-panel" id="productionPanel" style="display: none;">
                        <div class="production-form">
                            <div class="form-group">
                                <label>选择处方模板：</label>
                                <select class="template-select" id="templateSelect">
                                    <option value="">请选择处方模板</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>生产数量：</label>
                                <input type="number" class="quantity-input" id="productionQuantity" placeholder="请输入生产套数" min="1">
                            </div>
                            <button class="production-btn" id="startProductionBtn" disabled>开始生产</button>
                        </div>
                    </div>

                    <!-- 入库操作界面 -->
                    <div class="operation-panel" id="stockinPanel" style="display: none;">
                        <div class="operation-form">
                            <div class="form-group">
                                <label>扫描或输入SKU编码：</label>
                                <input type="text" class="scanner-input" id="stockinSkuInput" placeholder="请扫描或输入SKU编码">
                            </div>
                            <div class="form-group">
                                <label>入库数量（瓶）：</label>
                                <input type="number" class="quantity-input" id="stockinQuantity" value="1" min="1">
                            </div>
                            <div class="operation-tips">
                                <i class="layui-icon layui-icon-tips"></i>
                                <span>扫描后添加到列表，最后统一确认入库</span>
                            </div>

                            <!-- 入库项目列表 -->
                            <div class="items-list" id="stockinItemsList">
                                <div class="list-header">
                                    <span>待入库项目列表</span>
                                    <button class="layui-btn layui-btn-xs layui-btn-danger" id="clearStockinListBtn">清空列表</button>
                                </div>
                                <div class="list-content" id="stockinListContent">
                                    <div class="empty-tip">暂无项目，请扫描添加</div>
                                </div>
                                <div class="list-footer">
                                    <div class="summary-info" id="stockinSummary">
                                        总计：<span id="stockinTotalBottles">0</span>瓶，涉及<span id="stockinTotalProducts">0</span>种产品
                                    </div>
                                    <button class="layui-btn layui-btn-normal" id="confirmStockinBtn" disabled>确认入库</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧单据区 -->
        <div class="right-panel">
            <!-- 抽屉式摘要面板 - 用于拆零和入库标签页 -->
            <div class="panel-summary" id="drawerSummaryPanel">
                <div class="summary-card" id="splitSummary">
                    <h4>拆零单</h4>
                    <div class="summary-count">0</div>
                    <button class="layui-btn layui-btn-sm" onclick="openDrawer('split')">查看详情</button>
                </div>
                <div class="summary-card" id="stockinSummary">
                    <h4>入库单</h4>
                    <div class="summary-count">0</div>
                    <button class="layui-btn layui-btn-sm" onclick="openDrawer('stockin')">查看详情</button>
                </div>
            </div>

            <!-- 传统布局面板 - 用于生产计划标签页 -->
            <div class="traditional-panel" id="traditionalPanel" style="display: none;">
                <div class="order-section">
                    <div class="section-title">生产计划</div>
                    <div class="plan-content" id="productionPlanContent">
                        <div class="empty-tip">请选择处方模板和生产数量</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 抽屉式详情面板 -->
<div class="drawer-overlay" id="drawerOverlay" onclick="closeDrawer()"></div>
<div class="drawer-container" id="drawerContainer">
    <div class="drawer-header">
        <div class="drawer-title" id="drawerTitle">详情</div>
        <button class="drawer-close" onclick="closeDrawer()">×</button>
    </div>
    <div class="drawer-content" id="drawerContent">
        <!-- 动态内容 -->
    </div>
</div>

<!-- 操作记录弹窗 -->
<div id="operationLogModal" style="display: none; padding: 20px;">
    <div class="log-content">
        <table class="layui-table" id="operationLogTable" lay-filter="operationLogTable"></table>
    </div>
</div>

<!-- 单据详情弹窗 -->
<div id="orderDetailModal" style="display: none; padding: 20px;">
    <div class="order-detail-content" id="orderDetailContent">
        <!-- 动态内容 -->
    </div>
</div>

<!-- 生产详情弹窗 -->
<div id="productionDetailModal" style="display: none; padding: 20px;">
    <div class="production-detail-content">
        <div class="detail-section">
            <h3>基本信息</h3>
            <div class="detail-info" id="basicInfo"></div>
        </div>
        <div class="detail-section">
            <h3>消耗详情</h3>
            <div class="detail-table" id="consumedTable"></div>
        </div>
        <div class="detail-section">
            <h3>产生散药</h3>
            <div class="detail-table" id="producedTable"></div>
        </div>
    </div>
</div>

<script src="__STATIC__/js/jquery-3.1.1.js"></script>
<script src="__STATIC__/ext/layui/layui.js"></script>
<script src="__STATIC__/shop/js/pharmacy-enhanced.js"></script>
</body>
</html>
