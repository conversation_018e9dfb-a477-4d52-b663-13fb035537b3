<!DOCTYPE html>
<html>
<head>
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
    <title>生产端管理系统</title>
    <!--
    登录验证配置说明：
    如需启用登录验证，请修改 app/shop/controller/Production.php 文件中的 REQUIRE_LOGIN 常量为 true
    默认为 false，无需登录即可访问
    -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="__STATIC__/img/shop_bitbug_favicon.ico" />
    <link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css" />
    <link rel="stylesheet" href="SHOP_CSS/production.css"/>
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
</head>
<body>
<div class="production-container">
    <!-- 搜索区域 -->
    <div class="search-section">
        <div class="search-wrapper">
            <h1 class="production-title">生产端管理系统</h1>
            <div class="system-info">
                <span class="current-time" id="currentTime"></span>
            </div>
            <div class="search-box">
                <input type="text" id="orderSearch" class="search-input" placeholder="请扫描或输入订单号..." autocomplete="off">
                <button type="button" class="search-btn" id="searchBtn">
                    <i class="layui-icon layui-icon-search"></i>
                </button>
            </div>
            <div class="search-tips">
                <span>支持扫描枪输入，扫描完成后自动搜索</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 订单信息显示区域 -->
        <div class="order-info" id="orderInfo" style="display: none;">
            <div class="order-header">
                <h3>订单信息</h3>
                <span class="order-no" id="orderNo"></span>
            </div>
            <div class="order-details">
                <span class="order-customer" id="orderCustomer"></span>
                <span class="order-status" id="orderStatus"></span>
                <span class="order-time" id="orderTime"></span>
            </div>
        </div>

        <!-- 卡片区域 -->
        <div class="cards-container">
            <!-- 左侧动态卡片 -->
            <div class="left-cards">
                <h4 class="cards-title">订单操作</h4>
                <div class="cards-grid" id="leftCards">
                    <!-- 搜索前的提示 -->
                    <div class="search-prompt" id="searchPrompt">
                        <div class="prompt-icon">
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <p>请扫描或输入订单号查看订单操作</p>
                    </div>
                </div>
            </div>

            <!-- 右侧静态卡片 -->
            <div class="right-cards">
                <h4 class="cards-title">系统功能</h4>
                <div class="cards-grid" id="rightCards">
                    {volist name="right_cards" id="card"}
                    <div class="card card-large card-{$card.color}" onclick="handleCardClick('{$card.url}', '{$card.target}')">
                        <div class="card-icon">
                            <i class="layui-icon {$card.icon}"></i>
                        </div>
                        <div class="card-content">
                            <h5>{$card.title}</h5>
                            <p>{$card.description}</p>
                        </div>
                    </div>
                    {/volist}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 搜索输入框
    var searchInput = document.getElementById('orderSearch');
    var searchBtn = document.getElementById('searchBtn');
    var orderInfo = document.getElementById('orderInfo');
    var searchPrompt = document.getElementById('searchPrompt');
    
    // 回车搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 点击搜索
    searchBtn.addEventListener('click', performSearch);
    
    // 执行搜索
    function performSearch() {
        var orderNo = searchInput.value.trim();
        if (!orderNo) {
            layer.msg('请输入订单号');
            return;
        }
        
        // 显示加载状态
        var loadingIndex = layer.load(2);
        
        // 发送搜索请求
        $.ajax({
            url: '{:url("shop/Production/search")}',
            type: 'POST',
            data: { order_no: orderNo },
            dataType: 'json',
            success: function(res) {
                layer.close(loadingIndex);
                if (res.code === 0) {
                    displaySearchResult(res.data);
                } else {
                    layer.msg(res.msg);
                    showSearchPrompt();
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('搜索失败，请重试');
                showSearchPrompt();
            }
        });
    }
    
    // 显示搜索结果
    function displaySearchResult(data) {
        var orderInfo = data.order_info;
        var leftCards = data.left_cards;
        
        // 更新订单信息
        document.getElementById('orderNo').textContent = orderInfo.order_no;
        document.getElementById('orderCustomer').textContent = '客户：' + orderInfo.name;
        document.getElementById('orderStatus').textContent = '状态：' + orderInfo.order_status_name;
        document.getElementById('orderTime').textContent = '下单时间：' + formatTime(orderInfo.create_time);
        
        // 生成左侧卡片
        generateLeftCards(leftCards);

        // 显示订单信息，隐藏搜索提示
        searchPrompt.style.display = 'none';
        orderInfo.style.display = 'block';
    }
    
    // 生成左侧卡片
    function generateLeftCards(cards) {
        var leftCardsContainer = document.getElementById('leftCards');
        leftCardsContainer.innerHTML = '';
        
        cards.forEach(function(card) {
            var cardElement = document.createElement('div');
            cardElement.className = 'card card-small card-' + card.color;
            cardElement.innerHTML = 
                '<div class="card-icon">' +
                    '<i class="layui-icon ' + card.icon + '"></i>' +
                '</div>' +
                '<div class="card-content">' +
                    '<h5>' + card.title + '</h5>' +
                '</div>';
            
            cardElement.addEventListener('click', function() {
                window.open(card.url, card.target);
            });
            
            leftCardsContainer.appendChild(cardElement);
        });
    }
    
    // 显示搜索提示
    function showSearchPrompt() {
        orderInfo.style.display = 'none';
        searchPrompt.style.display = 'block';
        // 清空左侧卡片
        var leftCardsContainer = document.getElementById('leftCards');
        leftCardsContainer.innerHTML = '<div class="search-prompt" id="searchPrompt"><div class="prompt-icon"><i class="layui-icon layui-icon-search"></i></div><p>请扫描或输入订单号查看订单操作</p></div>';
    }
    
    // 格式化时间
    function formatTime(timestamp) {
        var date = new Date(timestamp * 1000);
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0') + ' ' +
               String(date.getHours()).padStart(2, '0') + ':' + 
               String(date.getMinutes()).padStart(2, '0');
    }
    
    // 页面加载完成后聚焦搜索框
    searchInput.focus();

    // 显示当前时间
    function updateTime() {
        var now = new Date();
        var timeString = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');
        document.getElementById('currentTime').textContent = timeString;
    }

    // 每秒更新时间
    updateTime();
    setInterval(updateTime, 1000);

    // 处理卡片点击事件
    window.handleCardClick = function(url, target) {
        if (url === '#') {
            layer.msg('功能开发中...');
            return;
        }

        if (target === '_blank') {
            window.open(url, target);
        } else {
            window.location.href = url;
        }
    };
});
</script>
</body>
</html>
