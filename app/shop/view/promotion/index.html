{extend name="base"/}
{block name="resources"}
<style>
	.ns-card-common:first-child{margin-top: 0;}
	.ns-item-block-parent .ns-item-poa-pic{background-image: linear-gradient(to right, #fb8700, #fb6400);text-align: center;color: #FFFFFF;width: 70px;height: 28px;line-height: 28px;border-bottom-left-radius: 3px;}
	.layui-card-body{padding: 0 !important;}
	.ns-item-block .item-hide{
		position: absolute;
		top: 0;
		right: -1px;
	}


</style>
{/block}
{block name="main"}

<div class="layui-card ns-card-common ns-card-brief" id="promotion">
	<div class="layui-card-header ">
		<span class="ns-card-title">店铺促销</span>
	</div>
	
	<div class="layui-card-body">
		<div class="ns-item-block-parent ns-item-five" id="promotion-item">
			{foreach $promotion as $list_k => $list_v}
			{if  condition="$list_v['show_type'] eq 'shop'"}
			{empty name="$list_v['is_developing']"}
			<a class="ns-item-block ns-item-block-hover-a" href="{:addon_url($list_v['url'])}">
				<div class="ns-item-block-wrap">
					<div class="ns-item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="ns-item-con">
						<div class="ns-item-content-title">{$list_v.title}</div>
						<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
				</div>
			</a>
			{else/}
			<a class="ns-item-block ns-item-block-hover-a" href="#">
				<div class="ns-item-block-wrap">
					<div class="ns-item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="ns-item-con">
						<div class="ns-item-content-title">{$list_v.title}</div>
						<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
					<div class="ns-item-poa-pic">
						敬请期待
					</div>
				</div>
			</a>
			{/empty}
			{/if}
			{/foreach}
			{foreach $shop_addon as $shop_addon_k => $shop_addon_v}

			{/foreach}
		</div>
	</div>
</div>

<div class="layui-card ns-card-common ns-card-brief" id="interaction">
	<div class="layui-card-header">
		<span class="ns-card-title">会员互动</span>
	</div>
	
	<div class="layui-card-body">
		<div class="ns-item-block-parent ns-item-five">
			{foreach $promotion as $list_k => $list_v}
			{if  condition="$list_v['show_type'] eq 'member'"}
			{empty name="$list_v['is_developing']"}
			<a class="ns-item-block ns-item-block-hover-a" href="{:addon_url($list_v['url'])}">
				<div class="ns-item-block-wrap">
					<div class="ns-item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="ns-item-con">
						<div class="ns-item-content-title">{$list_v.title}</div>
						<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
				</div>
			</a>
			{else/}
					<a class="ns-item-block ns-item-block-hover-a" href="#">
				<div class="ns-item-block-wrap">
					<div class="ns-item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="ns-item-con">
						<div class="ns-item-content-title">{$list_v.title}</div>
						<p class="ns-item-content-desc ns-line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
					<div class="ns-item-poa-pic">
						敬请期待
					</div>
				</div>
			</a>
			{/empty}
			{/if}
			{/foreach}
			{foreach $member_addon as $member_addon_k => $member_addon_v}
			{/foreach}
		</div>
	</div>
</div>
{/block}
{block name="script"}
<script>

	var promotion_items = $("#promotion a").length,
		extend_items = $("#extend a").length,
		interaction_items = $("#interaction a").length,
		tool_items = $("#tool a").length;
	if (promotion_items == 0) {
		$("#promotion").hide();
	}
	if (extend_items == 0) {
		$("#extend").hide();
	}
	if (interaction_items == 0) {
		$("#interaction").hide();
	}
	if (tool_items == 0) {
		$("#tool").hide();
	}

</script>
{/block}
