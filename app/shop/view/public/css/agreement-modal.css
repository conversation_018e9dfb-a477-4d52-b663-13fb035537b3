.agreement-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.agreement-modal {
    width: 90%;
    max-width: 420px;
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.agreement-avatar {
    width: 100px;
    height: 100px;
    margin-bottom: 15px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f0f0f0;
}

.agreement-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.agreement-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.agreement-content {
    font-size: 14px;
    line-height: 1.8;
    color: #666;
    margin-bottom: 25px;
    text-align: left;
}

.agree-button {
    width: 100%;
    padding: 12px 0;
    background-color: #6c5ce7;
    color: white;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.agree-button:hover {
    background-color: #5b4bc9;
}