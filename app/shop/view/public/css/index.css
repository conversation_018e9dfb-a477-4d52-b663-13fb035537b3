.layui-layout-admin .layui-body .ns-body-content {
	min-width: 1200px;
	padding: 0 !important;
}

@media screen and (max-width: 1270px) {
	.ns-survey-info .layui-card-body .ns-survey-detail-con {
		flex-basis: 33% !important;
	}
}

/* 到期提示 */
.expire-hint{
    display: flex;
    align-items: center;
    padding: 15px 30px;
    height: 75px;
    line-height: 75px;
    background-color: #fff9f5;
    box-sizing: border-box;
}
.expire-hint .expire-logo{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    width: 40px;
    height: 40px;
}
.expire-hint .expire-logo img{
    max-height: 100%;
    max-width: 100%;
}
.expire-hint .expire-center{
    line-height: 1;
}
.expire-hint .expire-center h3{
    font-size: 15px;
    font-weight: 700;
}
.expire-hint .expire-center p{
    margin-top: 10px;
    color:#5d5c5c;
}
.expire-hint .expire-center p span:nth-of-type(2){
    margin-left: 15px;
}
.expire-hint .expire-center h3 a{
    font-size: 12px;
    font-weight: normal;
    margin-left: 5px;
    cursor: pointer;
}

/* 内容 */
.ns-survey{
    display: flex;
    padding: 30px 40px;
}
.ns-survey-left{flex: 1;padding-right: 15px;}
.ns-survey-right{width: 240px;}

/* 营销插件 */
.ns-card-common{
    border: 1px solid #f1f1f1;
}
.ns-card-common .layui-card-header{
    border: 0;
}
.ns-card-common .layui-card-body{
    padding-top: 0;
    padding-bottom: 0;
}
.ns-item-block-parent .ns-item-block{
    background-color: transparent;
}
.ns-item-block-parent .ns-item-pic{
    overflow: hidden;
    border-radius: 5px;
}
.ns-item-block-parent{
    padding: 0;
}

/* 商家信息 */
.ns-survey-item .ns-survey-info{
    flex: 1;
}
.ns-survey-item{
    display: flex;
    justify-content: space-between;
}
.ns-survey-item .ns-survey-shop{
    display: flex;
    width: 350px;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #f1f1f1;
}
.ns-survey-item .ns-survey-shop .ns-item-pic{
    display: flex;
    width: 65px;
    height: 65px;
    justify-content: center;
    align-items: center;
}
.ns-survey-item .ns-survey-shop .ns-item-pic img{
    max-width: 100%;
    max-height: 100%;
}
.ns-survey-item .ns-survey-shop .ns-surver-shop-detail{
    margin-left: 15px;
    flex: 1;
}
.ns-survey-item .ns-survey-shop .ns-survey-shop-name{
    font-size: 16px;
    font-weight: 600;
    color: rgba(38,38,38,1);
    height: 25px !important;
    line-height: 25px !important;
    margin-bottom: 10px;
}
.ns-survey-item .ns-survey-shop .ns-survey-shop-name span{
    border: 1px solid #999;
    padding: 1px 2px;
    line-height: 1;
    color: #999;
    font-size: 12px;
    margin-left: 10px;
    border-radius: 2px;
    cursor: pointer;
}
.ns-survey-item .ns-survey-shop .ns-surver-shop-detail p{
    line-height: 30px;
    color: #595959;
}
.ns-survey-item .ns-survey-shop .ns-surver-shop-detail p:nth-of-type(2){
    font-size: 12px;
    color: #B7B8B7;
}

/* 概况信息 */
.ns-survey-info{
    margin: 0;
}
.ns-survey-info .layui-card-body{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 0 40px !important;
}
.ns-survey-info .layui-card-body .ns-survey-detail-con{
    flex-basis: 25%;
    margin-bottom: 20px;
}
.ns-survey-info .layui-card-body .ns-survey-detail-aco{
    font-weight:400;
    color:rgba(89,89,89,1);
}
.ns-survey-info .layui-card-body .ns-survey-detail-num{
    margin: 10px 0;
    font-size: 28px;
    font-weight:400;
    color:rgba(89,89,89,1);
}
.ns-survey-info .layui-card-body .ns-survey-detail-yesterday{
    font-size:14px;
    font-weight:400;
    color:rgba(175,175,175,1);
}

/*  */
.ns-survey-right .layui-card{
    border: 1px solid #f1f1f1;
    box-shadow: inherit;
}

.ns-survey-right .layui-card .layui-card-body{
    overflow: hidden;
    text-overflow: ellipsis;
}

.ns-shop-state{
    margin: 30px 40px 0;
    padding: 10px;
    padding-right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 35px;
    background-color: rgba(255,129,67,0.1);
    color: #fff;
    border: 1px solid red;
}
.ns-shop-state p{
    color: #666;
}
.ns-shop-state p i{
    margin-right: 6px;
}
.ns-shop-state button{
    border: none;
    background-color: transparent;
    cursor: pointer;
}
