/* 生产端页面样式 */
body {
    margin: 0;
    padding: 0;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
}

.production-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 0;
    margin: 0;
}

/* 搜索区域 */
.search-section {
    padding: 80px 20px 60px;
    text-align: center;
    background: transparent;
}

.search-wrapper {
    max-width: 600px;
    margin: 0 auto;
}

.production-title {
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 300;
    letter-spacing: 2px;
}

.system-info {
    margin-bottom: 30px;
}

.current-time {
    font-size: 16px;
    color: #666;
    background: rgba(255,255,255,0.8);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
}

.search-box {
    position: relative;
    margin-bottom: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 50px;
    overflow: hidden;
    background: white;
}

.search-input {
    width: 100%;
    height: 60px;
    border: none;
    outline: none;
    padding: 0 80px 0 30px;
    font-size: 18px;
    background: transparent;
    color: #333;
}

.search-input::placeholder {
    color: #999;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 5px;
    width: 50px;
    height: 50px;
    border: none;
    background: #1E9FFF;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
}

.search-btn:hover {
    background: #0078d4;
    transform: scale(1.05);
}

.search-tips {
    color: #666;
    font-size: 14px;
}

/* 主要内容区域 */
.main-content {
    padding: 0 20px 40px;
    max-width: 1400px;
    margin: 0 auto;
}

/* 订单信息 */
.order-info {
    background: white;
    border-radius: 12px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.order-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.order-no {
    font-size: 18px;
    font-weight: bold;
    color: #1E9FFF;
}

.order-details {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.order-details span {
    color: #666;
    font-size: 14px;
}

/* 卡片容器 */
.cards-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.left-cards {
    flex: 0 0 60%;
}

.right-cards {
    flex: 0 0 40%;
}

.cards-title {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    font-weight: 500;
}

.cards-grid {
    display: grid;
    gap: 15px;
}

/* 左侧小卡片网格 */
.left-cards .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 右侧大卡片网格 */
.right-cards .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 卡片基础样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid transparent;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 小卡片样式 */
.card-small {
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: 80px;
}

.card-small .card-icon {
    flex-shrink: 0;
}

.card-small .card-content h5 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

/* 大卡片样式 */
.card-large {
    text-align: center;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card-large .card-icon {
    margin-bottom: 15px;
}

.card-large .card-content h5 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #333;
}

.card-large .card-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* 卡片图标 */
.card-icon i {
    font-size: 24px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    text-align: center;
    color: white;
}

/* 卡片颜色主题 */
.card-primary {
    border-left-color: #1E9FFF;
}
.card-primary .card-icon i {
    background: #1E9FFF;
}

.card-normal {
    border-left-color: #5FB878;
}
.card-normal .card-icon i {
    background: #5FB878;
}

.card-warm {
    border-left-color: #FFB800;
}
.card-warm .card-icon i {
    background: #FFB800;
}

.card-danger {
    border-left-color: #FF5722;
}
.card-danger .card-icon i {
    background: #FF5722;
}

/* 搜索提示 */
.search-prompt {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    background: rgba(255,255,255,0.5);
    border-radius: 12px;
    border: 2px dashed #ddd;
}

.prompt-icon i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
    display: block;
}

.search-prompt p {
    font-size: 16px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .cards-container {
        flex-direction: column;
    }
    
    .left-cards,
    .right-cards {
        flex: none;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .production-title {
        font-size: 24px;
    }
    
    .search-input {
        height: 50px;
        font-size: 16px;
        padding: 0 70px 0 20px;
    }
    
    .search-btn {
        width: 40px;
        height: 40px;
        right: 5px;
        top: 5px;
    }
    
    .order-details {
        flex-direction: column;
        gap: 10px;
    }
    
    .left-cards .cards-grid {
        grid-template-columns: 1fr;
    }
    
    .right-cards .cards-grid {
        grid-template-columns: 1fr;
    }
    
    .card-small {
        min-height: 70px;
    }
    
    .card-large {
        min-height: 100px;
    }
}
