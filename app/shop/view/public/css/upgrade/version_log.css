.log-content-block{padding:20px;background-color:#FFF}
.log-content{display:flex;margin-left: 120px;}
.log-step-text{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;margin-left: 10px;}
.log-step-text-title{
    line-height:16px!important;
    color:#000!important;
    font-size:14px!important;
    padding-bottom:8px;
}
.log-step-text-content{color:#999;font-size:12px}
.log-update{
    border:1px solid #DAE7FF;
    background-color: #DAE7FF;
    width: 30px;
    text-align: center;
    margin-right: 10px;
    line-height: 20px;
}
.log-detail{
    color:#000;
    font-size:12px;
    display: flex;
    align-items: baseline;
    padding-top: 8px;
}
.log-no-block{padding:20px}
.log-body{
    margin-left: 60px;
    margin-bottom:26px;
}
.ns-log-version-date{
    left: -120px;
    width: 100px;
    color: #000 !important;
    font-size: 16px !important;
}
.layui-timeline-axis{
    z-index: 1 !important;
}
.layui-icon{

}
.layui-icon.layui-timeline-axis{
    color:#fff !important;
    width: 12px;
    height: 12px;
    line-height: 12px;
    padding: 2px;
    font-size: 9px;
    text-align: center;
    left:-3px;
}
.load-more .log-step-text-title{
    cursor: pointer;
}

.load-more-img{
    position: absolute;
    width: 20px;
    left: -4px;
}
.open{
    display: none
}