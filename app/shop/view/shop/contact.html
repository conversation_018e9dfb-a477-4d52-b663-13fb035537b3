{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<style>
	.selected-products, .sales-products, .warehouse-products {
		width: 50%;
		float: left;
		padding: 20px;
		box-sizing: border-box;
	}
</style>
<div class="container">
	<!-- 左侧：已选择的商品列表 -->
	<div class="selected-products">
		<h2>采购单</h2>
		<div class="layui-form-item">
			<div>
			</div>
		</div>
		<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
			<colgroup>
				<col width="10%">
				<col width="10%">
				<col width="10%">
				<col width="10%">
			</colgroup>
			<thead>
			<tr>
				<th>采购单编号</th>
				<th>时间</th>
				<th>出库状态</th>
				<th>操作</th>
			</tr>
			</thead>
			<tbody>
			{foreach name="$sor" item="vo" key="k"}
			<tr>
				<td>{$vo.number}</td>
				<td>{$vo.time}</td>
				<td>{$vo.state}</td>
				<td><span onclick="saleb({$vo.id})">详情</span></td>
			</tr>
			{if $vo.child}
			{foreach name="$vo.child" item="v" key="kk"}
			<tr>
				<td>--包裹{$v.number}</td>
				<td>{$v.time}</td>
				{if $v.ruku==1}
				<td>已入库</td>
				<td><span onclick="showStockInDetail('{$v.number}')">查看记录</span></td>
				{else /}
				<td></td>
				<td><span onclick="goodsb({$v.id},'{$v.number}')">入库</span></td>
				{/if}

			</tr>
			{/foreach}
			{/if}
			{/foreach}
			</tbody>
		</table>
	</div>

	<!-- 中间：按销售量排序的商品列表 -->
	<div class="sales-products">
		<h2>包裹订单瓶数--<span id="jihua"></span> / 实际瓶数--<span id="shiji"></span></h2>
		<input type="hidden" id="package" name="package"  value=""/>
		<div class="layui-form-item">
			<div>
				<input id="scannerInput" class="layui-input" autofocus placeholder="请扫码" style="float: left;width: 50%">
				<button class="layui-btn ns-bg-color" onclick="clearn()">重新扫码</button>
				<button id="order" class="layui-btn ns-bg-color" onclick="ruku()">入库</button>
			</div>
		</div>
		<table class="layui-table" id="goodssale" lay-skin="line" lay-size="lg">
			<colgroup>
				<col width="10%">
				<col width="10%">
				<col width="10%">
				<col width="10%">
			</colgroup>
			<thead>
			<tr>
				<th>商品名称</th>
				<th>编号</th>
				<th>需求数</th>
				<th>出库数</th>
			</tr>
			</thead>
			<tbody>
			<tr>
			</tr>
			</tbody>
		</table>
	</div>

</div>

<script>
	var selectGoodsSkuId = [];
	var shiji = 0;
	function saleb(saleb){
		$.ajax({
			url: '/api/cart/count.html',
			type: 'GET',
			dataType: 'json',
			data: {
				saleb:saleb
			},
			success: function(res) {
				$("#goodssale tbody").html('');
				var html = '';
				var earlymoning  = res.salelist;
				for (var i in earlymoning) {
					var item = earlymoning[i];
					html += "<tr>";
					html += "<td>" + item.name + "</td>";
					html += "<td>" + item.number + "</td>";
					html += "<td>" + item.nums + "</td>";
					html += "<td>" + item.handle + "</td>";
					html += "</tr>";
				}
				$("#goodssale tbody").html(html);
				$("#jihua").text(res.nums);
				$("#shiji").text(res.handle);

			}
		});
	}

	function goodsb(saleb,numberr){
		$.ajax({
			url: '/api/cart/lists.html',
			type: 'GET',
			dataType: 'json',
			data: {
				saleb:saleb
			},
			success: function(res) {
				$("#goodssale tbody").html('');
				var html = '';
				var earlymoning  = res.salelist;
				for (var i in earlymoning) {
					var item = earlymoning[i];
					html += "<tr>";
					html += "<td>" + item.name + "</td>";
					html += "<td>" + item.number + "</td>";
					html += "<td></td>";
					html += "<td name='"+item.number+"' class='scan'></td>";
					html += "</tr>";
				}
				$("#goodssale tbody").html(html);
				$("#jihua").text(res.nums);
				$("#shiji").text(0);
				$("#package").val(numberr);

			}
		});
	}

	// 入库函数
	// 入库函数（带差异提示和备注功能）
	function ruku() {
		if (selectGoodsSkuId.length === 0) {
			layer.msg('请先扫码商品', {icon: 2});
			return;
		}

		// 获取当前包裹信息
		var packageId = $('#package').val();
		var jihua = parseInt($("#jihua").text()) || 0;
		var shiji = parseInt($("#shiji").text()) || 0;

		if (!packageId) {
			layer.msg('无法确定当前包裹', {icon: 2});
			return;
		}

		// 检查计划瓶数与实际瓶数是否一致
		if (jihua !== shiji) {
			// 显示差异确认弹窗
			layer.confirm(
					'计划瓶数(' + jihua + ')与实际瓶数(' + shiji + ')不一致，是否继续入库?',
					{
						title: '瓶数不一致提示',
						btn: ['继续入库', '取消'],
						area: ['400px', '200px'],
						content: '<div>' +
								'<div class="layui-form-item">' +
								'   <div >' +
								'       <textarea id="remarkContent" placeholder="请输入差异原因备注" class="layui-textarea" style="min-height:80px;"></textarea>' +
								'   </div>' +
								'</div>' +
								'</div>'
					},
					function(index) {
						// 确认继续入库
						layer.close(index);
						submitStockIn(packageId, jihua, shiji);
					},
					function(index) {
						// 取消入库
						layer.close(index);
					}
			);
		} else {
			// 瓶数一致，直接提交
			submitStockIn(packageId, jihua, shiji);
		}
	}

	// 提交入库单到服务器
	function submitStockIn(packageId, jihua, shiji) {
		// 获取备注内容
		var remark = $('#remarkContent').val() || '';

		// 显示加载中
		var loading = layer.load(2);

		// 准备提交数据
		var submitData = {
			package_id: packageId,
			package_no: $('#package').val(),
			scanned_items: JSON.stringify(selectGoodsSkuId),
			jihua: jihua,
			actual_count: shiji,
			remark: remark
		};

		$.ajax({
			url: '/api/cart/createStockIn.html',
			type: 'POST',
			dataType: 'json',
			contentType: 'application/json',
			data: JSON.stringify(submitData),
			success: function(res) {
				layer.close(loading);
				if (res.code === 0) {
					layer.msg('入库成功', {icon: 1}, function() {

						// 如果有入库单号，可以显示详情弹窗
						if (res.data.stock_in_no) {
							showStockInDetail(res.data.stock_in_no);
						}
					});
				} else {
					layer.msg(res.message || '入库失败', {icon: 2});
				}
			},
			error: function() {
				layer.close(loading);
				layer.msg('网络错误，请重试', {time: 5000});
			}
		});
	}

	// 显示入库单详情弹窗
	// 显示入库单详情弹窗（优化版，显示差异信息）
	function showStockInDetail(stockInNo) {
		$.ajax({
			url: '/api/cart/getStockInDetail.html',
			type: 'GET',
			data: {stock_in_no: stockInNo},
			dataType: 'json',
			success: function(res) {
				if (res.code === 0) {
					// 计算差异
					var difference = res.data.actual_quantity - res.data.expected_quantity;
					var diffClass = difference > 0 ? 'ns-text-success' : (difference < 0 ? 'ns-text-danger' : '');

					// 构建弹窗内容
					var html = '<div style="padding: 20px;">' +
							'<h3>入库单详情</h3>' +
							'<div class="layui-form">' +
							'   <div class="layui-form-item">' +
							'       <label class="layui-form-label">包裹编号:</label>' +
							'       <div class="layui-input-block">' +
							'           <input type="text" class="layui-input" value="' + res.data.package_no + '" readonly>' +
							'       </div>' +
							'   </div>' +
							'   <div class="layui-form-item">' +
							'       <label class="layui-form-label">计划数量:</label>' +
							'       <div class="layui-input-block">' +
							'           <input type="text" class="layui-input" value="' + res.data.expected_quantity + '" readonly>' +
							'       </div>' +
							'   </div>' +
							'   <div class="layui-form-item">' +
							'       <label class="layui-form-label">实际数量:</label>' +
							'       <div class="layui-input-block">' +
							'           <input type="text" class="layui-input" value="' + res.data.actual_quantity + '" readonly>' +
							'       </div>' +
							'   </div>' +
							'   <div class="layui-form-item">' +
							'       <label class="layui-form-label">数量差异:</label>' +
							'       <div class="layui-input-block">' +
							'           <input type="text" class="layui-input ' + diffClass + '" value="' + difference + '" readonly>' +
							'       </div>' +
							'   </div>';

					// 添加备注信息（如果有）
					if (res.data.remark) {
						html += '   <div class="layui-form-item">' +
								'       <label class="layui-form-label">差异备注:</label>' +
								'       <div class="layui-input-block">' +
								'           <textarea class="layui-textarea" readonly>' + res.data.remark + '</textarea>' +
								'       </div>' +
								'   </div>';
					}

					// 添加商品明细表格
					html += '   <div style="max-height: 400px; overflow-y: auto;">' +
							'   <table class="layui-table">' +
							'       <colgroup>' +
							'           <col width="20%">' +
							'           <col width="20%">' +
							'           <col width="20%">' +
							'           <col width="20%">' +
							'           <col width="20%">' +
							'       </colgroup>' +
							'       <thead>' +
							'       <tr>' +
							'           <th>商品名称</th>' +
							'           <th>商品编号</th>' +
							'           <th>计划数量</th>' +
							'           <th>实际入库</th>' +
							'           <th>分装规格</th>' +
							'       </tr>' +
							'       </thead>' +
							'       <tbody>';

					// 添加商品行
					res.data.items.forEach(function(item) {
						html += '<tr>' +
								'<td>' + (item.product_name || '--') + '</td>' +
								'<td>' + item.product_no + '</td>' +
								'<td>' + item.expected_quantity + '</td>' +
								'<td>' + item.actual_quantity + '</td>' +
								'<td>' + item.fenzhuang + '</td>' +
								'</tr>';
					});

					html += '</tbody></table></div></div></div>';

					// 显示弹窗
					layer.open({
						type: 1,
						title: '入库单详情',
						content: html,
						area: ['800px', '90%'], // 使用90%屏幕高度
						maxmin: true, // 允许最大化
						scrollbar: true, // 允许滚动
						yes: function(index) {
							layer.close(index);
						}
					});
				} else {
					layer.msg(res.message || '获取详情失败', {icon: 2});
				}
			},
			error: function() {
				layer.msg('网络错误，请重试', {icon: 2});
			}
		});
	}

	// 获取当前包裹ID的函数（需要根据实际业务实现）
	function getCurrentPackageId() {
		 return $('#package').val();
	}


	function clearn() {
		selectGoodsSkuId = [];
		shiji = 0;
		$("#shiji").text(shiji);
		$('.scan').text('');
		$('tr.new').remove();
	}


	// 初始化页面
	$(document).ready(function() {
		$('#scannerInput').on('input', function() {
			var scannedValue = $(this).val(); // 获取原始扫码值
			scannedValue = scannedValue.replace(/[\r\n\t]/g, '').trim(); // 移除隐藏字符
			scannedValue = String(scannedValue); // 确保是字符串格式
			if (scannedValue) {
				shiji = shiji+1;
				$("#shiji").text(shiji);
				// 查找第一个 text 为空且 name 为扫码值的 <tb> 标签
				var $targetTb = $('.scan').filter(function() {
					var $this = $(this);
					var name = $this.attr('name').replace(/[\r\n\t]/g, '').trim(); // 清理 name
					name = String(name); // 确保是字符串格式
					var text = $this.text().replace(/[\r\n\t]/g, '').trim(); // 清理 text
					return text === '' && name === scannedValue; // 匹配条件
				}).first();
				selectGoodsSkuId.push(scannedValue);
				if ($targetTb.length > 0) {
					// 如果找到匹配的 <tb>，填充内容
					$targetTb.text(scannedValue); // 填充内容

				} else {
					var html = $("#goodssale tbody").html();
					html += "<tr class='new'>";
					html += "<td></td>";
					html += "<td>" + scannedValue + "</td>";
					html += "<td></td>";
					html += "<td name='"+scannedValue+"' class='scan'>"+scannedValue+"</td>";
					html += "</tr>";
					$("#goodssale tbody").html(html);
				}

				// 清空输入框以便下一次扫码
				$('#scannerInput').val('');
			}
		});
	});
</script>

{/block}
{block name="script"}

{/block}