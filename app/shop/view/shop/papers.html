{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<style>
    .selected-products, .sales-products, .order-products {
        width: 33.33%;
        float: left;
        padding: 20px;
        box-sizing: border-box;
    }
    .order-detail {
        padding: 15px;
    }
    .order-detail table {
        width: 100%;
        margin-bottom: 15px;
    }
    .order-detail th {
        text-align: right;
        padding-right: 10px;
        width: 30%;
    }
    .order-detail td {
        text-align: left;
    }
</style>
<div class="container">
    <!-- 左侧：已选择的商品列表 -->
    <div class="selected-products">
        <h2>采购单商品</h2>
        <div class="layui-form-item">
            <div>
                <button class="layui-btn ns-bg-color" onclick="addGoods()">添加商品</button>
                <button id="order" class="layui-btn ns-bg-color" onclick="submitOrder()">发送采购单</button>
            </div>
        </div>
        <table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
            <colgroup>
                <col width="10%">
                <col width="10%">
                <col width="10%">
            </colgroup>
            <thead>
            <tr>
                <th>商品名称</th>
                <th>编号</th>
                <th>数量(瓶)</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td colspan="4">
                    <div class="goods-empty">未添加商品</div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- 中间：按销售量排序的商品列表 -->
    <div class="sales-products">
        <h2>近一个周销售量/库存比商品</h2>
        <div class="layui-form-item">
            <div>
                <input name="saleb" type="text" value="" class="layui-input" style="float: left;width: 85%">
                <button id="saleb" class="layui-btn ns-bg-color" onclick="saleb()">筛选</button>
            </div>
        </div>
        <table class="layui-table" id="goodssale" lay-skin="line" lay-size="lg">
            <colgroup>
                <col width="10%">
                <col width="10%">
                <col width="10%">
            </colgroup>
            <thead>
            <tr>
                <th>商品名称</th>
                <th>编号</th>
                <th>销售量/库存</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td colspan="4">
                    <div class="goods-empty">未添加商品</div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- 右侧：采购单记录表 -->
    <div class="order-products">
        <h2>采购单记录</h2>
        <div class="layui-form-item">
            <div>
                <input name="order_search" type="text" value="" class="layui-input" style="float: left;width: 85%;">
                <button id="order_search" class="layui-btn ns-bg-color" onclick="searchOrders()">搜索</button>
            </div>
        </div>
        <table class="layui-table" id="orders" lay-skin="line" lay-size="lg">
            <colgroup>
                <col width="10%">
                <col width="10%">
                <col width="10%">
            </colgroup>
            <thead>
            <tr>
                <th>采购单号</th>
                <th>创建时间</th>
                <th>状态</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td colspan="4">
                    <div class="goods-empty">暂无采购单</div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 采购单详情弹窗 -->
<div id="order-detail" style="display: none;">
    <div class="order-detail">
        <table>
            <tr>
                <th>采购单号：</th>
                <td id="detail-order-no"></td>
            </tr>
            <tr>
                <th>创建时间：</th>
                <td id="detail-create-time"></td>
            </tr>
            <tr>
                <th>状态：</th>
                <td id="detail-status"></td>
            </tr>
            <tr>
                <th>备注：</th>
                <td id="detail-remark"></td>
            </tr>
        </table>
        <h4>采购商品</h4>
        <table class="layui-table" lay-skin="line">
            <thead>
            <tr>
                <th>商品名称</th>
                <th>编号</th>
                <th>数量</th>
            </tr>
            </thead>
            <tbody id="detail-goods-list">
            </tbody>
        </table>
    </div>
</div>


<script>
    var selectGoodsSkuId = [];

    // 加载采购单记录
    function loadOrders(search) {
        $.ajax({
            url: '/api/cart/list.html',
            type: 'GET',
            dataType: 'json',
            data: { search: search },
            success: function(res) {
                $("#orders tbody").html('');
                if (res.data && res.data.length > 0) {
                    var html = '';
                    for (var i in res.data) {
                        var item = res.data[i];
                        html += "<tr onclick='showOrderDetail(\"" + item.order_no + "\")' style='cursor:pointer;'>";
                        html += "<td>" + item.order_no + "</td>";
                        html += "<td>" + item.create_time + "</td>";
                        html += "<td>" + getStatusText(item.status) + "</td>";
                        html += "</tr>";
                    }
                    $("#orders tbody").html(html);
                } else {
                    $("#orders tbody").html('<tr><td colspan="4"><div class="goods-empty">暂无采购单</div></td></tr>');
                }
            }
        });
    }

    function getStatusText(status) {
        var statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    function searchOrders() {
        loadOrders($("input[name='order_search']").val());
    }

    // 显示采购单详情
    function showOrderDetail(orderNo) {
        $.ajax({
            url: '/api/cart/detail.html',
            type: 'GET',
            dataType: 'json',
            data: { order_no: orderNo },
            success: function(res) {
                if (res.code === 0) {
                    var order = res.data;
                    $("#detail-order-no").text(order.order_no);
                    $("#detail-create-time").text(order.create_time);
                    $("#detail-status").text(getStatusText(order.status));
                    $("#detail-remark").text(order.remark || '无');

                    var goodsHtml = '';
                    for (var i in order.goods_list) {
                        var goods = order.goods_list[i];
                        goodsHtml += "<tr>";
                        goodsHtml += "<td>" + goods.sku_name + "</td>";
                        goodsHtml += "<td>" + goods.sku_no + "</td>";
                        goodsHtml += "<td>" + goods.quantity + "</td>";
                        goodsHtml += "</tr>";
                    }
                    $("#detail-goods-list").html(goodsHtml);

                    layer.open({
                        type: 1,
                        title: '采购单详情',
                        area: ['800px', '600px'],
                        content: $('#order-detail').html()
                    });
                } else {
                    layer.msg(res.message);
                }
            }
        });
    }

    // 提交采购单
    // 提交采购单
    function submitOrder() {
        if (selectGoodsSkuId.length === 0) {
            layer.msg('请至少选择一个商品');
            return;
        }

        var goodsList = [];
        var hasEmptySkuNo = false;

        // 检查商品编码并收集商品数据
        $("#goods tbody tr[data-sk_id]").each(function() {
            var skuId = $(this).attr('data-sk_id');
            var skuName = $(this).find("td:eq(0)").text().trim();
            var skuNo = $(this).find("td:eq(1)").text().trim();
            var quantity = $(this).find(".goods-num").val();

            // 检查商品编码是否为空
            if (!skuNo || skuNo.trim() === '') {
                hasEmptySkuNo = true;
                $(this).find("td:eq(1)").css('background-color', '#fff2f0');
                layer.msg('商品"' + skuName + '"的编码为空，请完善后再提交');
            } else {
                $(this).find("td:eq(1)").css('background-color', '');
            }

            goodsList.push({
                sku_id: skuId,
                sku_name: skuName,
                sku_no: skuNo,
                quantity: quantity
            });
        });

        // 如果有商品编码为空，则不提交
        if (hasEmptySkuNo) {
            return;
        }

        // 第一步：检查商品是否有多种规格
        checkMultiSpec(goodsList);
    }

    // 检查商品多规格
    function checkMultiSpec(goodsList) {
        $.ajax({
            url: '/api/cart/checkMultiSpec.html',
            type: 'POST',
            dataType: 'json',
            data: {
                goods_list: JSON.stringify(goodsList)
            },
            success: function(res) {
                if (res.code === 0) {
                    if (res.data.has_multi_spec) {
                        // 显示规格选择弹窗
                        showSpecSelection(res.data.multi_spec_goods, goodsList);
                    } else {
                        // 没有多规格商品，直接进入备注输入
                        promptForRemark(goodsList);
                    }
                } else {
                    layer.msg(res.message);
                }
            }
        });
    }

    // 显示规格选择弹窗
    function showSpecSelection(multiSpecGoods, goodsList) {
        var html = '<div class="spec-selection-container">';
        html += '<h4>以下商品有多种规格，请选择：</h4>';
        html += '<table class="layui-table" lay-skin="line">';
        html += '<thead><tr><th>商品名称</th><th>规格</th><th>选择</th></tr></thead>';
        html += '<tbody>';

        multiSpecGoods.forEach(function(goods) {
            html += '<tr>';
            html += '<td>' + goods.goods_name + '</td>';
            html += '<td><select class="layui-select spec-select" data-sku-id="' + goods.sku_id + '">';

            goods.specs.forEach(function(spec) {
                html += '<option value="' + spec.spec_id + '">' + spec.spec_name + '</option>';
            });

            html += '</select></td>';
            html += '<td><button class="layui-btn layui-btn-xs" onclick="confirmSpec(this)">确认</button></td>';
            html += '</tr>';
        });

        html += '</tbody></table>';
        html += '</div>';

        var index = layer.open({
            type: 1,
            title: '选择商品规格',
            area: ['800px', '500px'],
            content: html,
            btn: ['全部确认', '取消'],
            yes: function() {
                // 更新所有规格选择
                $('.spec-select').each(function() {
                    var skuId = $(this).data('sku-id');
                    var specId = $(this).val();
                    for (var i = 0; i < goodsList.length; i++) {
                        if (goodsList[i].sku_id == skuId) {
                            goodsList[i].sku_no = specId;
                            // 同时更新页面显示的商品编码
                            $('tr[data-sk_id="' + skuId + '"] td:nth-child(2)').text(specId);
                            break;
                        }
                    }
                });
                layer.close(index);
                promptForRemark(goodsList);
            },
            btn2: function() {
                layer.close(index);
            }
        });
    }

    // 提示输入备注
    function promptForRemark(goodsList) {
        layer.prompt({
            title: '请输入采购单备注（可选）',
            formType: 2
        }, function(remark, index) {
            layer.close(index);
            confirmSubmitOrder(goodsList, remark);
        });
    }

    // 确认提交采购单
    function confirmSubmitOrder(goodsList, remark) {
        layer.confirm('确认提交采购单吗？', function() {
            $.ajax({
                url: '/api/cart/create.html',
                type: 'POST',
                dataType: 'json',
                data: {
                    goods_list: JSON.stringify(goodsList),
                    remark: remark
                },
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg('采购单提交成功');
                        // 清空当前选择的商品
                        selectGoodsSkuId = [];
                        $("#goods tbody").html('<tr><td colspan="4"><div class="goods-empty">未添加商品</div></td></tr>');
                        // 刷新采购单列表
                        loadOrders();
                        // 取消所有商品选择
                        $(".choosee").prop('checked', false);
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        });
    }
    // 加载按销售量排序的商品列表
    function loadSalesProducts(saleb,goodsb) {
        $.ajax({
            url: '/api/cart/goodsLists.html',
            type: 'GET',
            dataType: 'json',
            data: {
                goodsb: goodsb,
                saleb:saleb
            },
            success: function(res) {
                $("#goodssale tbody").html('');
                var html = '';
                var earlymoning  = res.salelist;
                for (var i in earlymoning) {
                    var item = earlymoning[i];
                    html += "<tr data-sku_id='" + item.sku_id + "' data-sku_no='"+ item.sku_no +"' data-sku_name='"+item.sku_name+ "'>";
                    html += "<td><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox'  name='goods_service_ids' id='sale_"+item.sku_id+"' />" + item.sku_name + "</td>";
                    html += "<td>" + item.sku_no + "</td>";
                    html += "<td class='price-one'>" + item.sales +"/"+ item.stock +"="+ item.salestotal + "</td>";
                    html += "</tr>";
                }
                $("#goodssale tbody").html(html);

                $("#goodsware tbody").html('');
                var html = '';
                var earlymoning  = res.goodslist;
                for (var i in earlymoning) {
                    var item = earlymoning[i];
                    html += "<tr data-sku_id='" + item.sku_id + "' data-sku_no='"+ item.sku_no +"' data-sku_name='"+item.goods_name+"("+item.goods_chi+")"+"'>";
                    html += "<td><input lay-ignore onchange='editSort(this)' class='choosee' type='checkbox'  name='goods_service_ids' id='goods_"+item.sku_id+"' />" + item.goods_name+"("+item.goods_chi+")" + "</td>";
                    html += "<td>" + item.sku_no + "</td>";
                    html += "<td>" + item.stock + "</td>";
                    html += "</tr>";
                }
                $("#goodsware tbody").html(html);

                earlymoning  = res.salelist;
                for (var i in earlymoning) {
                    var item = earlymoning[i];
                    if (selectGoodsSkuId.includes(String(item.sku_id))){
                        $('#sale_'+String(item.sku_id)).prop('checked', true);
                    }
                }
                earlymoning  = res.goodslist;
                for (var i in earlymoning) {
                    var item = earlymoning[i];
                    if (selectGoodsSkuId.includes(String(item.sku_id))){
                        $('#goods_'+String(item.sku_id)).prop('checked', true);
                    }
                }
            }
        });
    }

    function saleb(){
        loadSalesProducts($("input[name='saleb']").val(),$("input[name='goodsb']").val());
    }

    function goodsb(){
        loadSalesProducts($("input[name='saleb']").val(),$("input[name='goodsb']").val());
    }


    function editSort(checkbox){
        var td = checkbox.parentElement;
        // 获取 <td> 的父元素 <tr>
        var tr = td.parentElement;
        // 获取 <tr> 的 data-sku_id 属性值
        var skuId = tr.getAttribute('data-sku_id');
        var skuNo = tr.getAttribute('data-sku_no');
        var skuName = tr.getAttribute('data-sku_name');
        if (checkbox.checked) {
            if (selectGoodsSkuId.includes(skuId)){

            }else{
                var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
                html += "<tr data-sk_id='" + skuId + "'>";
                html += "<td><input lay-ignore onchange='editS(this)' class='choosee' type='checkbox'  name='goods_service_ids' checked id='select_"+skuId+"' />" + skuName + "</td>";
                html += "<td>" + skuNo + "</td>";
                html += "<td><input type='number' min='1' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'></td>";
                html += "</tr>";
                selectGoodsSkuId.push(skuId);
                $("#goods tbody").html(html);
            }
        }else{
            if (selectGoodsSkuId.includes(skuId)){
                var index = selectGoodsSkuId.indexOf(skuId);
                selectGoodsSkuId.splice(index, 1);
                var tr = document.querySelector('tr[data-sk_id="' + skuId + '"]');
                if (tr) {
                    tr.parentNode.removeChild(tr);
                }
            }else{

            }
        }
    }
    function editS(checkbox){
        var td = checkbox.parentElement;
        // 获取 <td> 的父元素 <tr>
        var tr = td.parentElement;
        // 获取 <tr> 的 data-sku_id 属性值
        var skuId = tr.getAttribute('data-sk_id');
        if (checkbox.checked) {

        }else{
            if (selectGoodsSkuId.includes(skuId)){
                var index = selectGoodsSkuId.indexOf(skuId);
                selectGoodsSkuId.splice(index, 1);
                var tr = document.querySelector('tr[data-sk_id="' + skuId + '"]');
                if (tr) {
                    tr.parentNode.removeChild(tr);
                }
                $('#goods_'+skuId).prop('checked', false);
                $('#sale_'+skuId).prop('checked', false);
            }else{

            }
        }
    }

    function addGoods() {
        goodsSelect(function (res) {
            if (!res.length) return false;
            var html = $("#goods tbody .goods-empty").length ? '' : $("#goods tbody").html();
            for (var i = 0; i < res.length; i++) {
                for (var k = 0; k < res[i].selected_sku_list.length; k++) {
                    var item = res[i].selected_sku_list[k];
                    html += "<tr data-sk_id='" + item.sku_id + "'>";
                    html += "<td><input lay-ignore onchange='editS(this)' class='choosee' type='checkbox'  name='goods_service_ids' checked id='select_"+item.sku_id+"' />" + item.sku_name + "</td>";
                    html += "<td>" + item.sku_no + "</td>";
                    html += "<td><input type='number' min='1' value='1' autocomplete='off' class='goods-num layui-input edit-sort ns-len-short'></td>";
                    html += "</tr>";

                    selectGoodsSkuId.push(String(item.sku_id));
                    $('#goods_'+String(item.sku_id)).prop('checked', true);
                    $('#sale_'+String(item.sku_id)).prop('checked', true);
                }
            }
            console.log(selectGoodsSkuId);
            $("#goods tbody").html(html);
        }, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 0});
    }


    // 初始化页面
    $(document).ready(function() {
        loadSalesProducts('','');
        loadOrders();
    });
</script>

{/block}
{block name="script"}

{/block}