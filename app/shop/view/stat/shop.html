{extend name="base"/}
{block name="resources"}
<style>
	table
	{
		border-collapse:collapse;
	}
	table, td, th
	{
		border:1px solid #B4C6E7;
		line-height:18px;
		font-size:10px;
		padding:5px;
		color: #364A93;
	}
	.headert td{
		font-size: 18px;
	}
	/* 商品整体概况 */
	/* 头部 */
	.ns-card-common:first-child {
		margin-top: 0;
	}

	.ns-card-head-right {
		display: flex;
		align-items: center;
	}

	.ns-card-head-right .layui-form-item {
		margin-bottom: 0;
	}

	.current-time, .ns-flow-time-today {
		margin-left: 10px;
		line-height: 34px;
	}
	/* 头部 end */
	
	.ns-shop-table tr {
		border-bottom: 1px solid #E6E6E6;
	}
	
	.layui-table[lay-skin=line] td {
	    border-width: 0!important;
	}

	.ns-goods-survey-title {
		font-size: 17px;
		font-weight: 600;
		color: #000000;
	}

	.layui-table[lay-skin=line] {
		border-width: 0;
	}

	.ns-goods-intro-num {
		font-size: 18px;
		font-weight: 600;
		margin-top: 10px;
	}
</style>
{/block}
{block name="main"}
<!-- 实时概况 -->
<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">实时概况</span>

		<div class="ns-card-head-right layui-form">
			<div class="layui-form-item">
				<div class="layui-inline">
                    <label class="layui-form-label" style="width: 70px">机构名称</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="search" id="search" value="{$search}" placeholder="搜索" autocomplete="off" >
					</div>
					<label class="layui-form-label" style="width: 70px">订单时间</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="reg_start_date" id="reg_start_date" placeholder="请输入开始时间" autocomplete="off" readonly>
					</div>
					<div class="layui-input-inline ns-split">-</div>
					<div class="layui-input-inline end-time">
						<input type="text" class="layui-input" name="reg_end_date" id="reg_end_date" placeholder="请输入结束时间" autocomplete="off" readonly>
					</div>
				</div>
			</div>
            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="export">批量导出</button>
            </div>
		</div>
	</div>
	<div class="layui-card-body">
		<table class="layui-table ns-shop-table" lay-skin="line" lay-size="lg">
			<tbody>
				<tr>
					<td>
						<div class="ns-goods-intro-block">
							<p>订单数</p>
							<p class="ns-goods-intro-num" id="addGoodsCount">{$count_all}</p>
						</div>
					</td>
					<td>
						<div class="ns-goods-intro-block">
							<p>订单金额</p>
							<p class="ns-goods-intro-num" id="visitCount">{$sum_all}</p>
						</div>
					</td>
					<td>
						<div class="ns-goods-intro-block">
							<p>机构数</p>
							<p class="ns-goods-intro-num" id="collectGoods">{$bp_all}</p>
						</div>
					</td>
					<td>
						<div class="ns-goods-intro-block">
							<p>医生数</p>
							<p class="ns-goods-intro-num" id="goodsPayCount">{$doctor_all}</p>
						</div>
					</td>
				</tr>
                <tr>
                    <td>
                        <div class="ns-goods-intro-block">
                            <p>用户数</p>
                            <p class="ns-goods-intro-num" id="visiCount">{$member_count_all}</p>
                        </div>
                    </td>
                </tr>
			</tbody>
		</table>
	</div>
</div>

<!-- 趋势分析 -->
<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">详细分析</span>

	</div>
	<div class="layui-card-body">
		<table style="width:95%;border-collapse:collapse;">
			<thead>
			<tr class="headert" style="position:-webkit-sticky;position: sticky;top: 0px;background-color: #FF6A00">
				<td width="10%" align="center" ><b>产品经理</b></td>
				<td width="10%" align="center" ><b>机构</b></td>
				<td width="10%" align="center" ><b>订单数</b></td>
				<td width="15%" align="center" ><b>订单金额</b></td>
				<td width="10%" align="center" ><b>用户数</b></td>
				<td width="10%" align="center" ><b>医生数</b></td>
				<td width="10%" align="center" ><b>医生</b></td>
				<td width="10%" align="center" ><b>订单数</b></td>
				<td width="10%" align="center" ><b>订单金额</b></td>
				<td width="5%" align="center" ><b>用户数</b></td>
			</tr>
			</thead>
			{foreach $list as $k => $v}
			{foreach $v['doctor'] as $kk => $vo}
			<tr>
				{if $kk == 0}
				{php}
				$count = count($v['doctor'])+0;
				{/php}
				<td rowspan="{$count}" align="center">{$v['agent_username']}</td>
				<td rowspan="{$count}" align="center">{$v['bp']}</td>
				<td rowspan="{$count}" align="center">{$v['count']}</td>
				<td rowspan="{$count}" align="center">{$v['sum']}</td>
				<td rowspan="{$count}" align="center">{$v['member_count']}</td>
				<td rowspan="{$count}" align="center">{$v['doctor_count']}</td>
				{/if}
				<td align="center">{$vo['username']}</td>
				<td align="center">{$vo['count']}</td>
				<td align="center">{$vo['sum']}</td>
				<td align="center">{$vo['member_count']}</td>
			</tr>
			{/foreach}
			{/foreach}
		</table>
	</div>
</div>

{/block}
{block name="script"}
<script src="SHOP_JS/echarts.min.js"></script>
<script>
	var table, form, laytpl, laydate,
			repeat_flag = false,
			currentDate = new Date(),
			minDate = "";
	var reg_start_date = {$reg_start_date};
	var reg_end_date = {$reg_end_date};

	layui.use(['form', 'laydate'], function() {
		var form = layui.form;
		laydate = layui.laydate;
		currentDate.setDate(currentDate.getDate() - 7);
		form.render();
		//注册开始时间
		laydate.render({
			elem: '#reg_start_date',
			type: 'datetime',
			value: new Date(reg_start_date)
		});

		//注册结束时间
		laydate.render({
			elem: '#reg_end_date',
			type: 'datetime',
			value: new Date(reg_end_date)
		});

		form.on('submit(export)', function(data) {
			location.href = ns.url("shop/stat/exportshop",data.field);
			return false;
		});
		form.on('submit(search)', function(data) {
			location.href = ns.url("shop/stat/shop",data.field);
			return false;
		});



	});

</script>
{/block}