{extend name="base"/}
{block name="resources"}
<style>
	/* 商品整体概况 */
	/* 头部 */
	.ns-card-common:first-child {
		margin-top: 0;
	}

	.ns-card-head-right {
		display: flex;
		align-items: center;
	}

	.ns-card-head-right .layui-form-item {
		margin-bottom: 0;
	}

	.current-time, .ns-flow-time-today {
		margin-left: 10px;
		line-height: 34px;
	}

	/* 头部 end */
	.ns-goods-survey-title {
		font-size: 17px;
		font-weight: 600;
		color: #000000;
	}

	.layui-table[lay-skin=line] {
		border-width: 0;
	}

	.ns-goods-intro-num {
		font-size: 18px;
		font-weight: 600;
		margin-top: 10px;
	}
</style>
{/block}
{block name="main"}
<!-- 访问统计 -->
<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">访问概况</span>
		
		<div class="ns-card-head-right layui-form">
			<div class="layui-form-item">
				<div class="layui-input-block ns-len-mid">
					<select name="date" lay-filter="date">
						<option value="0">今日实时</option>
						<option value="1">近7天</option>
						<option value="2">近30天</option>
					</select>
				</div>
			</div>
			<div class="ns-flow-time">
				<div class="ns-flow-time-today"></div>
			</div>
		</div>
	</div>
	<div class="layui-card-body">
		<table class="layui-table" lay-skin="line" lay-size="lg">
			<tbody>
				<tr>
					<td>
						<div class="ns-goods-intro-block">
							<p>商品收藏</p>
							<p class="ns-goods-intro-num" id="collectGoods">0</p>
						</div>
					</td>
					<td>
						<div class="ns-goods-intro-block">
							<p>商品浏览</p>
							<p class="ns-goods-intro-num" id="visitCount">0</p>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
	<div class="layui-card-header">
		<span class="ns-card-title">访问趋势分析</span>
		
		<div class="ns-card-head-right layui-form">
			<div class="layui-form-item">
				<div class="layui-input-block ns-len-mid">
					<select name="date" lay-filter="date_chart">
						<option value="1">近7天</option>
						<option value="2">近30天</option>
					</select>
				</div>
			</div>
			<span class="current-time"></span>
		</div>
	</div>
	<div class="layui-card-body">
		<div id="main" style="width: 100%; height: 400px;"></div>
	</div>
</div>
{/block}
{block name="script"}
<script src="SHOP_JS/echarts.min.js"></script>
<script>
	$(function() {
		getVisitStat(0);
		getVisitStatChart(1);
	});

	layui.use(['form'], function() {
		var form = layui.form;
		form.render();

		/**
		 * 监听下拉框
		 */
		form.on('select(date)', function(data) {
			getVisitStat(data.value);
		});

		/**
		 * 图表监听下拉框
		 */
		form.on('select(date_chart)', function(data) {
			getVisitStatChart(data.value);
		});
	});

	//获取访问统计
	function getVisitStat(date_type) {
		$.ajax({
			dataType: 'JSON',
			type: 'POST',
			url: ns.url("shop/stat/visit"),
			data: {
				date_type: date_type
			},
			success: function(res) {
				if (res.code == 0) {
					$("#collectShop").html(res.data.collect_shop);
					$("#collectGoods").html(res.data.collect_goods);
					$("#visitCount").html(res.data.visit_count);
					$(".ns-flow-time-today").html(res.data.time_range);
				} else {
					layer.msg(res.message);
				}
			}
		});
	}

	//获取访问统计图表
	function getVisitStatChart(date_type) {
		$.ajax({
			dataType: 'JSON',
			type: 'POST',
			url: ns.url("shop/stat/getvisitstatlist"),
			data: {
				date_type: date_type
			},
			success: function(res) {
				option.xAxis.data = res.time;
//				option.series[0].data = res.collect_shop;
//				option.series[1].data = res.collect_goods;
//				option.series[2].data = res.visit_count;

                option.series[0].data = res.collect_goods;
                option.series[1].data = res.visit_count;
				$(".current-time").html(res.time_range);
				myChart.setOption(option);
			}
		});
	}

	// 基于准备好的dom，初始化echarts实例
	var myChart = echarts.init(document.getElementById('main'));

	// 指定图表的配置项和数据
	option = {
		tooltip: {
			trigger: 'axis'
		},
		legend: {
//			data: ['店铺收藏数', '商品收藏数', '商品浏览数']
            data: ['商品收藏数', '商品浏览数'],
			textStyle: {
				fontSize: 14,
				color: "#000"
			}
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: []
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				formatter: '{value} 个'
			}
		},
		series: [
//		    {
//				name: '店铺收藏数',
//				type: 'line',
//				data: [],
//			},
			{
				name: '商品收藏数',
				type: 'line',
				data: [],
			},
			{
				name: '商品浏览数',
				type: 'line',
				data: [],
			}
		]
	};

	// 使用刚指定的配置项和数据显示图表。
	myChart.setOption(option);
</script>
{/block}