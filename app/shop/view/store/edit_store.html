{extend name="base"/}
{block name="resources"}
<style>
	#container{ width: 650px; height: 500px; }
	.empty-address{ display: none; }
	.address-content {
		display: inline-block;
		vertical-align: top;
	}
	.ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="storeform" >
    <input type="hidden" name="store_id" value="{$store_id}"/>
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>门店名称：</label>
		<div class="layui-input-block">
			<input type="text" name="store_name" autocomplete="off" lay-verify="store_name" class="layui-input ns-len-mid" value="{$info.store_name}">
		</div>
		<div class="ns-word-aux">门店的名称（招牌）</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label img-upload-lable">门店图片：</label>
		<div class="layui-input-block img-upload">
			<div class="upload-img-block icon">
				<input type="hidden" name="store_image" value="{$info.store_image}">
				<div class="upload-img-box" id="storeUpload">
					{if empty($info.store_image)}
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
					{else/}
					<img src="{:img($info.store_image)}" alt="">
					{/if}
				</div>
			</div>
		</div>
		<div class="ns-word-aux">
			<p>门店图片在PC及移动端对应页面及列表作为门店标志出现。</p>
			<p>建议图片尺寸：100 * 100像素，图片格式：jpg、png、jpeg。</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">门店电话：</label>
		<div class="layui-input-block">
			<input type="text" name="telphone" value="{$info.telphone}" lay-verify="tel" autocomplete="off" class="layui-input ns-len-mid">
		</div>
		<div class="ns-word-aux">请输入合法的手机号</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">营业状态：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="status" value="1" lay-skin="switch" {if !empty($info) && $info.status==1 }checked{/if}>
		</div>
		<div class="ns-word-aux">是否开启营业，关闭即停业</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用自提：</label>
		<div class="layui-input-block">
			<input type="checkbox" value="1" name="is_pickup" lay-skin="switch" {if !empty($info) && $info.is_pickup==1 }checked{/if}>
		</div>
	</div>

	<!--自提点地址-->
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">* </span>门店地址：</label>
		<div class="layui-input-inline area-select">
			<select name="province_id" lay-filter="province_id" lay-verify="province_id">
				{foreach $province_list as $k => $v}
				<option value="{$v.id}" {if $info.province_id == $v.id}select{/if}>{$v.name}</option>
				{/foreach}
			</select>
		</div>
		
		<div class="layui-input-inline area-select">
			<select name="city_id"  lay-filter="city_id" lay-verify="city_id">
				<option value="">请选择城市</option>
			</select>
		</div>
		
		<div class="layui-input-inline area-select">
			<select name="district_id"  lay-filter="district_id" lay-verify="district_id">
				<option value="">请选择区/县</option>
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"></label>
		<div class="layui-input-block">
			<input type="text" name="address"  placeholder="请填写门店的具体地址" value="{$info.address}" lay-verify="required" autocomplete="off" class="layui-input ns-len-long address-content" value="">
			<input type = "hidden" name="longitude" lay-verify="required" class="layui-input" value="{$info.longitude}"/>
			<input type = "hidden" name="latitude" lay-verify="required" class="layui-input"value="{$info.latitude}"/>
			<button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>
		</div>
		<div class="ns-word-aux">点击查找地址可在地图上显示输入的地理位置</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">地图定位：</label>
		<div class="layui-input-block">
			<div id="container" class="selffetch-map"></div>
		</div>
		<span class="layui-word-aux empty-address">请选择地理位置！在地图上点击得到的地理位置会自动填入到对应的输入框中</span>
	</div>

	<div class="layui-form-item layui-hide">
		<label class="layui-form-label">是否启用外卖配送：</label>
		<div class="layui-input-block">
			<input type="checkbox" value="1" name="is_o2o" lay-skin="switch" {if !empty($info) && $info.is_o2o==1 }checked{/if}>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">营业时间：</label>
		<div class="layui-input-block">
			<input type="text" name="open_date" value="{$info.open_date}" class="layui-input ns-len-long">
		</div>
		<div class="ns-word-aux ">例：上午9:00-12:00，下午2:00-6:00</div>
	</div>

	{if $is_exit == 1}
	<div class="layui-form-item">
		<label class="layui-form-label">门店账号：</label>
		<div class="layui-input-block">
			<p class="ns-input-text">{$info.username}</p>
		</div>
	</div>
	{/if}

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="{$http_type}://webapi.amap.com/maps?v=1.4.6&amp;key=66d777244fce2401c784bdaa1da8decd"></script>
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script type="text/javascript" src="STATIC_JS/map_address.js"></script>
<script>
	var form, repeat_flag, map_class, upload;

	layui.use(['form','upload'], function() {
		form = layui.form;
		upload = layui.upload;
		repeat_flag = false;//防重复标识
		
		form.render();
		
		var initdata = {province_id : '{$info.province_id}', city_id : '{$info.city_id}', district_id : '{$info.district_id}'};
		initAddress(initdata, "storeform");

        if('{$info.latitude}' == "" || '{$info.longitude}' == ""){
            var latlng = {lat:'',lng:''};
        }else{
            var latlng = {lat:'{$info.latitude}',lng:'{$info.longitude}'};
        }

        //地图展示
        map_class = new mapClass("container",latlng);

		/**
		 * 验证码
		 */
		form.verify({
			required : function(value, item){
				var msg = $(item).attr("placeholder") != undefined ? $(item).attr("placeholder") : '';
				if(value == '') return msg;
			},
			province_id : function(value, item){
				if(value == ''){
					return '请选择省份';
				}
			},
			city_id : function(value, item){
				if(value == ''){
					return '请选择城市';
				}
			},
			district_id : function(value, item){
				if(value == ''){
					return '请选择区/县';
				}
			},
			tel : function(value, item) {
				var tel = /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/; //固定电话
				var phone = /^1[345678]\d{9}$/; //手机
				if (value != '') {
					if (!(tel.test(value)) && !(phone.test(value))) {
						return '请输入正确的电话号码或手机号！';
					}
				}
			},
			store_name : function (value,item) {
				if(value == ""){
					return '请输入门店名称';
				}
			}
		});

		/**
		 * 门店logo
		 */
		upload.render({
			elem: '#storeUpload',
			url: ns.url("shop/upload/image"),
			done: function(res) {
				if (res.code >= 0) {
					$("input[name='store_image']").val(res.data.pic_path);
					$("#storeUpload").html("<img src=" + ns.img(res.data.pic_path) + " >");
				}
				return layer.msg(res.message);
			}
		});

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data){
			if( data.field.longitude == "" || data.field.latitude == ""){
				layer.msg('请确认地理位置!');
				$(".empty-address").show();
				return;
			}else{
				$(".empty-address").hide();
			}
			
			var full_address  = [];
			full_address.push($("select[name=province_id] option:selected").text());
			full_address.push($("select[name=city_id] option:selected").text());
			full_address.push($("select[name=district_id] option:selected").text());

			data.field.full_address = full_address.toString();

			if(repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				type : "POST",
				dataType: 'JSON',
				url : ns.url("shop/store/editStore"),
				async : true,
				data : data.field,
				success : function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title: '操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function() {
								location.href = ns.url("shop/store/lists")
							},
							btn2: function() {
								location.reload();
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			})
		});

	});

	function back() {
		location.href = ns.url("shop/store/lists");
	}
	
	/**
	 * 重新渲染表单
	 */
	function refreshFrom(){
		form.render();
		orderAddressChange();//改变地址
		map_class.mapChange();
	}

	//动态改变订单地址赋值
	function orderAddressChange(){
		map_class.address.province = $("select[name=province_id]").val();
		map_class.address.province_name = $("select[name=province_id] option:selected").text();
		map_class.address.city = $("select[name=city_id]").val();
		map_class.address.city_name = $("select[name=city_id] option:selected").text();
		map_class.address.district = $("select[name=district_id]").val();
		map_class.address.district_name = $("select[name=district_id] option:selected").text();
		map_class.address.detail_address = $("input[name='address']").val();
	}
	
	/**
	 * 地址下拉框（主要用于坐标记录）
	 */
	function selectCallBack(){
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标
	}

	//地图点击回调事件
	function mapChangeCallBack(){
		$("input[name=address]").val(map_class.address.address);//详细地址
		$("input[name=longitude]").val(map_class.address.longitude);//坐标
		$("input[name=latitude]").val(map_class.address.latitude);//坐标

		$.ajax({
			type : "POST",
			dataType: 'JSON',
			url : ns.url("shop/address/getGeographicId"),
			async : true,
			data : {
				"address" : map_class.address.area
			},
			success : function(data) {
				map_class.address.province = data.province_id;
				map_class.address.city = data.city_id;
				map_class.address.district = data.district_id;
				map_class.address.township = data.subdistrict_id;
				map_class.map_change = false;
				form.val("storeform", {
					"province_id": data.province_id
				});
				$("select[name=province_id]").change();
				form.val("storeform", {
					"city_id": data.city_id
				});
				$("select[name=city_id]").change();
				form.val("storeform", {
					"district_id": data.district_id
				});
				refreshFrom();//重新渲染form
				map_class.map_change = true;
			}
		})

	}

</script>
{/block}