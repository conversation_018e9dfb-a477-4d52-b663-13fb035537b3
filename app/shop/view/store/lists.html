{extend name="base"/}
{block name="resources"}
<style>
	.ns-table-tab{margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>当前页面对门店的信息进行管理，可以添加门店，管理门店等。</li>
			{if $store_is_exit == 1}
			<li>通过以下链接前往门店管理中心<a href="{:addon_url('store://store/login/login')}" target="_blank" style="color: red"> {:addon_url('store://store/login/login')}</a></li>
			{/if}
		</ul>
	</div>
</div>
<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加门店</button>
	
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="keyword" placeholder="请输入门店名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<div class="layui-tab ns-table-tab"  lay-filter="store_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">全部门店</li>
		<li lay-id="0">已停业</li>
		<li lay-id="1">营业中</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="store_list" lay-filter="store_list"></table>
	</div>
</div>

<!-- 门店详情 -->
<script type="text/html" id="store">
	<div class="ns-table-title">
		<div class="ns-title-pic">
			{{#  if(d.store_image){  }}
			<img layer-src src={{ns.img(d.store_image)}} alt="">
			{{#  }else{  }}
			<img layer-src src="__STATIC__/img/default_shop.png" alt="">
			{{#  }  }}
		</div>
		<div class="ns-title-content">
			<p class="layui-elip">店铺名称：{{d.store_name}}</p>
			<p class="layui-elip">联系方式：{{d.telphone}}</p>
		</div>
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		{{# if(d.is_frozen) { }}
		<a class="layui-btn" lay-event="frozen">开启</a>
		{{# } else{ }}
		<a class="layui-btn" lay-event="frozen">关闭</a>
		{{# } }}
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form','element'], function() {
		var table,
			form = layui.form,
			element = layui.element,
			repeat_flag = false; //防重复标识
		form.render();
		
		//监听Tab切换，以改变地址hash值
		element.on('tab(store_tab)', function(){
			table.reload({
				page: {
					curr: 1
				},
				where:{
					'status':this.getAttribute('lay-id')
				}
			});
		});
		
		table = new Table({
			elem: '#store_list',
			url: ns.url("shop/store/lists"),
			cols: [
				[{
					title: '门店',
					unresize: 'false',
					width: '28%',
					templet: '#store'
				}, {
					field: 'full_address',
					title: '门店地址',
					unresize: 'false',
					width: '32%',
					templet: function(data) {
						return '<span title="'+data.full_address+data.address+'">'+data.full_address+data.address+'</span>'; //创建时间转换方法
					}
				},{
					title: '创建时间',
					unresize: 'false',
					width: '15%',
					templet: function(data) {
						return ns.time_to_date(data.create_time); //创建时间转换方法
					}
				}, {
					title: '门店状态',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						if (data.is_frozen == 1) {
							return '关闭';
						} else {
							return '正常';
						}
					}
				},{
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '15%'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("shop/store/editStore",{"store_id":data.store_id});
					break;
				case 'delete': //删除
					deleteCompany(data.store_id);
					break;
				case 'frozen'://冻结&解冻
					frozenStore(data.store_id, data.is_frozen);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteCompany(store_id) {
			layer.confirm('门店已开始运营，确认要删除吗?', function() {
				$.ajax({
					url: ns.url("shop/store/deleteStore"),
					data: {store_id},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}
		
		/**
		 * 删除
		 */
		function frozenStore(store_id, is_frozen) {
			var msg = '门店已开始运营，确认要关闭吗?';
			if(is_frozen == 1) {
				msg = '确定要开启该门店吗？';
			}
			layer.confirm(msg, function() {
				$.ajax({
					url: ns.url("shop/store/frozenStore"),
					data: {store_id:store_id, is_frozen:is_frozen},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
		
	});
	
	function add() {
		location.href = ns.url("shop/store/addStore");
	}
</script>
{/block}