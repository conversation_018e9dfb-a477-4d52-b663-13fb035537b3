{extend name="base"/}

{block name="resources"}
<style type="text/css">
.auth-form {padding-top: 150px;text-align: center;}
.auth-form .layui-input {display: inline-block;}
.auth-tips {font-size: 12px;color: #666;width: 544px;margin: 5px auto;}
</style>
{/block}

{block name="main"}
    {if condition="$auth_info['code'] == 1"}
    <div class="index-box">
        <div class="index-content">
            <div class="system-function">
                <table class="layui-table">
                    <colgroup>
                        <col width="20%">
                        <col width="30%">
                        <col width="20%">
                        <col width="30%">
                    </colgroup>
                    <tbody>
                    <tr>
                        <td class="ns-bg-color-light-gray">产品名称</td>
                        <td>{$auth_info['data']['app_info']['goods_name']}</td>
                        <td class="ns-bg-color-light-gray">授权状态</td>
                        <td>已授权</td>
                    </tr>
                    <tr>
                        <td class="ns-bg-color-light-gray">授权码</td>
                        <td>{$auth_info['data']['auth_code']}</td>
                        <td class="ns-bg-color-light-gray">授权类型</td>
                        <td>{$auth_info['data']['auth_type_name']}</td>
                    </tr>
                    <tr>
                        <td class="ns-bg-color-light-gray">当前版本</td>
                        <td>{$app_info.version}</td>
                        <td class="ns-bg-color-light-gray">最新版本</td>
                        <td>{$auth_info['data']['app_info']['version_info']['version_name']}</td>
                    </tr>
                    <tr>
                        <td class="ns-bg-color-light-gray">授权时间</td>
                        <td>{:date('Y-m-d H:i:s', $auth_info['data']['create_time'])}</td>
                        <td class="ns-bg-color-light-gray">服务到期时间</td>
                        <td>
                            {:date('Y-m-d H:i:s', $auth_info['data']['expire_time'])}
                            {if condition="$auth_info['data']['expire_time'] > time()"}
                            <span style="color:red;">可更新</span>
                            {else/}
                            {/if}
                        </td>
                    </tr>
                    <tr>
                        <td class="ns-bg-color-light-gray">授权公司</td>
                        <td>{$auth_info['data']['company']}</td>
                        <td class="ns-bg-color-light-gray">授权个人</td>
                        <td>
                            {$auth_info['data']['name']}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {else/}
    <div>
        <div class="auth-form">
            <input type="" name="auth_code" class="layui-input ns-len-long" placeholder="请输入您的授权码">
            <button class="layui-btn ns-bg-color" onclick="auth()">绑定授权</button>
        </div>
        <div class="auth-tips">
            请在上方输入框内输入您的授权码，如果没有授权码请到<a href="https://www.niushop.com.cn/authorization.html#Tab4" target="_blank" class="ns-text-color">Niushop官网</a>获取授权
        </div>
    </div>
    {/if}   
{/block}

{block name="script"}
<script type="text/javascript">
    var isSub = false;
    function auth(){
        var authCode = $('.auth-form [name="auth_code"]').val();
        if (!/[\S]+/.test(authCode)) {
            layer.msg('请输入您的授权码') 
            return;  
        }
        if (isSub) return;
        isSub = true;

        $.ajax({
            type: 'POST',
            url: ns.url("shop/upgrade/auth"),
            data: {
                code: authCode
            },
            dataType: 'JSON',
            success: function (res) {
                if (res.code == 1) {
                    location.reload();
                } else {
                    isSub = false;
                    layer.msg('未查找到该授权码');
                }
            }
        });        
    }
</script>
{/block}