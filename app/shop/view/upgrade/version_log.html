{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/upgrade/version_log.css" />
{/block}
{block name="main"}
<!-- 版本 1.0.1 -->
<!-- 版本 1.0.1 -->
<!-- 版本 1.0.1 -->
<!-- 版本 1.0.1 -->
<!-- 版本 1.0.1 -->
<script type="text/html" id="log_block">
    <div class="log-block">
        <div class="log-content-block">
            <div class="log-content">
                {{#  if(d.total_count === 0){ }}
                    暂无数据
                {{#  }else{ }}
                <div class="log-step-text">
                    <ul class="layui-timeline">
                        {{#  layui.each(d.data, function(index, item){ }}
                        <li class="layui-timeline-item">
                            <div class="layui-timeline-axis ns-log-version-date">{{ item.format_time }}</div>
                            <i class="layui-icon open layui-timeline-axis ns-bg-color"  style="display:{{# if(item.isclose){ }}block {{#  }else{ }}none {{#  } }}" onclick="showHidebtn($(this))" data-index="{{index}}">&#xe61a;</i>
                            <i class="layui-icon close layui-timeline-axis ns-bg-color" style="display:{{# if(item.isclose){ }}none {{#  }else{ }}block {{#  } }}" onclick="showHidebtn($(this))" data-index="{{index}}">&#xe619;</i>
                            <div class="log-body look_text" onclick="showHide($(this))" data-index="{{index}}">{{# if(item.isclose){ }}点击查看完整更新日志{{#  }else{ }}点击收起 {{#  } }}</div>
                            <div class="look" style="display:{{# if(item.isclose){ }}none{{#  }else{ }}block {{#  } }}">
                                 {{#  layui.each(item.list, function(item_index, item_item){ }}
                                <div class="layui-timeline-content layui-text log-body">
                                    <h3 class="log-step-text-title">{{ item_item.goods_name }}</h3>
                                    <div class="log-step-text-content">
                                        版本号：{{ item_item.version_name }}
                                    </div>
                                    <div class="log-step-text-content">
                                        发布时间：{{ ns.time_to_date(item_item.online_time) }}
                                    </div>
                                    <div class="log-detail">
                                        <!-- <div class="log-update ns-text-color">更新</div> -->
                                        <div>{{ item_item.description }}</div>
                                    </div>
                                </div>
                                {{#  }); }}
                            </div>
                           
                        </li>
                        {{#  }); }}
                        <li class="layui-timeline-item load-more">
                            <div class="layui-timeline-axis ns-log-version-date"></div>
                            <img src="SHOP_IMG/version_load_more.png" class="load-more-img"/>
                            <div class="layui-timeline-content layui-text log-body">
                                <h3 class="log-step-text-title" onclick="LoadingData()">
                                    {{# if(d.is_end == 1){ }}
                                        已经加载到底了
                                    {{# }else{ }}
                                        加载更多
                                    {{# } }}
                                </h3>
                            </div>
                        </li>
                    </ul>
                </div>
                {{#  } }}
            </div>
        </div>
    </div>
</script>

<div id="log_detail"></div>

<div id="page"></div>

{/block}
{block name="script"}
<script>
    var logData = {};
    var page = 0;
    var page_size = 14;
    var is_end = false;

    function LoadingData(){
        if(is_end) return false;
        page ++;
        $.ajax({
            type: "post",
            dataType: 'JSON',
            url: ns.url("shop/upgrade/versionlog"),
            async: true,
            data:{
                page:page,
                page_size:page_size
            },
            success: function(res) {
                if(res.data.page_count == page){
                    is_end = true;
                }
                renderLogData(res);//调用函数
            }
        });
    }

    function renderLogData(res){
        var new_list = res.data.data;
        if(page > 1){
            var old_list = logData.data;
            //如果上一页的最后一条和新数据的第一条是属于同一天的要进行数据合并
            if(old_list[old_list.length - 1]['time'] === new_list[0]['time']){
                var first = new_list.shift();
                
                for(var i in first['list']){
                    old_list[old_list.length - 1]['list'].push(first['list'][i]);
                }
            }
            for(var i in new_list){
                old_list.push(new_list[i]);
            }
            logData.data[logData.data.length-1].isclose = 0
        }else{
            for(var i in res.data.data){
                res.data.data[i].isclose = 0
            }
            logData = res.data;
        }
        logData.is_end = is_end;
        layui.use(['form', 'laytpl'], function() {
            var laytpl = layui.laytpl;
            laytpl($("#log_block").html()).render(logData, function(html) {
                $("#log_detail").html(html);
            });
        });
    }
    function renderLogDataStatus(res){
        logData.data[res].isclose = logData.data[res].isclose ? 0 :1
        layui.use(['form', 'laytpl'], function() {
            var laytpl = layui.laytpl;
            laytpl($("#log_block").html()).render(logData, function(html) {
                $("#log_detail").html(html);
            });
        });
    }
    function showHide(val){
        val.siblings('.look').slideToggle(function(){
          val.text( $(this).is(":visible") ? "点击收起" : "点击查看完整更新日志" );

          if($(this).is(":visible") ){
            val.siblings('.close').show()
            val.siblings('.open').hide()
            
          }else{
            val.siblings('.open').show()
            val.siblings('.close').hide()
           
          }
           renderLogDataStatus(val.data('index'))
        })
    }
    function showHidebtn(val){
        val.siblings('.look').slideToggle(function(){
            val.hide()
            if($(this).is(":visible") ){
                val.siblings('.close').show()
            }else{
                val.siblings('.open').show()
            }
            val.siblings('.look_text').text( $(this).is(":visible") ? "点击收起" : "点击查看完整更新日志" );
            renderLogDataStatus(val.data('index'))
         })
    }
    $(function(){
        LoadingData();
    })
</script>
{/block}