{extend name="base"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>编辑时，用户组不可以修改。</li>
			<li>编辑时，用户组不能为空。</li>
			<li>当用户状态为锁定时，不能登录。</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">

	<div class="layui-form-item">
		<label class="layui-form-label">用户名：</label>
		<div class="layui-input-inline">
			<input name="username" value="{$edit_user_info.username}" type="text" class="layui-input ns-len-long" autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">级别：</label>
		<div class="layui-input-inline ns-len-mid">
			<select name="group_id"  lay-verify="groupid">
				<option value="">请选择级别</option>
				{foreach $group_list as $group_list_k => $group_list_v}
				<option value="{$group_list_v.group_id}" {if $edit_user_info.type_id==$group_list_v.group_id}selected{/if}>{$group_list_v.group_name} </option>
				{/foreach}
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">上级：</label>
		<div class="layui-input-inline ns-len-mid">
			<select name="aroup_id">
				<option value="">请选择上级</option>
				{foreach $aroup_list as $group_list_k => $group_list_v}
				<option value="{$group_list_v.group_id}" {if $edit_user_info.source_uid==$group_list_v.group_id}selected{/if}>{$group_list_v.group_name} </option>
				{/foreach}
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">产品经理：</label>
		<div class="layui-input-inline ns-len-mid">
			<select name="agent_uid">
				<option value="">请选择产品经理</option>
				{foreach $user_agent as $group_list_k => $group_list_v}
				<option value="{$group_list_v.uid}" {if $edit_user_info.agent_uid==$group_list_v.uid}selected{/if}>{$group_list_v.username} </option>
				{/foreach}
			</select>
		</div>
	</div>
	{if $sys_uid==1}
	<div class="layui-form-item">
		<label class="layui-form-label">线上支付：</label>
		<div class="layui-input-inline">
			<input type="checkbox" name="pay_line" value="1" lay-skin="switch" {if condition="$edit_user_info.pay_line == 1"} checked {/if} />
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">线下支付：</label>
		<div class="layui-input-inline">
			<input type="checkbox" name="pay_close" value="1" lay-skin="switch" {if condition="$edit_user_info.pay_close == 0"} checked {/if} />
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">余额支付：</label>
		<div class="layui-input-inline">
			<input type="checkbox" name="isbalance" value="1" lay-skin="switch" {if condition="$edit_user_info.isbalance == 1"} checked {/if} />
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">线上支付成本后，剩下金额自动转入余额：</label>
		<div class="layui-input-inline">
			<input type="checkbox" name="ispaybalance" value="1" lay-skin="switch" {if condition="$edit_user_info.ispaybalance == 1"} checked {/if} />
		</div>
	</div>
	<div class="ns-word-aux">需开启余额支付</div>
	<div class="layui-form-item">
		<label class="layui-form-label">折扣：</label>
		<div class="layui-input-inline">
			<input name="balancediscount" value="{$edit_user_info.balancediscount}" type="number" mix="0.4" max="1" class="layui-input ns-len-long" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">机构Logo：</label>
		<input type="hidden" name="default_headimg" {if $edit_user_info} value="{$edit_user_info.headimg}"{/if}>
		<div class="layui-input-inline">
			<div class="upload-img-block">
				<div class="upload-img-box" id="imgUploadMember">
					{if empty($edit_user_info['headimg'])}
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
					{else/}
					<img src="{:img($edit_user_info.headimg)}" alt="">
					{/if}
				</div>
			</div>
		</div>
	</div>
	{/if}
	<div class="layui-form-item">
		<label class="layui-form-label">用户状态：</label>
		<div class="layui-input-inline">
			<input type="checkbox" name="status" value="1" lay-skin="switch" {if condition="$edit_user_info.status == 1"} checked {/if} />
		</div>
	</div>

	<div class="ns-word-aux">关闭后，用户将被锁定，无法登录</div>
	<!-- 表单操作 -->
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>

	<!-- 隐藏域 -->
	<input value="{$edit_user_info.uid}" type="hidden" class="user_id" name="uid" />
</div>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'upload'], function() {
		var form = layui.form,
				upload = layui.upload,
				repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {

			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				dataType: 'JSON',
				type: 'POST',
				url: ns.url("shop/user/editinvUser"),
				data: data.field,
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(){
								location.href = ns.url("shop/user/invuser")
							},
							btn2: function() {
								location.reload();
							}
						})
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		//普通图片上传
		var uploadInst = upload.render({
			elem: '#imgUploadMember'
			, url: ns.url("shop/upload/image")
			, done: function (res) {
				if (res.code >= 0) {
					$("#imgUploadMember").html("<img src=" + ns.img(res.data.pic_path) + " >");
					$("input[name='default_headimg']").val(res.data.pic_path);
				}
				return layer.msg(res.message);
			}
		});

		/**
		 * 表单验证
		 */
		form.verify({
			groupid: function(value) {
				if (value == 0) {
					return '请选择级别!';
				}
			}
		});
	});

	function back() {
		location.href = ns.url("shop/user/invuser");
	}
</script>
{/block}
