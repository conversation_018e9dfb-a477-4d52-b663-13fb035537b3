{extend name="base"/}
{block name="resources"}
<style>
	.ns-margin {margin-left: 5px;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>用户列表展示，admin为系统用户，不可进行编辑</li>
			<li>可以添加用户，用于登录、管理shop端后台</li>
			<li>可对用户进行编辑，修改用户状态，当用户状态为锁定时，不可进行登录</li>
			<li>添加时选择用户组，对用户的操作权限进行限制</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	{if $bp == 1}
	<button class="layui-btn ns-bg-color" onclick="add()">添加用户</button>
	{/if}
	<div class="layui-form">
		<div class="layui-inline">
			<label class="layui-form-label">级别</label>
			<div class="layui-input-inline">
				<select name="type_id">
					<option value="">请选择</option>
					<option value="1">机构</option>
					<option value="2">医生</option>
					<option value="3">健康师</option>
				</select>
			</div>
		</div>
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入用户名" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="use_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">全部</li>
		<li lay-id="1">正常</li>
		<li lay-id="0">锁定</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="user_list" lay-filter="user_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		{if $sysuser == 1}
		<a class="layui-btn" lay-event="balance_reset">充值</a>
		<a class="layui-btn" lay-event="balance_dec">整瓶扣减</a>
		<a class="layui-btn" lay-event="balance_non">返点</a>
		{/if}
		{if $bp == 1}
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
		<a class="layui-btn" lay-event="reset_pass">重置密码</a>
		<a class="layui-btn" lay-event="extend_weixin">绑定微信账号</a>
		{else/}
		<a class="layui-btn" lay-event="reset_pass">重置密码</a>
		<a class="layui-btn" lay-event="extend_weixin">绑定微信账号</a>
		{/if}
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{ d.status == 1 ? '正常' : '锁定'}}
</script>
{/block}
{block name="script"}
<script>
	var table, form, laytpl, element, layer_pass, repeat_flag = false; //防重复标识
	layui.use(['form', 'laytpl', 'element'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		element = layui.element;
		form.render();


		table = new Table({
			elem: '#user_list',
			url: ns.url("shop/user/invuser"),
			cols: [
				[{
					field: 'username',
					title: '用户名',
					width: '20%',
					unresize: 'false'
				},{
					field: 'agent_username',
					title: '产品经理',
					width: '10%',
					unresize: 'false'
				},{
					field: 'balance',
					title: '钱包余额',
					width: '8%',
					unresize: 'false'
				}, {
					field: 'group_name',
					title: '用户组',
					width: '20%',
					unresize: 'false'
				}, {
					field: 'login_time',
					title: '最后登录时间',
					width: '8%',
					unresize: 'false',
					templet: function(data) {
						return ns.time_to_date(data.login_time); //创建时间转换方法
					}
				}, {
					field: 'status',
					title: '用户状态',
					width: '12%',
					unresize: 'false',
					templet: '#status'
				}, {
					title: '操作',
					width: '17%',
					toolbar: '#operation',
					unresize: 'false'
				}]
			]
		});

		//监听Tab切换，以改变地址hash值
		element.on('tab(use_tab)', function(){
			table.reload({
				page: {curr: 1},
				where:{'status':this.getAttribute('lay-id')}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					window.open(ns.url("shop/user/editinvUser", {"uid": data.uid}));
					break;
				case 'delete': //删除
					deleteUser(data.uid);
					break;
				case 'reset_pass': //重置密码
					resetPassword(data);
					break;
				case 'balance_reset': //重置密码
					balance_reset(data);
					break;
				case 'balance_dec': //重置密码
					balance_dec(data);
					break;
				case 'balance_non': //重置密码
					balance_non(data);
					break;
				case 'extend_weixin':
					extendWeixin(data.extend_weixin);
					break;
			}
		});

		/**
		 * 重置密码
		 */
		function balance_reset(data) {
			laytpl($("#edit_stock").html()).render(data, function (html) {
				layer_pass = layer.open({
					title: '钱包充值',
					skin: 'layer-tips-class',
					type: 1,
					area: ['500px'],
					content: html
				});

			});
		}

		function balance_dec(data) {
			laytpl($("#edit_dec").html()).render(data, function (html) {
				layer_pass = layer.open({
					title: '整瓶扣减',
					skin: 'layer-tips-class',
					type: 1,
					area: ['500px'],
					content: html
				});

			});
		}

		function balance_non(data) {
			laytpl($("#edit_non").html()).render(data, function (html) {
				layer_pass = layer.open({
					title: '返点',
					skin: 'layer-tips-class',
					type: 1,
					area: ['500px'],
					content: html
				});

			});
		}


		/**
		 * 删除
		 */
		function deleteUser(uid) {
			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('确定要删除该用户吗?', function() {
				$.ajax({
					url: ns.url("shop/user/deleteinvUser"),
					data: {uid},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 绑定微信账号
		 */
		function extendWeixin(weixin){
			var html = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta content="telephone=no, address=no" name="format-detection"/>' +
					'<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/> ' +
					'<meta name="apple-mobile-web-app-capable" content="yes"/> ' +
					'<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/> ' +
					'<title>微信绑定</title> ' +
					'<link href="/public/static/css/main.css" rel="stylesheet"/></head> ' +
					'<body> ' +
					'<div class="p-header" style=" background-color: white;"> ' +
					'<div class="w"> ' +
					'<div id="logo"> ' +
					'</div> ' +
					'</div> ' +
					'</div> ' +
					'<div class="main" style="background-color:white"> ' +
					'<div class="w" style="width: auto;"> ' +
					'<div class="order"> ' +
					'<div class="o-left"> ' +
					'</div> ' +
					'<div class="o-right"> ' +
					'<div class="o-price"> ' +
					'</div> ' +
					'</div> <div class="clr"></div> </div> ' +
					'<div class="payment" style="border-top: none;"> ' +
					'<div class="pay-wechat"> ' +
					'<div class="p-w-bd" style="padding-left:120px;"> ' +
					'<div class="p-w-box"> ' +
					'<div class="pw-box-hd"> ' +
					'<img alt="模式二扫码绑定" src="http://m.metawordtech.com/includes/lib/phpqrcode/qrcode.php?data=' + weixin +
					'" style="width:298px;height:298px;"/> ' +
					'</div> ' +
					'<div class="pw-box-ft"> <p>请使用微信扫一扫</p> <p>扫描二维码绑定</p> ' +
					'</div> ' +
					'</div> ' +
					'</div></div>' +
					'</div> </div> </div> ' +
					'</body> </html>';
			var layerIndex = layer.open({
				title: '绑定微信',
				skin: 'layer-tips-class',
				type: 1,
				area: ['650px', '630px'],
				content: html,
			});
		}

		/**
		 * 重置密码
		 */
		function resetPassword(data) {
			laytpl($("#pass_change").html()).render(data, function(html) {
				layer_pass = layer.open({
					title: '重置密码',
					skin: 'layer-tips-class',
					type: 1,
					area: ['500px'],
					content: html,
				});
			});
		}

		form.on('submit(repass)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				type: "POST",
				url: ns.url("shop/user/modifyPassword"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});

		form.on('submit(balance)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				type: "POST",
				url: ns.url("shop/user/modifybalance"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});

		form.on('submit(balance_dec)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				type: "POST",
				url: ns.url("shop/user/modifybalance_dec"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});

		form.on('submit(balance_non)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				type: "POST",
				url: ns.url("shop/user/modifybalance_non"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data){
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		/**
		 * 表单验证
		 */
		form.verify({
			repass: function(value) {
				if (value != $("input[name='password']").val()) {
					return "输入错误,两次密码不一致!";
				}
			}
		});
	});

	function add() {
		location.href = ns.url("shop/user/addinvUser");
	}

	function closePass() {
		layer.close(layer_pass);
	}
</script>

<!-- 编辑库存html -->
<script type="text/html" id="edit_stock">
	<div class="layui-form" id="balance_reset">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>充值金额</label>
			<div class="layui-input-block">
				<input type="number" id="balance" name="balance" placeholder="请输入充值金额" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>备注</label>
			<div class="layui-input-block">
				<input type="text" name="remark" placeholder="请输入备注" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
			<div class="ns-word-aux mid">
				<p>请填写详细备注</p>
			</div>
		</div>

		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="balance">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="uid" value="{{d.uid}}"/>
	</div>
</script>

<script type="text/html" id="edit_dec">
	<div class="layui-form" id="balance_dec">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>扣减金额</label>
			<div class="layui-input-block">
				<input type="number" id="balance_d" name="balance_dec" placeholder="请输入扣减金额" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>备注</label>
			<div class="layui-input-block">
				<input type="text" name="remark_dec" placeholder="请输入备注" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
			<div class="ns-word-aux mid">
				<p>请填写详细备注</p>
			</div>
		</div>

		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="balance_dec">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="uid" value="{{d.uid}}"/>
	</div>
</script>

<script type="text/html" id="edit_non">
	<div class="layui-form" id="balance_non">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>返点金额</label>
			<div class="layui-input-block">
				<input type="number" id="balance_n" name="balance_non" placeholder="请输入返点金额" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>备注</label>
			<div class="layui-input-block">
				<input type="text" name="remark_non" placeholder="请输入备注" class="layui-input ns-len-mid" lay-verify="required" autocomplete="off">
			</div>
			<div class="ns-word-aux mid">
				<p>请填写详细备注</p>
			</div>
		</div>

		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="balance_non">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="uid" value="{{d.uid}}"/>
	</div>
</script>

<!-- 重置密码弹框html -->
<script type="text/html" id="pass_change">
	<div class="layui-form" id="reset_pass">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="password" placeholder="请输入密码" class="layui-input ns-len-mid" lay-verify="required"  maxlength="18" autocomplete="off">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" name="password" placeholder="请输入密码" lay-verify="repass" class="layui-input ns-len-mid" maxlength="18" autocomplete="off">
			</div>
			<div class="ns-word-aux mid">
				<p>请再一次输入密码，两次输入密码须一致</p>
			</div>
		</div>

		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="repass">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="uid" value="{{d.uid}}"/>
	</div>
</script>
{/block}