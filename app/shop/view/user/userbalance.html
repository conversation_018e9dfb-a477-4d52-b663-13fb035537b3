{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/evaluate.css" />
<style>
	.layui-table {
		margin: 15px 0;
	}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
		</ul>
	</div>
</div>


<!-- 搜索框 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title"></h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<label class="layui-form-label">搜索</label>
				<div class="layui-input-inline">
					<input type="text" name="search_keys" value="" placeholder="名称" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">时间：</label>
					<div class="layui-input-inline">
						<input type="text" name="start_time" id="start_time" placeholder="开始时间" class="layui-input" autocomplete="off" readonly>
						<i class="ns-calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline end-time">
						<input type="text" name="end_time" id="end_time" placeholder="结束时间" class="layui-input" autocomplete="off" readonly>
						<i class="ns-calendar"></i>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">类型：</label>
					<div class="layui-input-inline">
						<select name="explain_type">
							<option value="">请选择类型</option>
							<option value="1">订单扣减</option>
							<option value="2">充值</option>
							<option value="3">订单返回</option>
							<option value="4">整瓶扣减</option>
                            <option value="5">返点</option>
						</select>
					</div>
				</div>
			</div>
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="export">批量导出</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>


<table id="attr_class_list" lay-filter="attr_class_list"></table>

<div id="laypage"></div>
{/block}
{block name="script"}
<script>
	var laytpl, add_attr_index = -1,
			form, table;
	var uid = {$uid};
	layui.use(['form', 'laytpl', 'laydate'], function() {
		var repeat_flag = false; //防重复标识
		laytpl = layui.laytpl;
		laydate = layui.laydate;
		form = layui.form;
		form.render();

		//注册开始时间
		laydate.render({
			elem: '#start_time',
			type: 'datetime'
		});

		//注册结束时间
		laydate.render({
			elem: '#end_time',
			type: 'datetime'
		});

		table = new Table({
			elem: '#attr_class_list',
			url: ns.url("shop/user/userbalance", {
				"uid": uid
			}),
			cols: [
				[ {
					title: '时间',
					width: '10%',
					unresize: 'false',
                    templet: function (data) {
                        return ns.time_to_date(data.time);
                    }
				},{
					title: '类型',
					width: '10%',
					unresize: 'false',
                    templet: function (data) {
                        var str = '';
                        if (data.type == 1) {
                            str = '订单扣减';
                        } else if (data.type == 2) {
                            str = '充值';
                        } else if (data.type == 3) {
							str = '订单返回';
						} else if (data.type == 4) {
							str = '整瓶扣减';
						} else if (data.type == 5) {
                            str = '返点';
                        }
                        return str;
                    }
				}, {
					unresize: 'false',
					title: '钱包账户',
					width: '10%',
					field: 'username'
				}, {
					unresize: 'false',
					title: '使用者',
					width: '10%',
					field: 'user_uid_name'
				}, {
					unresize: 'false',
					title: '金额',
					width: '10%',
					field: 'balance'
				}, {
					unresize: 'false',
					title: '变更前',
					width: '10%',
					field: 'old_balance'
				}, {
					unresize: 'false',
					title: '变更后',
					width: '10%',
					field: 'new_balance'
				}, {
					unresize: 'false',
					title: '备注',
					width: '30%',
					field: 'remark'
				}]
			]
		});


		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});

		/**
		 *  导出
		 */
		form.on('submit(export)', function(data) {
			location.href = ns.url("shop/user/exportbalance",data.field);
			return false;
		});


	});

</script>
{/block}