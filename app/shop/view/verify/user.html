{extend name="base"/}
{block name="resources"}
<style>
	.ns-single-filter-box {margin-top: 0;}
</style>
{/block}
{block name="main"}
<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加核销人员</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="verifier_name" placeholder="请输入核销人员名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="user_list" lay-filter="user_list"></table>

<!-- 工具栏操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var table,
			form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		table = new Table({
			elem: '#user_list',
			url: ns.url("shop/verify/user"),
			cols: [
				[
					/* {
					type: 'checkbox',
					unresize: 'false',
					templet: '#verifier_id',
					width: '3%'
				}, */
				{
					field: 'verifier_name',
					title: '核销员',
					unresize: 'false',
					width: '50%'
				}, {
					field: 'create_time',
					title: '创建时间',
					unresize: 'false',
					width: '25%',
					templet: function(data) {
						return ns.time_to_date(data.create_time); //创建时间转换方法
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '25%'
				}]
			],
			bottomToolbar: ""
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("shop/verify/editUser", {"verifier_id": data.verifier_id});
					break;
				case 'delete': //删除
					deleteData(data.verifier_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteData(ids) {
			layer.confirm('真的删除核销员吗?', function() {
				$.ajax({
					url: ns.url("shop/verify/deleteUser"),
					data: {ids},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}
		
		/**
		 * 批量操作
		 */
		table.bottomToolbar(function(obj) {

			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			switch (obj.event) {
				case "del":
					var id_array = new Array();
					for (i in obj.data) id_array.push(obj.data[i].verifier_id);
					deleteData(id_array.toString());
					break;
			}
		});
		/**
		 * 批量操作
		 */
		table.bottomToolbar(function(obj) {

			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			switch (obj.event) {
				case "del":
					var id_array = new Array();
					for (i in obj.data) id_array.push(obj.data[i].company_id);
					deleteData(id_array.toString());
					break;
			}
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

	});
	
	function add() {
		location.href = ns.url("shop/verify/addUser");
	}
</script>
{/block}
