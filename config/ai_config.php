<?php
return [
    // 默认配置 OpenAI配置
    'openai' => [
        'api_url' => 'https://api.openai.com/v1/chat/completions',
        'default_model' => 'gpt-3.5-turbo',
        'timeout' => 30,
    ],
    
    // 千问配置
    'qianwen' => [
        'api_key' => 'sk-3962a82529cc4390bf1a4cb9112951ff', // API密钥
        // 'model' => 'qwen-long', // 模型名称
        'model' => 'qwen-vl-max-2025-01-25', // 模型名称
        'api_url' => 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', // API地址
        'timeout' => 600, // 请求超时时间
        'file_retention_days' => 30, // 上传文件的保留天数
        'files_api_url' => 'https://dashscope.aliyuncs.com/compatible-mode/v1/files', // 文件API基础URL
        'max_tokens' => 8192, // 最大token数量，（返回的数据长度）
        'temperature' => 0.7, // 温度参数
        'top_p' => 0.9, // top_p参数
    ],
    
    // 豆包配置
    'doubao' => [
        'api_key' => '3ed271a7-1f50-4d13-8955-7c9ea5a50087',
        'model' => 'doubao-1.5-vision-lite-250315',
        'api_url' => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        'timeout' => 30,
    ],
];