<?php

use think\facade\Env;

return [
    // 默认磁盘
    // 'default' => Env::get('filesystem.driver', 'local'),
    // 默认改为阿里云
    'default' => Env::get('filesystem.driver', 'aliyun'),
    // 磁盘列表
    'disks'   => [
        'local'  => [
            'type' => 'local',
            'root' => app()->getRuntimePath() . 'storage',
        ],
        'public' => [
            // 磁盘类型
            'type'       => 'local',
            // 磁盘路径
            'root'       => app()->getRootPath(),
            // 磁盘路径对应的外部URL路径
            'url'        => '/storage',
            // 可见性
            'visibility' => 'public',
        ],
        // oss
        'aliyun' => [
            'type'         => 'aliyun',
            'access_key_id'     => 'LTAI5tGB3KdGNdfKMMkyJAvB',
            'access_key_secret' => '******************************',
            'bucket'       => 'fenzhuang',
            'endpoint'     => 'oss-cn-shenzhen.aliyuncs.com',
            'url'          => 'http://fenzhuang.oss-cn-shenzhen.aliyuncs.com',
        ],
    ],
];
