<?php
// +----------------------------------------------------------------------
// | 店铺端菜单设置
// +----------------------------------------------------------------------
return [
	[
		'name' => 'INDEX_ROOT',
		'title' => '概况',
		'url' => 'shop/index/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_survey.png',
		'picture_selected' => '',
		'sort' => 1,
	],
	[
		'name' => 'SHOP_ROOT',
		'title' => '店铺',
		'url' => 'shop/diy/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_shop.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_shop_selected.png',
		'sort' => 2,
		'child_list' => [
			[
				'name' => 'SHOP_DIY',
				'title' => '店铺装修',
				'url' => 'shop/diy/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/diy.png',
				'picture_selected' => 'app/shop/view/public/img/icon/diy.png',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'SHOP_DIY_INDEX',
						'title' => '主页装修',
						'url' => 'shop/diy/index',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_DIY_GOODS_CATEGORY',
						'title' => '分类页面',
						'url' => 'shop/diy/goodscategory',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'SHOP_DIY_LISTS',
						'title' => '微页面',
						'url' => 'shop/diy/lists',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [
							[
								'name' => 'SHOP_DIY_EDIT',
								'title' => '编辑自定义页面',
								'url' => 'shop/diy/edit',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'SHOP_DIY_BOTTOM_NAV',
						'title' => '底部导航',
						'url' => 'shop/diy/bottomnavdesign',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],
					[
						'name' => 'SHOP_STYLE_CONFIG',
						'title' => '商城风格',
						'url' => 'shop/diy/style',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
					],
 				    [
    				    'name' => 'SHOP_STYLE_TEMPLATE',
    				    'title' => '模板选择',
    				    'url' => 'shop/diy/template',
    				    'is_show' => 1,
    				    'is_control' => 1,
    				    'is_icon' => 0,
    				    'picture' => '',
    				    'picture_selected' => '',
    				    'sort' => 6,
                        'child_list' => [
                            [
                                'name' => 'SHOP_STYLE_TEMPLATE_EDIT',
                                'title' => '模板编辑',
                                'url' => 'shop/diy/create',
                                'is_show' => 0,
                            ],
                        ],
				    ],
				
				
				]
			],
			[
				'name' => 'WEBSITE_CONFIG',
				'title' => '内容管理',
				'url' => 'shop/help/helplist',
				'parent' => '',
				'is_show' => 1,
				'is_control' => 0,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/shop_content.png',
				'picture_selected' => 'app/shop/view/public/img/icon/website_set_selected.png',
				'sort' => 8,
				'child_list' => [
					[
						'name' => 'WEBSITE_HELP_MANAGE',
						'title' => '店铺帮助',
						'url' => 'shop/help/helplist',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
						'child_list' => [
							[
								'name' => 'WEBSITE_HELP',
								'title' => '帮助列表',
								'url' => 'shop/help/helplist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'WEBSITE_HELP_ADD',
								'title' => '添加帮助',
								'url' => 'shop/help/addhelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_EDIT',
								'title' => '编辑帮助',
								'url' => 'shop/help/edithelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_DELETE',
								'title' => '删除帮助',
								'url' => 'shop/help/deletehelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS',
								'title' => '帮助分类',
								'url' => 'shop/help/classlist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_ADD',
								'title' => '添加分类',
								'url' => 'shop/help/addclass',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_EDIT',
								'title' => '编辑分类',
								'url' => 'shop/help/editclass',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_DELETE',
								'title' => '删除分类',
								'url' => 'shop/help/deleteclass',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'WEBSITE_NOTICE',
						'title' => '店铺公告',
						'url' => 'shop/notice/index',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
						'child_list' => [
							[
								'name' => 'WEBSITE_NOTICE_ADD',
								'title' => '添加公告',
								'url' => 'shop/notice/addnotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_EDIT',
								'title' => '编辑公告',
								'url' => 'shop/notice/editnotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_DELETE',
								'title' => '删除公告',
								'url' => 'shop/notice/deletenotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_TOP',
								'title' => '公告置顶',
								'url' => 'shop/notice/modifynoticetop',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_DETAIL',
								'title' => '公告详情',
								'url' => 'shop/notice/detail',
								'is_show' => 0,
							],
						],
					]
				
				]
			]
		]
	],
	[
		'name' => 'GOODS_ROOT',
		'title' => '商品',
		'url' => 'shop/goods/lists',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_commodity.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_commodity_selected.png',
		'sort' => 3,
		'child_list' => [
			[
				'name' => 'GOODS_MANAGE',
				'title' => '商品列表',
				'url' => 'shop/goods/lists',
				'is_show' => 1,
				'is_control' => 1,
				'sort' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods_list.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods_list.png',
				'child_list' => [
					[
						'name' => 'PHYSICAL_GOODS_ADD',
						'title' => '发布商品',
						'url' => 'shop/goods/addgoods',
						'sort' => 5,
						'is_show' => 0
					],
					[
						'name' => 'PHYSICAL_GOODS_EDIT',
						'title' => '编辑商品',
						'url' => 'shop/goods/editgoods',
						'sort' => 6,
						'is_show' => 0
					],
					[
						'name' => 'VIRTUAL_GOODS_ADD',
						'title' => '发布商品',
						'url' => 'shop/virtualgoods/addgoods',
						'sort' => 5,
						'is_show' => 0
					],
					[
						'name' => 'VIRTUAL_GOODS_EDIT',
						'title' => '编辑商品',
						'url' => 'shop/virtualgoods/editgoods',
						'sort' => 6,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_OFF',
						'title' => '商品下架',
						'url' => 'shop/goods/offgoods',
						'sort' => 7,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_ON',
						'title' => '商品上架',
						'url' => 'shop/goods/ongoods',
						'sort' => 8,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_DELETE',
						'title' => '商品删除',
						'url' => 'shop/goods/deletegoods',
						'sort' => 9,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'PHYSICAL_GOODS_RECYCLE',
				'title' => '回收站',
				'url' => 'shop/goods/recycle',
				'is_show' => 1,
				'is_control' => 1,
				'sort' => 2,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/recycle.png',
				'picture_selected' => 'app/shop/view/public/img/icon/recycle.png',
				'child_list' => [
					[
						'name' => 'PHYSICAL_GOODS_RECYCLE_DELETE',
						'title' => '回收站删除',
						'url' => 'shop/goods/deleterecycle',
						'sort' => 1,
						'is_show' => 0
					],
					[
						'name' => 'PHYSICAL_GOODS_RECYCLE_RECOVERY',
						'title' => '回收站恢复',
						'url' => 'shop/goods/recoveryrecycle',
						'sort' => 2,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_CATEGORY',
				'title' => '商品分类',
				'url' => 'shop/goodscategory/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 3,
				'picture' => 'app/shop/view/public/img/icon/category.png',
				'picture_selected' => 'app/shop/view/public/img/icon/category.png',
				'child_list' => [
					[
						'name' => 'GOODS_CATEGORY_ADD',
						'title' => '商品分类添加',
						'url' => 'shop/goodscategory/addcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_EDIT',
						'title' => '商品分类编辑',
						'url' => 'shop/goodscategory/editcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_DELETE',
						'title' => '商品分类删除',
						'url' => 'shop/goodscategory/deletecategory',
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_LABEL',
				'title' => '商品分组',
				'url' => 'shop/goodslabel/lists',
				'is_show' => 1,
				'sort' => 4,
				'picture' => 'app/shop/view/public/img/icon/goods_label.png',
				'picture_selected' => 'app/shop/view/public/img/icon/category.png',
				'child_list' => [
					[
						'name' => 'GOODS_LABEL_ADD',
						'title' => '添加商品分组',
						'url' => 'shop/goodslabel/add',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_LABEL_EDIT',
						'title' => '编辑商品分组',
						'url' => 'shop/goodslabel/edit',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_LABEL_DEL',
						'title' => '商品分组删除',
						'url' => 'shop/goodslabel/delete',
						'is_show' => 0
					]
				]
			],
			[
				'name' => 'GOODS_SERVICE',
				'title' => '商品服务',
				'url' => 'shop/goodsservice/lists',
				'is_show' => 1,
				'sort' => 5,
				'picture' => 'app/shop/view/public/img/icon/goods_serve.png',
				'picture_selected' => 'app/shop/view/public/img/icon/category.png',
				'child_list' => [
					[
						'name' => 'GOODS_SERVICE_ADD',
						'title' => '添加商品服务',
						'url' => 'shop/goodsservice/add',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_SERVICE_EDIT',
						'title' => '编辑商品服务',
						'url' => 'shop/goodsservice/edit',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_SERVICE_DEL',
						'title' => '商品服务删除',
						'url' => 'shop/goodsservice/delete',
						'is_show' => 0
					]
				]
			],
			[
				'name' => 'GOODS_ATTR',
				'title' => '商品属性',
				'url' => 'shop/goodsattr/lists',
				'is_show' => 1,
				'sort' => 6,
				'picture' => 'app/shop/view/public/img/icon/goods_property.png',
				'picture_selected' => 'app/shop/view/public/img/icon/category.png',
				'child_list' => [
					[
						'name' => 'GOODS_ATTR_EDIT',
						'title' => '属性模板',
						'url' => 'shop/goodsattr/editattr',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_ATTR_DEL',
						'title' => '类型删除',
						'url' => 'shop/goodsattr/deleteattr',
						'is_show' => 0
					]
				]
			],
			
			[
				'name' => 'GOODS_EVALUATE',
				'title' => '商品评价',
				'url' => 'shop/goods/evaluate',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 7,
				'picture' => 'app/shop/view/public/img/icon/goods_evaluate.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods_evaluate.png',
				'child_list' => [
					[
						'name' => 'GOODS_EVALUATE_DELETE',
						'title' => '删除',
						'url' => 'shop/goods/deleteevaluate',
						'sort' => 1,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'ALBUM_MANAGE',
				'title' => '相册管理',
				'url' => 'shop/album/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 8,
				'picture' => 'app/shop/view/public/img/icon/picture.png',
				'picture_selected' => 'app/shop/view/public/img/icon/picture.png',
				'child_list' => [
					[
						'name' => 'ALBUM_ADD',
						'title' => '添加相册分组',
						'url' => 'shop/album/addalbum',
						'sort' => 1,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_EDIT',
						'title' => '编辑相册分组',
						'url' => 'shop/album/editalbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_DELETE',
						'title' => '删除相册分组',
						'url' => 'shop/album/deletealbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_MODIFY_PICNAME',
						'title' => '编辑文件名称',
						'url' => 'shop/album/modifypicname',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_MODIFY_ALBUM',
						'title' => '修改文件分组',
						'url' => 'shop/album/modifyfilealbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_DELETE',
						'title' => '删除文件',
						'url' => 'shop/album/deletefile',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_BOX',
						'title' => '相册',
						'url' => 'shop/album/album',
						'sort' => 2,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_AFTERSALE',
				'title' => '售后保障',
				'url' => 'shop/config/aftersale',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 9,
				'picture' => 'app/shop/view/public/img/icon/after_sales_support.png',
				'picture_selected' => 'app/shop/view/public/img/icon/after_sales_support.png',
				'child_list' => []
			],
		]
	],
	[
		'name' => 'ORDER_ROOT',
		'title' => '订单',
		'url' => 'shop/order/lists',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_order.png',
		'picture_selected' => '',
		'sort' => 4,
		'child_list' => [
			[
				'name' => 'ORDER_MANAGE',
				'title' => '订单管理',
				'url' => 'shop/order/lists',
				'parent' => '',
				'is_show' => 1,
				'is_control' => 0,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/order.png',
				'picture_selected' => 'app/shop/view/public/img/icon/order.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'EXPRESS_ORDER_DETAIL',
						'title' => '订单详情',
						'url' => 'shop/order/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_CLOSE',
						'title' => '订单关闭',
						'url' => 'shop/order/close',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_DELIVER',
						'title' => '订单发货',
						'url' => 'shop/order/deliver',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_ADJUST_PRICE',
						'title' => '订单调价',
						'url' => 'shop/order/adjustprice',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_EDIT_ADDRESS',
						'title' => '订单修改收货地址',
						'url' => 'shop/order/editaddress',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'LOCAL_ORDER_DETAIL',
						'title' => '外卖订单详情',
						'url' => 'shop/localorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'LOCAL_ORDER_DELIVER',
						'title' => '外卖订单发货',
						'url' => 'shop/localorder/deliver',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_ORDER_DETAIL',
						'title' => '自提订单详情',
						'url' => 'shop/storeorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'VIRTUAL_ORDER_DETAIL',
						'title' => '虚拟订单详情',
						'url' => 'shop/virtualorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					]
				],
			],
            [
                'name' => 'ORDER_DELIVERY',
                'title' => '订单发货',
                'url' => 'shop/delivery/lists',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 0,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/deliver.png',
                'picture_selected' => 'app/shop/view/public/img/icon/deliver.png',
                'sort' => 2,
            ],
            [
                'name' => 'ORDER_REFUND_LIST',
                'title' => '退款维权',
                'url' => 'shop/orderrefund/lists',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/refund.png',
                'picture_selected' => 'app/shop/view/public/img/icon/refund.png',
                'sort' => 3,
                'child_list' => [
                    [
                        'name' => 'ORDER_REFUND_DETAIL',
                        'title' => '维权详情',
                        'url' => 'shop/orderrefund/detail',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'ORDER_REFUND_REFUSE',
                        'title' => '维权拒绝',
                        'url' => 'shop/orderrefund/refuse',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'ORDER_REFUND_AGREE',
                        'title' => '维权同意',
                        'url' => 'shop/orderrefund/agree',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'ORDER_REFUND_AGREE',
                        'title' => '维权收货',
                        'url' => 'shop/orderrefund/receive',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'ORDER_REFUND_COMPLETE',
                        'title' => '维权通过',
                        'url' => 'shop/orderrefund/complete',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                ]
            ],
			[
				'name' => 'PRODUCTION_MANAGE',
				'title' => '生产端',
				'url' => 'shop/production/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods.png',
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'PRODUCTION_INDEX',
						'title' => '生产端主页',
						'url' => 'shop/production/index',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'PRODUCTION_SEARCH',
						'title' => '订单搜索',
						'url' => 'shop/production/search',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
				],
			],
			[
				'name' => 'PHARMACY_MANAGE',
				'title' => '药房管理',
				'url' => 'shop/pharmacymanagement/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods.png',
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'PHARMACY_INDEX',
						'title' => '药房库存管理',
						'url' => 'shop/pharmacymanagement/index',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'PHARMACY_SPLIT',
						'title' => '拆零操作',
						'url' => 'shop/pharmacymanagement/splitoperation',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'PHARMACY_PRODUCTION',
						'title' => '套餐生产',
						'url' => 'shop/pharmacymanagement/packageproduction',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
					],
					[
						'name' => 'PHARMACY_STOCKIN',
						'title' => '入库操作',
						'url' => 'shop/pharmacymanagement/stockinoperation',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],
					[
						'name' => 'PHARMACY_LOG',
						'title' => '操作记录',
						'url' => 'shop/pharmacymanagement/getoperationlog',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
					],
				],
			],
			[
				'name' => 'ORDER_VERIFY',
				'title' => '订单核销',
				'url' => 'shop/verify/verifycard',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/verify.png',
				'picture_selected' => 'app/shop/view/public/img/icon/verify.png',
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'ORDER_VERIFY_CARD',
						'title' => '核销台',
						'url' => 'shop/verify/verifycard',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'ORDER_VERIFY_RECORDS',
						'title' => '核销记录',
						'url' => 'shop/verify/records',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'ORDER_VERIFY_USER',
						'title' => '核销人员',
						'url' => 'shop/verify/user',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
						'child_list' => [
							[
								'name' => 'ORDER_VERIFY_USER_ADD',
								'title' => '添加核销人员',
								'url' => 'shop/verify/adduser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'ORDER_VERIFY_USER_DELETE',
								'title' => '删除核销人员',
								'url' => 'shop/verify/deleteuser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							], [
								'name' => 'ORDER_VERIFY_USER_EDIT',
								'title' => '编辑核销人员',
								'url' => 'shop/verify/edituser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],
				]
			],
            [
                'name' => 'INVOICE_LIST',
                'title' => '发票列表',
                'url' => 'shop/order/invoiceorderlist',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/invoice.png',
                'picture_selected' => 'app/shop/view/public/img/icon/invoice.png',
                'sort' => 5,
                'child_list' => [

                ]
            ],

		]
	],
	[
		'name' => 'MEMBER_ROOT',
		'title' => '会员',
		'url' => 'shop/member/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_member.png',
		'picture_selected' => '',
		'sort' => 5,
		'child_list' => [
			[
				'name' => 'SHOP_MEMBER_INDEX',
				'title' => '会员概况',
				'url' => 'shop/member/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'sort' => 1,
			],
			[
				'name' => 'MEMBER_INDEX',
				'title' => '会员列表',
				'url' => 'shop/member/memberlist',
				'is_show' => 1,
				'picture' => 'app/shop/view/public/img/icon/member_list.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'MEMBER_LIST',
						'title' => '会员列表',
						'url' => 'shop/member/memberlist',
						'is_show' => 0,
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'MEMBER_ADD',
								'title' => '会员添加',
								'url' => 'shop/member/addmember',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_EDIT',
								'title' => '基础信息',
								'url' => 'shop/member/editmember',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_PATIENT_DETAILS',
								'title' => '历程',
								'url' => 'shop/member/journey',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_DELETE',
								'title' => '会员删除',
								'url' => 'shop/member/deletemember',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_ACCOUNT_DETAIL',
								'title' => '检查报告',
								'url' => 'shop/member/accountdetail',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_ORDER',
								'title' => '订单管理',
								'url' => 'shop/member/order',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_ADDRESS',
								'title' => '病历管理',
								'url' => 'shop/member/addressdetail',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_DETAIL',
								'title' => '会员详情',
								'url' => 'shop/member/memberdetail',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_LABEL_MODIFY',
								'title' => '修改会员标签',
								'url' => 'shop/member/modifylabel',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_STATUS_MODIFY',
								'title' => '修改会员状态',
								'url' => 'shop/member/midifystatus',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_PASSWORD_MODIFY',
								'title' => '修改会员密码',
								'url' => 'shop/member/midifypassword',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_BALANCE_ADJUST',
								'title' => '余额调整',
								'url' => 'shop/member/adjustbalance',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_POINT_ADJUST',
								'title' => '积分调整',
								'url' => 'shop/member/adjustpoint',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_GROWTH_ADJUST',
								'title' => '成长值调整',
								'url' => 'shop/member/adjustgrowth',
								'is_show' => 0,
							],
						]
					],
				],
			],
			
			[
				'name' => 'MEMBER_LEVEL',
				'title' => '会员等级',
				'url' => 'shop/memberlevel/levellist',
				'is_show' => 1,
				'sort' => 3,
				'picture' => 'app/shop/view/public/img/icon/member_class.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'child_list' => [
					[
						'name' => 'MEMBER_LEVEL_ADD',
						'title' => '等级添加',
						'url' => 'shop/memberlevel/addlevel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LEVEL_EDIT',
						'title' => '等级修改',
						'url' => 'shop/memberlevel/editlevel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LEVEL_DELETE',
						'title' => '等级删除',
						'url' => 'shop/memberlevel/deletelevel',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'MEMBER_LABEL',
				'title' => '会员标签',
				'url' => 'shop/memberlabel/labellist',
				'is_show' => 1,
				'sort' => 4,
				'picture' => 'app/shop/view/public/img/icon/member_label.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'child_list' => [
					[
						'name' => 'MEMBER_LABEL_ADD',
						'title' => '标签添加',
						'url' => 'shop/memberlabel/addlabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_EDIT',
						'title' => '标签修改',
						'url' => 'shop/memberlabel/editlabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_DELETE',
						'title' => '标签删除',
						'url' => 'shop/memberlabel/deletelabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_SORT_MODIFY',
						'title' => '修改排序',
						'url' => 'shop/memberlabel/modifysort',
						'is_show' => 0,
					],
				]
			],
		
		]
	],
	[
		'name' => 'PROMOTION_ROOT',
		'title' => '营销',
		'url' => 'shop/promotion/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_marketing.png',
		'picture_selected' => '',
		'sort' => 6,
		'child_list' => [
			[
				'name' => 'PROMOTION_CENTER',
				'title' => '营销中心',
				'url' => 'shop/promotion/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/promotion.png',
				'picture_selected' => 'app/shop/view/public/img/icon/promotion.png',
				'sort' => 1,
			],
			[
				'name' => 'PROMOTION_MEMBER',
				'title' => '会员互动',
				'url' => 'shop/promotion/member',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member_chat.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member_chat.png',
				'sort' => 1,
			],
		]
	],
	/*[
		'name' => 'TOOL_ROOT',
		'title' => '应用',
		'url' => 'shop/promotion/tool',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_marketing.png',
		'picture_selected' => '',
		'sort' => 6,
		'child_list' => [
			[
				'name' => 'PROMOTION_TOOL',
				'title' => '应用管理',
				'url' => 'shop/promotion/tool',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/promotion_tool.png',
				'picture_selected' => 'app/shop/view/public/img/icon/promotion_tool.png',
				'sort' => 1,
			],
		]
	],*/
	[
		'name' => 'STORE_ROOT',
		'title' => '门店',
		'url' => 'shop/store/lists',
		'is_show' => 1,
		'is_control' => 1,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/icon/store.png',
		'picture_selected' => 'app/shop/view/public/img/icon/store.png',
		'sort' => 7,
		'child_list' => [
			[
				'name' => 'STORE_LIST',
				'title' => '门店列表',
				'url' => 'shop/store/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 1,
				'picture' => 'app/shop/view/public/img/icon/store.png',
				'picture_selected' => 'app/shop/view/public/img/icon/store.png',
				'child_list' => [
					[
						'name' => 'STORE_ADD',
						'title' => '添加门店',
						'url' => 'shop/store/addstore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_EDIT',
						'title' => '修改门店',
						'url' => 'shop/store/editstore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_DELETE',
						'title' => '删除门店',
						'url' => 'shop/store/deletestore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],
		
		]
	],
	[
		'name' => 'ACCOUNT_ROOT',
		'title' => '财务',
		'url' => 'shop/account/dashboard',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 1,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_account.png',
		'picture_selected' => '',
		'sort' => 8,
		'child_list' => [
			[
				'name' => 'ACCOUNT_DASHBOARD',
				'title' => '财务概况',
				'url' => 'shop/account/dashboard',
				'is_show' => 1,
				'picture' => 'app/shop/view/public/img/menu_icon/money.png',
				'picture_selected' => 'app/shop/view/public/img/menu_icon/money.png',
				'sort' => 1,
				'child_list' => [
				]
			],
			[
				'name' => 'MEMBER_WITHDRAW_LIST',
				'title' => '会员提现',
				'url' => 'shop/memberwithdraw/lists',
				'is_show' => 1,
				'picture' => 'app/shop/view/public/img/icon/member_withdraw.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'child_list' => [
					[
						'name' => 'MEMBER_WITHDRAW_DETAIL',
						'title' => '提现详情',
						'url' => 'shop/memberwithdraw/detail',
						'is_show' => 0,
					],
				]
			],
		
		]
	],
	[
		'name' => 'STAT_ROOT',
		'title' => '统计',
		'url' => 'shop/stat/shop',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_data.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_data_selected.png',
		'sort' => 9,
		'child_list' => [
			[
				'name' => 'STAT_SHOP',
				'title' => '店铺统计',
				'url' => 'shop/stat/shop',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat.png',
				'sort' => 2,
			],
			[
				'name' => 'STAT_GOODS',
				'title' => '商品统计',
				'url' => 'shop/stat/goods',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_goods.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_goods.png',
				'sort' => 3,
			],
			[
				'name' => 'STAT_ORDER',
				'title' => '交易统计',
				'url' => 'shop/stat/order',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_order.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_order.png',
				'sort' => 4,
			],
			[
				'name' => 'STAT_VISIT',
				'title' => '访问统计',
				'url' => 'shop/stat/visit',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_icon.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_icon.png',
				'sort' => 5,
			],
		]
	],
	[
		'name' => 'CONFIG_ROOT',
		'title' => '设置',
		'url' => 'shop/shop/config',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_set.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_set_selected.png',
		'sort' => 10,
		'child_list' => [
			[
				'name' => 'CONFIG_BASE',
				'title' => '基础设置',
				'url' => 'shop/shop/config',
				'is_show' => 1,
				'picture' => 'app/shop/view/public/img/icon/shop_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'SHOP_CONFIG',
						'title' => '网站设置',
						'url' => 'shop/shop/config',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_CONTACT',
						'title' => '联系地址',
						'url' => 'shop/shop/contact',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'COPYRIGHT',
						'title' => '版权信息',
						'url' => 'shop/config/copyright',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
                    [
                        'name' => 'CONFIG_UPLOAD',
                        'title' => '上传设置',
                        'url' => 'shop/upload/config',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'CONFIG_UPLOAD_SET',
                                'title' => '上传设置',
                                'url' => 'shop/upload/config',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'UPLOAD_OSS',
                                'title' => '云上传',
                                'url' => 'shop/upload/oss',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                            ],
                        ],
                    ],
                    [
                        'name' => 'CONFIG_API',
                        'title' => 'api安全',
                        'url' => 'shop/config/api',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 4,
                    ],
                    [
                        'name' => 'CONFIG_CAPTCHA',
                        'title' => '验证码设置',
                        'url' => 'shop/config/captcha',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 5
                    ],
				]
			],
			[
				'name' => 'CONFIG_BASE_MEMBER',
				'title' => '会员设置',
				'url' => 'shop/member/regconfig',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'LOGIN_REG_CONFIG',
						'title' => '注册设置',
						'url' => 'shop/member/regconfig',
						'is_show' => 1,
						'sort' => 2,
					],
					[
						'name' => 'LOGIN_REG_AGREEMENT',
						'title' => '注册协议',
						'url' => 'shop/member/regagreement',
						'is_show' => 1,
						'picture' => 'app/shop/view/public/img/icon/member.png',
						'picture_selected' => 'app/shop/view/public/img/icon/member.png',
						'sort' => 5,
					],
					[
						'name' => 'MEMBER_WITHDRAW_CONFIG',
						'title' => '会员提现',
						'url' => 'shop/memberwithdraw/config',
						'is_show' => 1,
						'sort' => 10,
					],
				]
			],
			[
				'name' => 'CONFIG_BASE_ORDER',
				'title' => '交易设置',
				'url' => 'shop/order/config',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/deal_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'ORDER_CONFIG_SETTING',
						'title' => '交易设置',
						'url' => 'shop/order/config',
						'is_show' => 1,
						'sort' => 1,
					],
					[
						'name' => 'CONFIG_PAY',
						'title' => '支付设置',
						'url' => 'shop/config/pay',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],
				],
			],
			[
				'name' => 'CONFIG_EXPRESS_ROOT',
				'title' => '配送设置',
				'url' => 'shop/delivery/express',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/distribution_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'EXPRESS_CONFIG',
						'title' => '配送管理',
						'url' => 'shop/delivery/express',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'EXPRESS_STORE_CONFIG',
								'title' => '自提设置',
								'url' => 'shop/delivery/storeconfig',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_STORE_STATUS',
								'title' => '自提开关',
								'url' => 'shop/delivery/modifystorestatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_EXPRESS_STATUS',
								'title' => '物流开关',
								'url' => 'shop/delivery/modifyexpressstatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_LOCAL_STATUS',
								'title' => '外卖配送开关',
								'url' => 'shop/delivery/modifylocalstatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_LOCALDELIVERY_CONFIG',
								'title' => '外卖配送',
								'url' => 'shop/local/local',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_EDIT_PRINT_TEMPLATE',
								'title' => '运单模板',
								'url' => 'shop/express/editprinttemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE',
								'title' => '运费模板',
								'url' => 'shop/express/template',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_ADD',
								'title' => '添加运费模板',
								'url' => 'shop/express/addtemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_EDIT',
								'title' => '编辑运费模板',
								'url' => 'shop/express/edittemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_DELETE',
								'title' => '删除运费模板',
								'url' => 'shop/express/deletetemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],
					[
						'name' => 'EXPRESS_COMPANY_ROOT',
						'title' => '物流公司',
						'url' => 'shop/express/expresscompany',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
						'child_list' => [
							[
								'name' => 'EXPRESS_COMPANY',
								'title' => '物流公司',
								'url' => 'shop/express/expresscompany',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'DELIVERY_EXPRESS_ADD',
								'title' => '添加物流公司',
								'url' => 'shop/express/addcompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'child_list' => [],
							],
							[
								'name' => 'DELIVERY_EXPRESS_EDIT',
								'title' => '编辑物流公司',
								'url' => 'shop/express/editcompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'child_list' => [],
							],
							[
								'name' => 'DELIVERY_EXPRESS_DELETE',
								'title' => '删除物流公司',
								'url' => 'shop/express/deletecompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'child_list' => [],
							],
							[
								'name' => 'EXPRESS_EXPRESS_CONFIG',
								'title' => '物流跟踪',
								'url' => 'shop/express/trace',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
						]
					],
				
				]
			],
			[
				'name' => 'SYSTEM_CONFIG',
				'title' => '系统设置',
				'url' => 'shop/user/user',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/sys_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'USER_AUTH',
						'title' => '权限管理',
						'url' => 'shop/user/user',
						'parent' => '',
						'is_show' => 1,
						'is_control' => 0,
						'is_icon' => 0,
						'picture' => 'app/shop/view/public/img/icon/account.png',
						'picture_selected' => 'app/shop/view/public/img/icon/account.png',
						'sort' => 7,
						'child_list' => [
							[
								'name' => 'USER_LIST',
								'title' => '用户管理',
								'url' => 'shop/user/user',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [
									[
										'name' => 'USER_ADD',
										'title' => '用户添加',
										'url' => 'shop/user/adduser',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_EDIT',
										'title' => '用户编辑',
										'url' => 'shop/user/edituser',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_DELETE',
										'title' => '用户删除',
										'url' => 'shop/user/deleteuser',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_MODIFY_STATUS',
										'title' => '调整用户状态',
										'url' => 'shop/user/modifyuserstatus',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
								]
							],
							[
								'name' => 'USER_GROUP',
								'title' => '用户组',
								'url' => 'shop/user/group',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [
									[
										'name' => 'USER_GROUP_ADD',
										'title' => '用户组添加',
										'url' => 'shop/user/addgroup',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_GROUP_EDIT',
										'title' => '用户组编辑',
										'url' => 'shop/user/editgroup',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_GROUP_DELETE',
										'title' => '用户组删除',
										'url' => 'shop/user/deletegroup',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
									[
										'name' => 'USER_GROUP_MODIFY_STATUS',
										'title' => '调整用户状态',
										'url' => 'shop/user/modifygroupstatus',
										'is_show' => 0,
										'is_control' => 1,
										'is_icon' => 0,
										'picture' => '',
										'picture_selected' => '',
										'sort' => 1,
									],
								]
							],
							[
								'name' => 'USER_LOG',
								'title' => '用户日志',
								'url' => 'shop/user/userlog',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],
					[
						'name' => 'CONFIG_DEFAULT_PICTURE',
						'title' => '默认图设置',
						'url' => 'shop/config/defaultPicture',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 6,
					],
					[
						'name' => 'MESSAGE_LISTS',
						'title' => '消息管理',
						'url' => 'shop/message/lists',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 7,
						'child_list' => [
							[
								'name' => 'MESSAGE_EMAIL_EDIT',
								'title' => '编辑邮件模板',
								'url' => 'shop/message/editEmailMessage',
								'parent' => '',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [
								],
							],
						],
					],
					[
						'name' => 'SMS_MANAGE',
						'title' => '短信管理',
						'url' => 'shop/message/sms',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 8,
						'child_list' => [
                            [
                                'name' => 'SMS_LIST',
                                'title' => '短信配置',
                                'url' => 'shop/message/sms',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                ],
                            ],
							[
								'name' => 'SMS_RECORDS',
								'title' => '发送记录',
								'url' => 'shop/message/smsrecords',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [
								],
							],
						],
					],
				
				]
			],
            [
                'name' => 'CONFIG_SYSTEM_TOOL',
                'title' => '常用工具',
                'url' => 'shop/system/database',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/sys_config.png',
                'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'CONFIG_SYSTEM_DATABASE',
                        'title' => '数据库管理',
                        'url' => 'shop/system/database',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'CONFIG_SYSTEM_DATABASE_LIST',
                                'title' => '数据备份',
                                'url' => 'shop/system/database',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_IMPORTLIST',
                                'title' => '数据还原',
                                'url' => 'shop/system/importlist',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_BACKUP',
                                'title' => '数据备份',
                                'url' => 'shop/system/backup',
                                'parent' => '',
                                'is_show' => 0,
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_DELETEBACKUP',
                                'title' => '删除备份文件',
                                'url' => 'shop/system/deletebackup',
                                'parent' => '',
                                'is_show' => 0,
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_TABLEREPAIR',
                                'title' => '数据表修复',
                                'url' => 'shop/system/tablerepair',
                                'parent' => '',
                                'is_show' => 0,
                            ],
                        ],
                    ],
                    [
                        'name' => 'CONFIG_SYSTEM_CACHE',
                        'title' => '缓存管理',
                        'url' => 'shop/system/cache',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                        'child_list' => [
                        ],
                    ],
                    [
                        'name' => 'SYSTEM_H5_REFRESH',
                        'title' => 'H5端刷新',
                        'url' => 'shop/system/refreshh5',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                        'child_list' => [
                        ],
                    ],
                ]
            ]
		]
	],
   /* [
        'name' => 'UPGRADE_ROOT',
        'title' => '授权',
        'url' => 'shop/upgrade/auth',
        'is_show' => 1,
        'picture' => 'app/shop/view/public/img/menu_icon/system_setup.png',
        'picture_selected' => '',
        'sort' => 11,
        'child_list' => [
            [
                'name' => 'AUTH_INFO',
                'title' => '系统授权',
                'url' => 'shop/upgrade/auth',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon/system_authorization.png',
                'picture_selected' => '',
                'sort' => 1,
            ],
            [
                'name' => 'SYSTEM_ADDON_ROOT',
                'title' => '插件管理',
                'url' => 'shop/system/addon',
                'parent' => '',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon/plug_management.png',
                'picture_selected' => '',
                'sort' => 2,
                'child_list' => [],
            ],
            [
                'name' => 'UPGRADE_INFO',
                'title' => '系统升级',
                'url' => 'shop/upgrade/upgrade',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon/system_upgrade.png',
                'picture_selected' => '',
                'sort' => 3,
                'child_list' => [
                    [
                        'name' => 'UPGRADE_ACTION',
                        'title' => '升级操作 ',
                        'url' => 'shop/upgrade/upgradeAction',
                        'parent' => '',
                        'is_show' => 0,
                    ],
                ],
            ],
            [
                'name' => 'VERSION_LOG',
                'title' => '更新日志',
                'url' => 'shop/upgrade/versionLog',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon/update_log.png',
                'picture_selected' => '',
                'sort' => 5,
            ],
        ],
    ],*/

];
