(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-topics-payment-payment"],{"157e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};t.default=i},2126:function(e,t,a){"use strict";var i,o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":e.checked,"ns-border-color":e.checked},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"ns-bg-color":e.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},r=[];a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}))},"26b3":function(e,t,a){var i=a("a715");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("4f06").default;o("6190f968",i,!0,{sourceMap:!1,shadowMode:!1})},"2d82":function(e,t,a){"use strict";var i=a("26b3"),o=a.n(i);o.a},"30ef":function(e,t,a){"use strict";a.r(t);var i=a("157e"),o=a.n(i);for(var r in i)"default"!==r&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},"55ee":function(e,t,a){"use strict";var i={nsSwitch:a("d806").default,uniPopup:a("d380").default,mypOne:a("82bc").default,loadingCover:a("cd2f").default,nsShowToast:a("f505").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"order-container",class:{"safe-area":e.isIphoneX},attrs:{"data-theme":e.themeStyle}},[a("v-uni-view",{staticClass:"head-nav ns-bg-color"}),a("v-uni-view",{staticClass:"head-return ns-bg-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navigateBack()}}},[a("v-uni-text",{staticClass:"iconfont iconback_light"}),e._v(e._s(e.$lang("title")))],1),a("v-uni-view",{staticClass:"payment-top ns-bg-color"}),a("v-uni-view",{staticClass:"store-wrap"},[0==e.orderPaymentData.is_virtual&&e.orderPaymentData.shop_goods_list.express_type.length>1?a("v-uni-view",{staticClass:"tabs"},e._l(e.orderPaymentData.shop_goods_list.express_type,(function(t,i){return a("v-uni-view",{key:i,staticClass:"ns-bg-color",class:{"active ns-text-color":t.name==e.orderPaymentData.delivery.delivery_type},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectDeliveryType(t)}}},[a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.title))])],1)})),1):e._e(),0==e.orderPaymentData.is_virtual&&"store"!=e.orderPaymentData.delivery.delivery_type?a("v-uni-view",{staticClass:"delivery-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",[e.orderPaymentData.member_address?a("v-uni-view",{staticClass:"store-info"},[a("v-uni-view",{staticClass:"icon ns-bg-color"},[a("v-uni-view",{staticClass:"iconfont icondingwei1 ns-font-size-lg"})],1),a("v-uni-view",{staticClass:"store-info-detail"},[a("v-uni-view",[a("v-uni-text",{staticClass:"ns-margin-right"},[e._v("收货人："+e._s(e.orderPaymentData.member_address.name))]),a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.mobile))])],1),a("v-uni-view",{staticClass:"store-detail"},[a("v-uni-view",{staticClass:"ns-text-color-gray"},[e._v("收货地址："+e._s(e.orderPaymentData.member_address.full_address)+" "+e._s(e.orderPaymentData.member_address.address))])],1)],1),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont iconright"})],1)],1):a("v-uni-view",{staticClass:"store-info"},[a("v-uni-view",{staticClass:"icon ns-bg-color"},[a("v-uni-view",{staticClass:"iconfont icondingwei1 ns-font-size-lg"})],1),a("v-uni-view",{staticClass:"store-info-detail"},[a("v-uni-view",{staticClass:"ns-text-color"},[e._v("前去设置")]),a("v-uni-view",[e._v("您还没有设置收货地址")])],1),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont iconright"})],1)],1)],1),"local"==e.orderPaymentData.delivery.delivery_type&&e.timeInfo.showTimeBar&&e.orderPaymentData.member_address?a("v-uni-picker",{attrs:{mode:"time"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)},change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindTimeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"pick-block"},[a("v-uni-view",[e._v("送达时间")]),a("v-uni-view",{staticClass:"time-picker"},[a("v-uni-view",[e._v(e._s(e.orderCreateData.buyer_ask_delivery_time))]),a("v-uni-text",{staticClass:"iconfont iconright ns-text-color-gray"})],1)],1)],1):e._e()],1):e._e(),0==e.orderPaymentData.is_virtual&&"store"==e.orderPaymentData.delivery.delivery_type?a("v-uni-view",{staticClass:"delivery-box"},[a("v-uni-view",[e.storeInfo.currStore?a("v-uni-view",{staticClass:"store-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSiteDelivery.apply(void 0,arguments)}}},[e.storeInfo.currStore.store_image?a("v-uni-view",{staticClass:"icon image-icon"},[a("v-uni-image",{attrs:{src:e.$util.img(e.storeInfo.currStore.store_image),mode:"aspectFill"}})],1):a("v-uni-view",{staticClass:"icon ns-bg-color"},[a("v-uni-view",{staticClass:"iconfont iconmendian"})],1),a("v-uni-view",{staticClass:"store-info-detail"},[a("v-uni-view",[a("v-uni-text",[e._v(e._s(e.storeInfo.currStore.store_name))]),a("v-uni-text")],1),a("v-uni-view",{staticClass:"store-detail"},[e.storeInfo.currStore.open_date?a("v-uni-view",{staticClass:"ns-text-color-gray"},[e._v("营业时间："+e._s(e.storeInfo.currStore.open_date))]):e._e(),a("v-uni-view",{staticClass:"ns-text-color-gray"},[e._v("地址："+e._s(e.storeInfo.currStore.full_address)+" "+e._s(e.storeInfo.currStore.address))])],1)],1),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont iconright"})],1)],1):a("v-uni-view",[a("v-uni-view",{staticClass:"address-empty"},[a("v-uni-view",{staticClass:"ns-text-color"},[e._v("当前无自提门店！请选择其它配送方式")])],1)],1)],1),e.storeInfo.currStore?a("v-uni-view",{staticClass:"pick-block",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",[e._v("预留手机")]),a("v-uni-input",{staticClass:"text-right",attrs:{type:"number",placeholder:"请输入手机号"},model:{value:e.member_address.mobile,callback:function(t){e.$set(e.member_address,"mobile",t)},expression:"member_address.mobile"}})],1):e._e()],1):e._e()],1),1==e.orderPaymentData.is_virtual?a("v-uni-view",{staticClass:"mobile-wrap"},[a("v-uni-view",{staticClass:"icon ns-bg-color"},[a("v-uni-view",{staticClass:"iconfont iconshouji1 ns-font-size-xx-lg"})],1),a("v-uni-view",{staticClass:"mobile-info"},[a("v-uni-view",{staticClass:"tips ns-text-color ns-font-size-sm"},[e._v("购买虚拟类商品需填写您的手机号，以方便商家与您联系")]),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"text"},[e._v("手机号码")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",value:"",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"ns-text-color-gray placeholder"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-body"},e._l(e.orderPaymentData.shop_goods_list.goods_list,(function(t,i){return a("v-uni-view",{key:i,staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.navigateTo(t.sku_id)}}},[a("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(i)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.navigateTo(t.sku_id)}}},[e._v(e._s(t.sku_name))]),t.sku_spec_format?a("v-uni-view",{staticClass:"sku "},[a("v-uni-view",{staticClass:"goods-spec"},[e._l(t.sku_spec_format,(function(a,i){return[e._v(e._s(a.spec_value_name)+" "+e._s(i<t.sku_spec_format.length-1?"；":""))]}))],2)],1):e._e(),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",{staticClass:"goods-price ns-text-color"},[a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),e._v(e._s(t.price))],1)],1),a("v-uni-view",[a("v-uni-text",[a("v-uni-text",{staticClass:"iconfont iconclose"}),e._v(e._s(t.num))],1)],1)],1)],1)],1)})),1),a("v-uni-view",{staticClass:"site-footer"},[e.orderPaymentData.shop_goods_list.coupon_list.length?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSiteCoupon.apply(void 0,arguments)}}},[e.orderPaymentData.coupon.coupon_id?a("v-uni-text",{staticClass:"ns-text-color-gray"},[a("v-uni-text",{staticClass:"inline"},[e._v("已选择优惠券，已优惠")]),a("v-uni-text",{staticClass:"ns-text-color inline"},[a("v-uni-text",{staticClass:"ns-font-size-sm inline"},[e._v(e._s(e.$lang("common.currencySymbol")))]),e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.coupon_money)))],1)],1):a("v-uni-text",{staticClass:"ns-text-color-gray"},[e._v("不使用优惠券")])],1),a("v-uni-text",{staticClass:"iconfont iconright"})],1):e._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("活动优惠")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSitePromotion.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"ns-text-color-gray"},[e._v("专题活动")])],1),a("v-uni-text",{staticClass:"iconfont iconright"})],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-input",{staticClass:"ns-text-color-gray",attrs:{type:"text",value:"",placeholder:"留言前建议先与商家协调一致","placeholder-class":"ns-text-color-gray"},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"order-checkout"},[e.orderPaymentData.order_money>0&&e.orderPaymentData.member_account.balance_total>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("使用余额可抵扣"),a("v-uni-text",{staticClass:"ns-text-color ns-margin-left ns-font-size-sm"},[e._v("¥"+e._s(e.balanceDeduct))])],1),a("v-uni-view",{staticClass:"box align-right"}),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==e.orderCreateData.is_balance},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.useBalance.apply(void 0,arguments)}}})],1):e._e(),e.orderPaymentData.shop_goods_list.invoice?a("v-uni-view",{staticClass:"order-cell order-invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("invoicePopup")}}},[1==e.orderCreateData.is_invoice?a("v-uni-text",{staticClass:"ns-text-color-gray"},[e._v(e._s(1==e.orderCreateData.invoice_type?"纸质":"电子")+"发票("+e._s(e.orderCreateData.invoice_content)+")")]):a("v-uni-text",{staticClass:"ns-text-color-gray"},[e._v("无需发票")])],1),a("v-uni-text",{staticClass:"iconfont iconright"})],1):e._e()],1),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("商品金额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.goods_money)))],1)],1)],1),0==e.orderPaymentData.is_virtual&&e.orderPaymentData.delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.delivery_money)))])],1)],1)],1):e._e(),e.orderCreateData.is_invoice&&e.orderPaymentData.invoice_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("税费"),a("v-uni-text",{staticClass:"ns-text-color"},[e._v("("+e._s(e.orderPaymentData.shop_goods_list.invoice.invoice_rate)+"%)")])],1),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.invoice_money)))])],1)],1)],1):e._e(),e.orderCreateData.is_invoice&&e.orderPaymentData.invoice_delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票邮寄费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.invoice_delivery_money)))])],1)],1)],1):e._e(),e.orderPaymentData.promotion_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.promotion_money)))])],1)],1)],1):e._e(),e.orderPaymentData.coupon_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.coupon_money)))])],1)],1)],1):e._e(),e.orderPaymentData.balance_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("使用余额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.balance_money)))])],1)],1)],1):e._e()],1),a("v-uni-view",{staticClass:"order-submit",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"order-settlement-info"},[a("v-uni-text",{staticClass:"ns-text-color-gray ns-margin-right"},[e._v("共"+e._s(e.orderPaymentData.goods_num)+"件")]),a("v-uni-text",[e._v("合计：")]),a("v-uni-text",{staticClass:"ns-text-color ns-font-size-base"},[e._v(e._s(e.$lang("common.currencySymbol"))),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.pay_money)))])],1)],1),a("v-uni-view",{staticClass:"submit-btn"},[a("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderCreate.apply(void 0,arguments)}}},[e._v("提交订单")])],1)],1),a("uni-popup",{ref:"invoicePopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"invoice-popup popup",style:1==e.orderCreateData.is_invoice?"height: 83vh;":"height: 48vh;",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-view",[a("v-uni-text",{staticClass:"tit"},[e._v("发票")])],1),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"iconfont iconclose uni-bold",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeInvoicePopup()}}})],1)],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[e.orderPaymentData.shop_goods_list.invoice?a("v-uni-view",{staticClass:"invoice-cell is-invoice"},[a("v-uni-text",{staticClass:"tit"},[e._v("需要发票")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":0==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("不需要")]),a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":1==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("需要")])],1)],1):e._e(),1==e.orderCreateData.is_invoice?[a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票类型")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":1==e.orderCreateData.invoice_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceType(1)}}},[e._v("纸质")]),a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":2==e.orderCreateData.invoice_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceType(2)}}},[e._v("电子")])],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("抬头类型")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":1==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(1)}}},[e._v("个人")]),a("v-uni-view",{staticClass:"option-item",class:{"ns-bg-color active":2==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(2)}}},[e._v("企业")])],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票信息")]),a("v-uni-view",{staticClass:"invoice-form-group"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请填写抬头名称"},model:{value:e.orderCreateData.invoice_title,callback:function(t){e.$set(e.orderCreateData,"invoice_title",t)},expression:"orderCreateData.invoice_title"}}),2==e.orderCreateData.invoice_title_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写纳税人识别号"},model:{value:e.orderCreateData.taxpayer_number,callback:function(t){e.$set(e.orderCreateData,"taxpayer_number",t)},expression:"orderCreateData.taxpayer_number"}}):e._e(),1==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮寄地址"},model:{value:e.orderCreateData.invoice_full_address,callback:function(t){e.$set(e.orderCreateData,"invoice_full_address",t)},expression:"orderCreateData.invoice_full_address"}}):e._e(),2==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮箱"},model:{value:e.orderCreateData.invoice_email,callback:function(t){e.$set(e.orderCreateData,"invoice_email",t)},expression:"orderCreateData.invoice_email"}}):e._e()],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票内容")]),a("v-uni-view",{staticClass:"option-grpup"},e._l(e.orderPaymentData.shop_goods_list.invoice.invoice_content_array,(function(t,i){return a("v-uni-view",{key:i,staticClass:"option-item",class:{"ns-bg-color active":t==e.orderCreateData.invoice_content},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeInvoiceContent(t)}}},[e._v(e._s(t))])})),1)],1)]:e._e(),a("v-uni-view",{staticClass:"invoice-tops ns-text-color-gray"},[e._v("发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息")])],2)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveInvoice.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn ns-bg-color"},[e._v("保存")])],1)],1)],1),a("uni-popup",{ref:"couponPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"coupon-popup popup",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-view",[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")])],1),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"iconfont iconclose uni-bold",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("couponPopup")}}})],1)],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},e._l(e.orderPaymentData.shop_goods_list.coupon_list,(function(t,i){return a("v-uni-view",{key:i,staticClass:"coupon-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectCoupon(t)}}},[a("v-uni-view",{staticClass:"iconfont",class:e.orderPaymentData.coupon.coupon_id==t.coupon_id?"iconyuan_checked ns-text-color":"iconyuan_checkbox"}),a("v-uni-view",{staticClass:"circular"}),a("v-uni-view",{staticClass:"coupon-info"},["reward"==t.type?a("v-uni-view",{staticClass:"coupon-money"},[a("v-uni-text",{staticClass:"ns-text-color"},[e._v(e._s(parseFloat(t.money)))]),a("v-uni-text",{staticClass:"unit ns-text-color"},[e._v("元")])],1):e._e(),"discount"==t.type?a("v-uni-view",{staticClass:"coupon-money"},[a("v-uni-text",{staticClass:"ns-text-color"},[e._v(e._s(parseFloat(t.discount)))]),a("v-uni-text",{staticClass:"unit ns-text-color"},[e._v("折")])],1):e._e(),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",[e._v(e._s(t.coupon_name))]),t.at_least>0?a("v-uni-view",{staticClass:"ns-text-color-gray ns-font-size-sm"},[e._v("满"+e._s(t.at_least)+"可用"),"discount"==t.type&&t.discount_limit>0?a("v-uni-text",{staticClass:"ns-text-color-gray ns-font-size-sm"},[e._v("(最多可抵"+e._s(t.discount_limit)+")")]):e._e()],1):a("v-uni-view",{staticClass:"ns-text-color-gray ns-font-size-sm"},[e._v("任意金额可用")]),a("v-uni-view",{staticClass:"ns-text-color-gray ns-font-size-sm"},[e._v("有效期："+e._s(e.$util.timeStampTurnTime(t.end_time)))])],1)],1)],1)})),1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn ns-bg-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.popupConfirm("couponPopup")}}},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"sitePromotionPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"promotion-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-view",[a("v-uni-text",{staticClass:"tit"},[e._v("活动优惠")])],1),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"iconfont iconclose uni-bold",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("sitePromotionPopup")}}})],1)],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[a("v-uni-text",{staticClass:"promotion-mark ns-gradient-promotionpages-topics-payment",attrs:{"data-theme":e.themeStyle}},[e._v("专题活动")]),e._v(e._s(e.orderPaymentData.topic_info.topic_name))],1),a("v-uni-view",{staticClass:"box align-right"})],1)],1)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn ns-bg-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("sitePromotionPopup")}}},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"deliveryPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"delivery-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-view",[a("v-uni-text",{staticClass:"tit"},[e._v("已为您甄选出附近所有相关门店")])],1),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"iconfont iconclose uni-bold",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("deliveryPopup")}}})],1)],1),a("v-uni-view",{staticClass:"popup-body store-popup",class:{"safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"delivery-cell delivery-cont"},[e._l(e.storeInfo.storeList,(function(t,i){return a("v-uni-view",{key:i,staticClass:"pickup-point",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectPickupPoint(t)}}},[a("v-uni-view",{staticClass:"delivery-detail"},[a("v-uni-view",{staticClass:"name",class:t.store_id==e.orderPaymentData.delivery.store_id?"ns-text-color":""},[a("v-uni-text",[e._v(e._s(t.store_name))]),t.distance?a("v-uni-text",[e._v("("+e._s(t.distance)+"km)")]):e._e()],1),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",{staticClass:"ns-font-size-sm",class:t.store_id==e.orderPaymentData.delivery.store_id?"ns-text-color":""},[e._v("营业时间："+e._s(t.open_date))]),a("v-uni-view",{staticClass:"ns-font-size-sm",class:t.store_id==e.orderPaymentData.delivery.store_id?"ns-text-color":""},[e._v("地址："+e._s(t.full_address)+e._s(t.address))])],1)],1),t.store_id==e.orderPaymentData.delivery.store_id?a("v-uni-view",{staticClass:"icon"},[a("v-uni-text",{staticClass:"iconfont iconyuan_checked ns-text-color"})],1):e._e()],1)})),e.storeInfo.storeList?e._e():a("v-uni-view",[e._v("所选择收货地址附近没有可以自提的门店")])],2)],1)],1)],1),a("uni-popup",{ref:"payPassword",attrs:{custom:!0}},[a("v-uni-view",{staticClass:"pay-password"},[0==e.orderPaymentData.member_account.is_pay_password?[a("v-uni-view",{staticClass:"title"},[e._v("为了您的账户安全,请先设置您的支付密码")]),a("v-uni-view",{staticClass:"tips"},[e._v('可到"个人中心","个人资料","支付密码"中设置')]),a("v-uni-view",{staticClass:"btn ns-border-color ns-bg-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("立即设置")]),a("v-uni-view",{staticClass:"btn white ns-border-color ns-text-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.noSet.apply(void 0,arguments)}}},[e._v("暂不设置")])]:[a("v-uni-view",{staticClass:"title"},[e._v("请输入支付密码")]),a("v-uni-view",{staticClass:"password-wrap"},[a("myp-one",{ref:"input",attrs:{maxlength:6,"is-pwd":!0,"auto-focus":e.isFocus,type:"box"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.input.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"ns-text-color ns-font-size-sm forget-password",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("忘记密码")])],1)],1)]],2)],1),a("loading-cover",{ref:"loadingCover"}),a("ns-show-toast")],1)},r=[];a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}))},5783:function(e,t,a){"use strict";a("a15b"),a("a9e3"),a("ac1f"),a("1276"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"mypOneInput",props:{value:{type:String,default:""},maxlength:{type:Number,default:4},autoFocus:{type:Boolean,default:!1},isPwd:{type:Boolean,default:!1},type:{type:String,default:"bottom"}},watch:{maxlength:{immediate:!0,handler:function(e){this.ranges=6===e?[1,2,3,4,5,6]:[1,2,3,4]}},value:{immediate:!0,handler:function(e){e!==this.inputValue&&(this.inputValue=e,this.toMakeAndCheck(e))}}},data:function(){return{inputValue:"",codeIndex:1,codeArr:[],ranges:[1,2,3,4]}},methods:{getVal:function(e){var t=e.detail.value;this.inputValue=t,this.$emit("input",t),this.toMakeAndCheck(t)},toMakeAndCheck:function(e){var t=e.split("");this.codeIndex=t.length+1,this.codeArr=t,this.codeIndex>Number(this.maxlength)&&this.$emit("finish",this.codeArr.join(""))},set:function(e){this.inputValue=e,this.toMakeAndCheck(e)},clear:function(){this.inputValue="",this.codeArr=[],this.codeIndex=1}}};t.default=i},"5caa":function(e,t,a){var i=a("5d8c");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("4f06").default;o("251d870e",i,!0,{sourceMap:!1,shadowMode:!1})},"5d8c":function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,'@-webkit-keyframes twinkling-data-v-7bff4eab{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}@keyframes twinkling-data-v-7bff4eab{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}.code-box[data-v-7bff4eab]{text-align:center}.flex-box[data-v-7bff4eab]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-flex-wrap:wrap;flex-wrap:wrap;position:relative}.flex-box .hide-input[data-v-7bff4eab]{position:absolute;top:0;left:-100%;width:200%;height:100%;text-align:left;z-index:9;opacity:1}.flex-box .item[data-v-7bff4eab]{position:relative;-webkit-box-flex:1;-webkit-flex:1;flex:1;margin-right:%?18?%;font-size:%?70?%;font-weight:700;color:#333;line-height:%?100?%}.flex-box .item[data-v-7bff4eab]::before{content:"";padding-top:100%;display:block}.flex-box .item[data-v-7bff4eab]:last-child{margin-right:0}.flex-box .middle[data-v-7bff4eab]{border:none}.flex-box .box[data-v-7bff4eab]{box-sizing:border-box;border:%?2?% solid #ccc;border-width:%?2?% 0 %?2?% %?2?%;margin-right:0}.flex-box .box[data-v-7bff4eab]:first-of-type{border-top-left-radius:%?8?%;border-bottom-left-radius:%?8?%}.flex-box .box[data-v-7bff4eab]:last-child{border-right:%?2?% solid #ccc;border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.flex-box .bottom[data-v-7bff4eab]{box-sizing:border-box;border-bottom:1px solid #ddd}.flex-box .active[data-v-7bff4eab]{border-color:#ddd}.flex-box .active .line[data-v-7bff4eab]{display:block}.flex-box .line[data-v-7bff4eab]{display:none;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?2?%;height:%?40?%;background:#333;-webkit-animation:twinkling-data-v-7bff4eab 1s infinite ease;animation:twinkling-data-v-7bff4eab 1s infinite ease}.flex-box .dot[data-v-7bff4eab],.flex-box .number[data-v-7bff4eab]{line-height:%?40?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.flex-box .number[data-v-7bff4eab]{font-size:$ns-font-size-xxx-lg}.flex-box .dot[data-v-7bff4eab]{font-size:$ns-font-size-lg}.flex-box .bottom-line[data-v-7bff4eab]{height:4px;background:#000;width:80%;position:absolute;border-radius:2px;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}',""]),e.exports=t},"62ff":function(e,t,a){"use strict";var i=a("a35e"),o=a.n(i);o.a},"6bb0":function(e,t,a){"use strict";a.r(t);var i=a("5783"),o=a.n(i);for(var r in i)"default"!==r&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},"7c82":function(e,t,a){"use strict";a.r(t);var i=a("8845"),o=a.n(i);for(var r in i)"default"!==r&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},"82bc":function(e,t,a){"use strict";a.r(t);var i=a("f7d1"),o=a("6bb0");for(var r in o)"default"!==r&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("9687");var n,s=a("f0c5"),d=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"7bff4eab",null,!1,i["a"],n);t["default"]=d.exports},8845:function(e,t,a){"use strict";var i=a("ee27");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("f029")),r=i(a("d380")),n=i(a("82bc")),s=i(a("4c22")),d=i(a("f505")),c=i(a("d806")),l={components:{uniPopup:r.default,mypOne:n.default,nsShowToast:d.default,nsSwitch:c.default},data:function(){return{}},onLoad:function(){},onShow:function(){},mixins:[o.default,s.default]};t.default=l},"8efb":function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,'.weui-switch[data-v-6d66adde]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:1px solid;border-color:#dfdfdf;-webkit-transition:background-color .1s,border .1s;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-6d66adde]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;-webkit-transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-6d66adde]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4);-webkit-transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-6d66adde]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-6d66adde]{border-color:#1aad19}.weui-switch-on .spotview[data-v-6d66adde]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),e.exports=t},9687:function(e,t,a){"use strict";var i=a("5caa"),o=a.n(i);o.a},a35e:function(e,t,a){var i=a("cbd7");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("4f06").default;o("3344919f",i,!0,{sourceMap:!1,shadowMode:!1})},a715:function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.ns-font-size-x-sm[data-v-226c1b28]{font-size:%?20?%}.ns-font-size-sm[data-v-226c1b28]{font-size:%?22?%}.ns-font-size-base[data-v-226c1b28]{font-size:%?24?%}.ns-font-size-lg[data-v-226c1b28]{font-size:%?28?%}.ns-font-size-x-lg[data-v-226c1b28]{font-size:%?32?%}.ns-font-size-xx-lg[data-v-226c1b28]{font-size:%?36?%}.ns-font-size-xxx-lg[data-v-226c1b28]{font-size:%?40?%}.ns-text-color-black[data-v-226c1b28]{color:#333!important}.ns-text-color-gray[data-v-226c1b28]{color:#898989!important}.ns-border-color-gray[data-v-226c1b28]{border-color:#e7e7e7!important}.ns-bg-color-gray[data-v-226c1b28]{background-color:#e5e5e5!important}uni-page-body[data-v-226c1b28]{background-color:#f7f7f7}uni-view[data-v-226c1b28]{font-size:%?28?%;color:#333}.ns-padding[data-v-226c1b28]{padding:%?20?%!important}.ns-padding-top[data-v-226c1b28]{padding-top:%?20?%!important}.ns-padding-right[data-v-226c1b28]{padding-right:%?20?%!important}.ns-padding-bottom[data-v-226c1b28]{padding-bottom:%?20?%!important}.ns-padding-left[data-v-226c1b28]{padding-left:%?20?%!important}.ns-margin[data-v-226c1b28]{margin:%?20?%!important}.ns-margin-top[data-v-226c1b28]{margin-top:%?20?%!important}.ns-margin-right[data-v-226c1b28]{margin-right:%?20?%!important}.ns-margin-bottom[data-v-226c1b28]{margin-bottom:%?20?%!important}.ns-margin-left[data-v-226c1b28]{margin-left:%?20?%!important}.ns-border-radius[data-v-226c1b28]{border-radius:4px!important}uni-button[data-v-226c1b28]:after{border:none!important}uni-button[data-v-226c1b28]::after{border:none!important}.uni-tag--inverted[data-v-226c1b28]{border-color:#e7e7e7!important;color:#333!important}.btn-disabled-color[data-v-226c1b28]{background:#b7b7b7}.pull-right[data-v-226c1b28]{float:right!important}.pull-left[data-v-226c1b28]{float:left!important}.clearfix[data-v-226c1b28]:before,\r\n.clearfix[data-v-226c1b28]:after{content:"";display:block;clear:both}.sku-layer .body-item .number-wrap .number uni-button[data-v-226c1b28],\r\n.sku-layer .body-item .number-wrap .number uni-input[data-v-226c1b28]{border-color:hsla(0,0%,89.8%,.5)!important;background-color:hsla(0,0%,89.8%,.4)!important}.ns-btn-default-all.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}.ns-btn-default-all.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}.ns-btn-default-mine.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}.ns-btn-default-mine.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}\r\n\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */.ns-text-color[data-v-226c1b28]{color:#ff4544!important}.ns-border-color[data-v-226c1b28]{border-color:#ff4544!important}.ns-border-color-top[data-v-226c1b28]{border-top-color:#ff4544!important}.ns-border-color-bottom[data-v-226c1b28]{border-bottom-color:#ff4544!important}.ns-border-color-right[data-v-226c1b28]{border-right-color:#ff4544!important}.ns-border-color-left[data-v-226c1b28]{border-left-color:#ff4544!important}.ns-bg-color[data-v-226c1b28]{background-color:#ff4544!important}.ns-bg-help-color[data-v-226c1b28]{background-color:#ffb644!important}uni-button[data-v-226c1b28]{margin:0 %?60?%;font-size:%?28?%;border-radius:20px;line-height:2.7}uni-button[type="primary"][data-v-226c1b28]{background-color:#ff4544!important}uni-button[type="primary"][plain][data-v-226c1b28]{background-color:initial!important;color:#ff4544!important;border-color:#ff4544!important}uni-button[type="primary"][disabled][data-v-226c1b28]{background:#e5e5e5!important;color:#898989}uni-button[type="primary"].btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}uni-button.btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}uni-button[type="warn"][data-v-226c1b28]{background:#fff;border:%?1?% solid #ff4544!important;color:#ff4544}uni-button[type="warn"][plain][data-v-226c1b28]{background-color:initial!important;color:#ff4544!important;border-color:#ff4544!important}uni-button[type="warn"][disabled][data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}uni-button[type="warn"].btn-disabled[data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}uni-button[size="mini"][data-v-226c1b28]{margin:0!important}uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked[data-v-226c1b28]{color:#ff4544!important}uni-switch .uni-switch-input.uni-switch-input-checked[data-v-226c1b28]{background-color:#ff4544!important;border-color:#ff4544!important}uni-radio .uni-radio-input-checked[data-v-226c1b28]{background-color:#ff4544!important;border-color:#ff4544!important}uni-slider .uni-slider-track[data-v-226c1b28]{background-color:#ff4544!important}.uni-tag--primary[data-v-226c1b28]{color:#fff!important;background-color:#ff4544!important;border-color:#ff4544!important}.uni-tag--primary.uni-tag--inverted[data-v-226c1b28]{color:#ff4544!important;background-color:#fff!important;border-color:#ff4544!important}.goods-coupon-popup-layer .coupon-body .item[data-v-226c1b28]{background-color:#fff!important}.goods-coupon-popup-layer .coupon-body .item uni-view[data-v-226c1b28]{color:#ff7877!important}.sku-layer .body-item .sku-list-wrap .items[data-v-226c1b28]{background-color:#f5f5f5!important}.sku-layer .body-item .sku-list-wrap .items.selected[data-v-226c1b28]{background-color:#fff!important;color:#ff4544!important;border-color:#ff4544!important}.sku-layer .body-item .sku-list-wrap .items.disabled[data-v-226c1b28]{color:#898989!important;cursor:not-allowed!important;pointer-events:none!important;opacity:.5!important;box-shadow:none!important;-webkit-filter:grayscale(100%);filter:grayscale(100%)}.goods-detail .goods-discount[data-v-226c1b28]{background:rgba(255,69,68,.2)}.goods-detail .goods-discount .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#ff7877)!important;background:linear-gradient(90deg,#ff4544,#ff7877)!important}.goods-detail .seckill-wrap[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#faa)!important;background:linear-gradient(90deg,#ff4544,#faa)!important}.goods-detail .goods-module-wrap .original-price .seckill-save-price[data-v-226c1b28]{background:#fff!important;color:#ff4544!important}.goods-detail .goods-pintuan[data-v-226c1b28]{background:rgba(255,69,68,.2)}.goods-detail .goods-pintuan .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#ff7877)!important;background:linear-gradient(90deg,#ff4544,#ff7877)!important}.goods-detail .goods-presale[data-v-226c1b28]{background:rgba(255,69,68,.2)}.goods-detail .goods-presale .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#ff7877)!important;background:linear-gradient(90deg,#ff4544,#ff7877)!important}.goods-detail .topic-wrap .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#ff7877)!important;background:linear-gradient(90deg,#ff4544,#ff7877)!important}.goods-detail .goods-module-wrap .original-price .topic-save-price[data-v-226c1b28]{background:#fff!important;color:#ff4544!important}.goods-detail .goods-groupbuy[data-v-226c1b28]{background:rgba(255,69,68,.2)}.goods-detail .goods-groupbuy .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#ff4544,#ff7877)!important;background:linear-gradient(90deg,#ff4544,#ff7877)!important}.gradual-change[data-v-226c1b28]{background:-webkit-linear-gradient(45deg,#ff4544,rgba(255,69,68,.6))!important;background:linear-gradient(45deg,#ff4544,rgba(255,69,68,.6))!important}.ns-btn-default-all[data-v-226c1b28]{width:100%;height:%?70?%;background:#ff4544;border-radius:%?70?%;text-align:center;line-height:%?70?%;color:#fff;font-size:%?28?%}.ns-btn-default-all.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}.ns-btn-default-all.free[data-v-226c1b28]{width:100%;background:#fff;color:#ff4544;border:%?1?% solid #ff4544;font-size:%?28?%;box-sizing:border-box}.ns-btn-default-all.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}.ns-btn-default-mine[data-v-226c1b28]{display:inline-block;height:%?60?%;border-radius:%?60?%;line-height:%?60?%;padding:0 %?30?%;box-sizing:border-box;color:#fff;background:#ff4544}.ns-btn-default-mine.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}.ns-btn-default-mine.free[data-v-226c1b28]{background:#fff;color:#ff4544;border:%?1?% solid #ff4544;font-size:%?28?%;box-sizing:border-box}.ns-btn-default-mine.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}.order-box-btn[data-v-226c1b28]{display:inline-block;line-height:%?56?%;padding:0 %?30?%;font-size:%?28?%;color:#333;border:%?1?% solid #999;box-sizing:border-box;border-radius:%?60?%;margin-left:%?20?%}.order-box-btn.order-pay[data-v-226c1b28]{background:#ff4544;color:#fff;border-color:#fff}.ns-text-before[data-v-226c1b28]::after, .ns-text-before[data-v-226c1b28]::before{color:#ff4544!important}.ns-bg-before[data-v-226c1b28]::after{background:#ff4544!important}.ns-bg-before[data-v-226c1b28]::before{background:#ff4544!important}[data-theme="theme-blue"] .ns-text-color[data-v-226c1b28]{color:#1786f8!important}[data-theme="theme-blue"] .ns-border-color[data-v-226c1b28]{border-color:#1786f8!important}[data-theme="theme-blue"] .ns-border-color-top[data-v-226c1b28]{border-top-color:#1786f8!important}[data-theme="theme-blue"] .ns-border-color-bottom[data-v-226c1b28]{border-bottom-color:#1786f8!important}[data-theme="theme-blue"] .ns-border-color-right[data-v-226c1b28]{border-right-color:#1786f8!important}[data-theme="theme-blue"] .ns-border-color-left[data-v-226c1b28]{border-left-color:#1786f8!important}[data-theme="theme-blue"] .ns-bg-color[data-v-226c1b28]{background-color:#1786f8!important}[data-theme="theme-blue"] .ns-bg-help-color[data-v-226c1b28]{background-color:#ff851f!important}[data-theme="theme-blue"] uni-button[data-v-226c1b28]{margin:0 %?60?%;font-size:%?28?%;border-radius:20px;line-height:2.7}[data-theme="theme-blue"] uni-button[type="primary"][data-v-226c1b28]{background-color:#1786f8!important}[data-theme="theme-blue"] uni-button[type="primary"][plain][data-v-226c1b28]{background-color:initial!important;color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] uni-button[type="primary"][disabled][data-v-226c1b28]{background:#e5e5e5!important;color:#898989}[data-theme="theme-blue"] uni-button[type="primary"].btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}[data-theme="theme-blue"] uni-button.btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}[data-theme="theme-blue"] uni-button[type="warn"][data-v-226c1b28]{background:#fff;border:%?1?% solid #1786f8!important;color:#1786f8}[data-theme="theme-blue"] uni-button[type="warn"][plain][data-v-226c1b28]{background-color:initial!important;color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] uni-button[type="warn"][disabled][data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}[data-theme="theme-blue"] uni-button[type="warn"].btn-disabled[data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}[data-theme="theme-blue"] uni-button[size="mini"][data-v-226c1b28]{margin:0!important}[data-theme="theme-blue"] uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked[data-v-226c1b28]{color:#1786f8!important}[data-theme="theme-blue"] uni-switch .uni-switch-input.uni-switch-input-checked[data-v-226c1b28]{background-color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] uni-radio .uni-radio-input-checked[data-v-226c1b28]{background-color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] uni-slider .uni-slider-track[data-v-226c1b28]{background-color:#1786f8!important}[data-theme="theme-blue"] .uni-tag--primary[data-v-226c1b28]{color:#fff!important;background-color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] .uni-tag--primary.uni-tag--inverted[data-v-226c1b28]{color:#1786f8!important;background-color:#fff!important;border-color:#1786f8!important}[data-theme="theme-blue"] .goods-coupon-popup-layer .coupon-body .item[data-v-226c1b28]{background-color:#f6faff!important}[data-theme="theme-blue"] .goods-coupon-popup-layer .coupon-body .item uni-view[data-v-226c1b28]{color:#49a0f9!important}[data-theme="theme-blue"] .sku-layer .body-item .sku-list-wrap .items[data-v-226c1b28]{background-color:#f5f5f5!important}[data-theme="theme-blue"] .sku-layer .body-item .sku-list-wrap .items.selected[data-v-226c1b28]{background-color:#f6faff!important;color:#1786f8!important;border-color:#1786f8!important}[data-theme="theme-blue"] .sku-layer .body-item .sku-list-wrap .items.disabled[data-v-226c1b28]{color:#898989!important;cursor:not-allowed!important;pointer-events:none!important;opacity:.5!important;box-shadow:none!important;-webkit-filter:grayscale(100%);filter:grayscale(100%)}[data-theme="theme-blue"] .goods-detail .goods-discount[data-v-226c1b28]{background:rgba(255,69,68,.4)}[data-theme="theme-blue"] .goods-detail .goods-discount .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#49a0f9)!important;background:linear-gradient(90deg,#1786f8,#49a0f9)!important}[data-theme="theme-blue"] .goods-detail .seckill-wrap[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#7abafb)!important;background:linear-gradient(90deg,#1786f8,#7abafb)!important}[data-theme="theme-blue"] .goods-detail .goods-module-wrap .original-price .seckill-save-price[data-v-226c1b28]{background:#ddedfe!important;color:#1786f8!important}[data-theme="theme-blue"] .goods-detail .goods-pintuan[data-v-226c1b28]{background:rgba(255,69,68,.4)}[data-theme="theme-blue"] .goods-detail .goods-pintuan .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#49a0f9)!important;background:linear-gradient(90deg,#1786f8,#49a0f9)!important}[data-theme="theme-blue"] .goods-detail .goods-presale[data-v-226c1b28]{background:rgba(255,69,68,.4)}[data-theme="theme-blue"] .goods-detail .goods-presale .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#49a0f9)!important;background:linear-gradient(90deg,#1786f8,#49a0f9)!important}[data-theme="theme-blue"] .goods-detail .topic-wrap .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#ff7877)!important;background:linear-gradient(90deg,#1786f8,#ff7877)!important}[data-theme="theme-blue"] .goods-detail .goods-module-wrap .original-price .topic-save-price[data-v-226c1b28]{background:#ddedfe!important;color:#1786f8!important}[data-theme="theme-blue"] .goods-detail .goods-groupbuy[data-v-226c1b28]{background:-webkit-linear-gradient(top,#fef391,#fbe253);background:linear-gradient(180deg,#fef391,#fbe253)}[data-theme="theme-blue"] .goods-detail .goods-groupbuy .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#1786f8,#49a0f9)!important;background:linear-gradient(90deg,#1786f8,#49a0f9)!important}[data-theme="theme-blue"] .gradual-change[data-v-226c1b28]{background:-webkit-linear-gradient(45deg,#1786f8,rgba(23,134,248,.6))!important;background:linear-gradient(45deg,#1786f8,rgba(23,134,248,.6))!important}[data-theme="theme-blue"] .ns-btn-default-all[data-v-226c1b28]{width:100%;height:%?70?%;background:#1786f8;border-radius:%?70?%;text-align:center;line-height:%?70?%;color:#fff;font-size:%?28?%}[data-theme="theme-blue"] .ns-btn-default-all.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}[data-theme="theme-blue"] .ns-btn-default-all.free[data-v-226c1b28]{width:100%;background:#fff;color:#1786f8;border:%?1?% solid #1786f8;font-size:%?28?%;box-sizing:border-box}[data-theme="theme-blue"] .ns-btn-default-all.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}[data-theme="theme-blue"] .ns-btn-default-mine[data-v-226c1b28]{display:inline-block;height:%?60?%;border-radius:%?60?%;line-height:%?60?%;padding:0 %?30?%;box-sizing:border-box;color:#fff;background:#1786f8}[data-theme="theme-blue"] .ns-btn-default-mine.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}[data-theme="theme-blue"] .ns-btn-default-mine.free[data-v-226c1b28]{background:#fff;color:#1786f8;border:%?1?% solid #1786f8;font-size:%?28?%;box-sizing:border-box}[data-theme="theme-blue"] .ns-btn-default-mine.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}[data-theme="theme-blue"] .order-box-btn[data-v-226c1b28]{display:inline-block;line-height:%?56?%;padding:0 %?30?%;font-size:%?28?%;color:#333;border:%?1?% solid #999;box-sizing:border-box;border-radius:%?60?%;margin-left:%?20?%}[data-theme="theme-blue"] .order-box-btn.order-pay[data-v-226c1b28]{background:#1786f8;color:#fff;border-color:#fff}[data-theme="theme-blue"] .ns-text-before[data-v-226c1b28]::after, [data-theme="theme-blue"] .ns-text-before[data-v-226c1b28]::before{color:#1786f8!important}[data-theme="theme-blue"] .ns-bg-before[data-v-226c1b28]::after{background:#1786f8!important}[data-theme="theme-blue"] .ns-bg-before[data-v-226c1b28]::before{background:#1786f8!important}[data-theme="theme-green"] .ns-text-color[data-v-226c1b28]{color:#31bb6d!important}[data-theme="theme-green"] .ns-border-color[data-v-226c1b28]{border-color:#31bb6d!important}[data-theme="theme-green"] .ns-border-color-top[data-v-226c1b28]{border-top-color:#31bb6d!important}[data-theme="theme-green"] .ns-border-color-bottom[data-v-226c1b28]{border-bottom-color:#31bb6d!important}[data-theme="theme-green"] .ns-border-color-right[data-v-226c1b28]{border-right-color:#31bb6d!important}[data-theme="theme-green"] .ns-border-color-left[data-v-226c1b28]{border-left-color:#31bb6d!important}[data-theme="theme-green"] .ns-bg-color[data-v-226c1b28]{background-color:#31bb6d!important}[data-theme="theme-green"] .ns-bg-help-color[data-v-226c1b28]{background-color:#393a39!important}[data-theme="theme-green"] uni-button[data-v-226c1b28]{margin:0 %?60?%;font-size:%?28?%;border-radius:20px;line-height:2.7}[data-theme="theme-green"] uni-button[type="primary"][data-v-226c1b28]{background-color:#31bb6d!important}[data-theme="theme-green"] uni-button[type="primary"][plain][data-v-226c1b28]{background-color:initial!important;color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] uni-button[type="primary"][disabled][data-v-226c1b28]{background:#e5e5e5!important;color:#898989}[data-theme="theme-green"] uni-button[type="primary"].btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}[data-theme="theme-green"] uni-button.btn-disabled[data-v-226c1b28]{background:#e5e5e5!important;color:#898989!important}[data-theme="theme-green"] uni-button[type="warn"][data-v-226c1b28]{background:#fff;border:%?1?% solid #31bb6d!important;color:#31bb6d}[data-theme="theme-green"] uni-button[type="warn"][plain][data-v-226c1b28]{background-color:initial!important;color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] uni-button[type="warn"][disabled][data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}[data-theme="theme-green"] uni-button[type="warn"].btn-disabled[data-v-226c1b28]{border:%?1?% solid #e7e7e7!important;color:#898989}[data-theme="theme-green"] uni-button[size="mini"][data-v-226c1b28]{margin:0!important}[data-theme="theme-green"] uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked[data-v-226c1b28]{color:#31bb6d!important}[data-theme="theme-green"] uni-switch .uni-switch-input.uni-switch-input-checked[data-v-226c1b28]{background-color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] uni-radio .uni-radio-input-checked[data-v-226c1b28]{background-color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] uni-slider .uni-slider-track[data-v-226c1b28]{background-color:#31bb6d!important}[data-theme="theme-green"] .uni-tag--primary[data-v-226c1b28]{color:#fff!important;background-color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] .uni-tag--primary.uni-tag--inverted[data-v-226c1b28]{color:#31bb6d!important;background-color:#fff!important;border-color:#31bb6d!important}[data-theme="theme-green"] .goods-coupon-popup-layer .coupon-body .item[data-v-226c1b28]{background-color:#dcf6e7!important}[data-theme="theme-green"] .goods-coupon-popup-layer .coupon-body .item uni-view[data-v-226c1b28]{color:#4ed187!important}[data-theme="theme-green"] .sku-layer .body-item .sku-list-wrap .items[data-v-226c1b28]{background-color:#f5f5f5!important}[data-theme="theme-green"] .sku-layer .body-item .sku-list-wrap .items.selected[data-v-226c1b28]{background-color:#dcf6e7!important;color:#31bb6d!important;border-color:#31bb6d!important}[data-theme="theme-green"] .sku-layer .body-item .sku-list-wrap .items.disabled[data-v-226c1b28]{color:#898989!important;cursor:not-allowed!important;pointer-events:none!important;opacity:.5!important;box-shadow:none!important;-webkit-filter:grayscale(100%);filter:grayscale(100%)}[data-theme="theme-green"] .goods-detail .goods-discount[data-v-226c1b28]{background:rgba(49,187,109,.4)}[data-theme="theme-green"] .goods-detail .goods-discount .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#4ed187)!important;background:linear-gradient(90deg,#31bb6d,#4ed187)!important}[data-theme="theme-green"] .goods-detail .seckill-wrap[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#77dba2)!important;background:linear-gradient(90deg,#31bb6d,#77dba2)!important}[data-theme="theme-green"] .goods-detail .goods-module-wrap .original-price .seckill-save-price[data-v-226c1b28]{background:#c8f0d9!important;color:#31bb6d!important}[data-theme="theme-green"] .goods-detail .goods-pintuan[data-v-226c1b28]{background:rgba(49,187,109,.4)}[data-theme="theme-green"] .goods-detail .goods-pintuan .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#4ed187)!important;background:linear-gradient(90deg,#31bb6d,#4ed187)!important}[data-theme="theme-green"] .goods-detail .goods-presale[data-v-226c1b28]{background:rgba(49,187,109,.4)}[data-theme="theme-green"] .goods-detail .goods-presale .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#4ed187)!important;background:linear-gradient(90deg,#31bb6d,#4ed187)!important}[data-theme="theme-green"] .goods-detail .topic-wrap .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#ff7877)!important;background:linear-gradient(90deg,#31bb6d,#ff7877)!important}[data-theme="theme-green"] .goods-detail .goods-module-wrap .original-price .topic-save-price[data-v-226c1b28]{background:#c8f0d9!important;color:#31bb6d!important}[data-theme="theme-green"] .coupon-body .item-btn[data-v-226c1b28]{background:rgba(49,187,109,.8)}[data-theme="theme-green"] .coupon-info .coupon-content_item[data-v-226c1b28]::before{border:1px solid #31bb6d;border-right:none}[data-theme="theme-green"] .coupon-content_item[data-v-226c1b28]::after{border:1px solid #31bb6d;border-left:none}[data-theme="theme-green"] .goods-detail .goods-groupbuy[data-v-226c1b28]{background:rgba(49,187,109,.4)}[data-theme="theme-green"] .goods-detail .goods-groupbuy .price-info[data-v-226c1b28]{background:-webkit-linear-gradient(left,#31bb6d,#4ed187)!important;background:linear-gradient(90deg,#31bb6d,#4ed187)!important}[data-theme="theme-green"] .gradual-change[data-v-226c1b28]{background:-webkit-linear-gradient(45deg,#31bb6d,rgba(49,187,109,.6))!important;background:linear-gradient(45deg,#31bb6d,rgba(49,187,109,.6))!important}[data-theme="theme-green"] .ns-btn-default-all[data-v-226c1b28]{width:100%;height:%?70?%;background:#31bb6d;border-radius:%?70?%;text-align:center;line-height:%?70?%;color:#fff;font-size:%?28?%}[data-theme="theme-green"] .ns-btn-default-all.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}[data-theme="theme-green"] .ns-btn-default-all.free[data-v-226c1b28]{width:100%;background:#fff;color:#31bb6d;border:%?1?% solid #31bb6d;font-size:%?28?%;box-sizing:border-box}[data-theme="theme-green"] .ns-btn-default-all.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}[data-theme="theme-green"] .ns-btn-default-mine[data-v-226c1b28]{display:inline-block;height:%?60?%;border-radius:%?60?%;line-height:%?60?%;padding:0 %?30?%;box-sizing:border-box;color:#fff;background:#31bb6d}[data-theme="theme-green"] .ns-btn-default-mine.gray[data-v-226c1b28]{background:#e5e5e5;color:#898989}[data-theme="theme-green"] .ns-btn-default-mine.free[data-v-226c1b28]{background:#fff;color:#31bb6d;border:%?1?% solid #31bb6d;font-size:%?28?%;box-sizing:border-box}[data-theme="theme-green"] .ns-btn-default-mine.free.gray[data-v-226c1b28]{background:#fff;color:#898989;border:%?1?% solid #e7e7e7}[data-theme="theme-green"] .order-box-btn[data-v-226c1b28]{display:inline-block;line-height:%?56?%;padding:0 %?30?%;font-size:%?28?%;color:#333;border:%?1?% solid #999;box-sizing:border-box;border-radius:%?60?%;margin-left:%?20?%}[data-theme="theme-green"] .order-box-btn.order-pay[data-v-226c1b28]{background:#31bb6d;color:#fff;border-color:#fff}[data-theme="theme-green"] .ns-text-before[data-v-226c1b28]::after, [data-theme="theme-green"] .ns-text-before[data-v-226c1b28]::before{color:#31bb6d!important}[data-theme="theme-green"] .ns-bg-before[data-v-226c1b28]::after{background:#31bb6d!important}[data-theme="theme-green"] .ns-bg-before[data-v-226c1b28]::before{background:#31bb6d!important}.ns-gradient-base-help-left[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#ff4544,#ffb644);background:linear-gradient(270deg,#ff4544,#ffb644)}.ns-gradient-base-help-left[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#31bb6d,#393a39);background:linear-gradient(270deg,#31bb6d,#393a39)}.ns-gradient-base-help-left[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#1786f8,#ff851f);background:linear-gradient(270deg,#1786f8,#ff851f)}.ns-gradient-otherpages-fenxiao-apply-apply-bg[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#ff4544,#faa);background:linear-gradient(270deg,#ff4544,#faa)}.ns-gradient-otherpages-fenxiao-apply-apply-bg[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#31bb6d,#77dba2);background:linear-gradient(270deg,#31bb6d,#77dba2)}.ns-gradient-otherpages-fenxiao-apply-apply-bg[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(left,#61adfa,#1786f8);background:linear-gradient(90deg,#61adfa,#1786f8)}.ns-gradient-otherpages-member-widthdrawal-withdrawal[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#ff4544,#faa);background:linear-gradient(270deg,#ff4544,#faa)}.ns-gradient-otherpages-member-widthdrawal-withdrawal[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#31bb6d,#77dba2);background:linear-gradient(270deg,#31bb6d,#77dba2)}.ns-gradient-otherpages-member-widthdrawal-withdrawal[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#1786f8,#7abafb);background:linear-gradient(270deg,#1786f8,#7abafb)}.ns-gradient-otherpages-member-balance-balance-rechange[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(top,#ff4544,#fd7e4b);background:linear-gradient(180deg,#ff4544,#fd7e4b)}.ns-gradient-otherpages-member-balance-balance-rechange[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(top,#31bb6d,#fd7e4b);background:linear-gradient(180deg,#31bb6d,#fd7e4b)}.ns-gradient-otherpages-member-balance-balance-rechange[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(top,#1786f8,#fd7e4b);background:linear-gradient(180deg,#1786f8,#fd7e4b)}.ns-gradient-pages-member-index-index[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#ff7877,#ff403f)!important;background:linear-gradient(270deg,#ff7877,#ff403f)!important}.ns-gradient-pages-member-index-index[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#4ed187,#30b76b)!important;background:linear-gradient(270deg,#4ed187,#30b76b)!important}.ns-gradient-pages-member-index-index[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(right,#49a0f9,#1283f8)!important;background:linear-gradient(270deg,#49a0f9,#1283f8)!important}.ns-gradient-promotionpages-pintuan-share-share[data-theme="theme-default"][data-v-226c1b28]{background-image:-webkit-linear-gradient(left,#ffa2a2,#ff4544);background-image:linear-gradient(90deg,#ffa2a2,#ff4544)}.ns-gradient-promotionpages-pintuan-share-share[data-theme="theme-green"][data-v-226c1b28]{background-image:-webkit-linear-gradient(left,#98ddb6,#31bb6d);background-image:linear-gradient(90deg,#98ddb6,#31bb6d)}.ns-gradient-promotionpages-pintuan-share-share[data-theme="theme-blue"][data-v-226c1b28]{background-image:-webkit-linear-gradient(left,#8bc3fc,#1786f8);background-image:linear-gradient(90deg,#8bc3fc,#1786f8)}.ns-gradient-promotionpages-topics-payment[data-theme="theme-default"][data-v-226c1b28]{background:-webkit-linear-gradient(left,#ffa2a2,#ff4544)!important;background:linear-gradient(90deg,#ffa2a2,#ff4544)!important}.ns-gradient-promotionpages-topics-payment[data-theme="theme-green"][data-v-226c1b28]{background:-webkit-linear-gradient(left,#98ddb6,#31bb6d)!important;background:linear-gradient(90deg,#98ddb6,#31bb6d)!important}.ns-gradient-promotionpages-topics-payment[data-theme="theme-blue"][data-v-226c1b28]{background:-webkit-linear-gradient(left,#8bc3fc,#1786f8)!important;background:linear-gradient(90deg,#8bc3fc,#1786f8)!important}.ns-gradient-promotionpages-pintuan-payment[data-theme="theme-default"][data-v-226c1b28]{background:rgba(255,69,68,.08)!important}.ns-gradient-promotionpages-pintuan-payment[data-theme="theme-green"][data-v-226c1b28]{background:rgba(49,187,109,.08)!important}.ns-gradient-promotionpages-pintuan-payment[data-theme="theme-blue"][data-v-226c1b28]{background:rgba(23,134,248,.08)!important}.ns-gradient-diy-goods-list[data-theme="theme-default"][data-v-226c1b28]{border-color:rgba(255,69,68,.2)!important}.ns-gradient-diy-goods-list[data-theme="theme-green"][data-v-226c1b28]{border-color:rgba(49,187,109,.2)!important}.ns-gradient-diy-goods-list[data-theme="theme-blue"][data-v-226c1b28]{border-color:rgba(23,134,248,.2)!important}.ns-gradient-detail-coupons-right-border[data-theme="theme-default"][data-v-226c1b28]{border-right-color:rgba(255,69,68,.2)!important}.ns-gradient-detail-coupons-right-border[data-theme="theme-green"][data-v-226c1b28]{border-right-color:rgba(49,187,109,.2)!important}.ns-gradient-detail-coupons-right-border[data-theme="theme-blue"][data-v-226c1b28]{border-right-color:rgba(23,134,248,.2)!important}.ns-gradient-detail-coupons[data-theme="theme-default"][data-v-226c1b28]{background-color:rgba(255,69,68,.8)!important}.ns-gradient-detail-coupons[data-theme="theme-green"][data-v-226c1b28]{background-color:rgba(49,187,109,.8)!important}.ns-gradient-detail-coupons[data-theme="theme-blue"][data-v-226c1b28]{background-color:rgba(23,134,248,.8)!important}.ns-pages-goods-category-category[data-theme="theme-default"][data-v-226c1b28]{background-image:-webkit-linear-gradient(315deg,#ff4544,#ff7444)!important;background-image:linear-gradient(135deg,#ff4544,#ff7444)!important}.ns-pages-goods-category-category[data-theme="theme-green"][data-v-226c1b28]{background-image:-webkit-linear-gradient(315deg,#31bb6d,#ff7444)!important;background-image:linear-gradient(135deg,#31bb6d,#ff7444)!important}.ns-pages-goods-category-category[data-theme="theme-blue"][data-v-226c1b28]{background-image:-webkit-linear-gradient(315deg,#1786f8,#ff7444)!important;background-image:linear-gradient(135deg,#1786f8,#ff7444)!important}.ns-gradient-pintuan-border-color[data-theme="theme-default"][data-v-226c1b28]{border-color:rgba(255,69,68,.2)!important}.ns-gradient-pintuan-border-color[data-theme="theme-green"][data-v-226c1b28]{border-color:rgba(49,187,109,.2)!important}.ns-gradient-pintuan-border-color[data-theme="theme-blue"][data-v-226c1b28]{border-color:rgba(23,134,248,.2)!important}\nuni-view[data-v-226c1b28],\r\nuni-input[data-v-226c1b28],\r\nuni-text[data-v-226c1b28],\r\nuni-textarea[data-v-226c1b28]{font-size:%?26?%}.popup-header .iconclose[data-v-226c1b28]{font-size:%?40?%}.inline[data-v-226c1b28]{display:inline!important}.order-container[data-v-226c1b28]{padding-bottom:%?160?%}.order-container.safe-area[data-v-226c1b28]{padding-bottom:%?188?%}.address-wrap[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative;min-height:%?100?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-top:%?-76?%;padding:%?24?%}.address-wrap .icon[data-v-226c1b28]{width:%?60?%;height:%?60?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;border-radius:50%;position:absolute;top:%?40?%;margin-right:%?20?%;background-color:#ff4544}.address-wrap .icon uni-image[data-v-226c1b28]{width:%?60?%;height:%?60?%}.address-wrap .icon .iconfont[data-v-226c1b28]{line-height:1;color:#fff;font-size:%?40?%}.address-wrap .icon .iconmendian[data-v-226c1b28]{font-size:%?32?%}.address-wrap .address-info[data-v-226c1b28]{padding-left:%?100?%;padding-right:%?40?%;-webkit-box-flex:1;-webkit-flex:1;flex:1}.address-wrap .address-info .info[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex}.address-wrap .address-info .info uni-text[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1}.address-wrap .address-info .info uni-text[data-v-226c1b28]:last-of-type{text-align:right;color:#999}.address-wrap .address-info .detail[data-v-226c1b28]{margin-top:%?8?%;line-height:1.3}.address-wrap .cell-more[data-v-226c1b28]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?10?%}.address-wrap .cell-more .iconfont[data-v-226c1b28]{color:#999;font-size:%?36?%}.mobile-wrap[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative;min-height:%?100?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-top:%?-76?%;padding:%?24?%}.mobile-wrap .icon[data-v-226c1b28]{width:%?60?%;height:%?60?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;border-radius:50%;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);margin-right:%?20?%;background-color:#ff4544}.mobile-wrap .icon uni-image[data-v-226c1b28]{width:%?60?%;height:%?60?%}.mobile-wrap .icon .iconfont[data-v-226c1b28]{line-height:1;color:#fff}.mobile-wrap .icon .iconmendian[data-v-226c1b28]{font-size:%?32?%}.mobile-wrap .mobile-info[data-v-226c1b28]{padding-left:%?100?%}.mobile-wrap .form-group .form-item[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;line-height:%?50?%}.mobile-wrap .form-group .form-item .text[data-v-226c1b28]{display:inline-block;line-height:%?50?%;padding-right:%?10?%}.mobile-wrap .form-group .form-item .placeholder[data-v-226c1b28]{line-height:%?50?%}.mobile-wrap .form-group .form-item .input[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;height:%?50?%;line-height:%?50?%;color:#838383}.order-cell[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;margin:%?20?% 0;-webkit-box-align:center;-webkit-align-items:center;align-items:center;background:#fff;line-height:%?40?%;position:relative}.order-cell .tit[data-v-226c1b28]{text-align:left}.order-cell .box[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.order-cell .box .textarea[data-v-226c1b28]{height:%?40?%;font-size:%?24?%!important}.order-cell .box uni-input[data-v-226c1b28]{font-size:%?24?%!important}.order-cell .iconfont[data-v-226c1b28]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-226c1b28]{padding:0}.order-cell .order-pay uni-text[data-v-226c1b28]{display:inline-block;margin-left:%?6?%}.site-wrap[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.site-wrap .site-header[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.site-wrap .site-header .icondianpu[data-v-226c1b28]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.site-wrap .site-body .goods-wrap[data-v-226c1b28]{margin-bottom:%?20?%;display:-webkit-box;display:-webkit-flex;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-226c1b28]:last-of-type{margin-bottom:0;padding-bottom:%?20?%;border-bottom:%?2?% solid #f1f1f1}.site-wrap .site-body .goods-wrap .goods-img[data-v-226c1b28]{width:%?124?%;height:%?124?%;padding:%?20?% 0 0 0;margin-right:%?20?%;border-radius:4px;overflow:hidden}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-226c1b28]{width:100%;height:100%;border-radius:4px}.site-wrap .site-body .goods-wrap .goods-info[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;position:relative;padding:%?20?% 0 0 0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-226c1b28]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-226c1b28]{width:100%;line-height:1.3;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:0 %?10?%;box-sizing:border-box}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .iconclose[data-v-226c1b28]{font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-226c1b28]{font-weight:700;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-226c1b28]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;line-height:1.3;font-weight:500}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-226c1b28]:last-of-type{text-align:right}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-226c1b28]{line-height:1}.site-wrap .site-footer .order-cell .tit[data-v-226c1b28]{width:%?180?%;text-align:left}.site-wrap .site-footer .order-cell .box uni-input[data-v-226c1b28]{font-size:%?24?%!important}.site-wrap .site-footer .order-cell .box.text-overflow uni-text[data-v-226c1b28]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;width:100%}.site-wrap .site-footer .order-cell[data-v-226c1b28]:last-of-type{margin-bottom:0}.order-checkout[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.order-checkout .order-cell .iconyuan_checkbox[data-v-226c1b28],\r\n.order-checkout .order-cell .iconyuan_checked[data-v-226c1b28]{font-size:%?40?%;position:absolute;top:50%;right:0;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#aaa}.order-money[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.order-money .order-cell .box[data-v-226c1b28]{font-weight:600;padding:0}.order-money .order-cell .box .operator[data-v-226c1b28]{font-size:%?24?%;margin-right:%?6?%}.order-money .order-cell .box uni-input[data-v-226c1b28]{font-size:%?24?%!important}.order-submit[data-v-226c1b28]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;text-align:right;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.order-submit.bottom-safe-area[data-v-226c1b28]{padding-bottom:%?68?%!important}.order-submit .order-settlement-info[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;height:%?100?%;line-height:%?100?%}.order-submit .submit-btn[data-v-226c1b28]{height:%?80?%;margin:0 %?20?% 0 %?34?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.order-submit .submit-btn uni-button[data-v-226c1b28]{line-height:%?54?%;width:%?156?%;height:%?54?%;text-align:center;padding:0}.popup[data-v-226c1b28]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;border-bottom:%?2?% solid #f1f1f1;padding:%?40?%}.popup .popup-header > uni-view[data-v-226c1b28]{line-height:1}.popup .popup-header .tit[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;font-size:%?28?%}.popup .popup-header .align-right[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.popup .popup-header .vice-tit[data-v-226c1b28]{margin-right:%?20?%}.popup .popup-body[data-v-226c1b28]{height:calc(100% - %?250?%)}.popup .popup-body.store-popup[data-v-226c1b28]{height:calc(100% - %?120?%)}.popup .popup-body.safe-area[data-v-226c1b28]{height:calc(100% - %?270?%)}.popup .popup-body.store-popup.safe-area[data-v-226c1b28]{height:calc(100% - %?140?%)}.popup .popup-footer[data-v-226c1b28]{height:%?120?%}.popup .popup-footer .confirm-btn[data-v-226c1b28]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?40?%;border-radius:%?40?%}.popup .popup-footer.bottom-safe-area[data-v-226c1b28]{padding-bottom:%?68?%!important}.invoice-popup[data-v-226c1b28]{height:83vh;padding:%?18?% 0;box-sizing:border-box;position:relative}.invoice-popup .invoice-close[data-v-226c1b28]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?40?%;z-index:9}.invoice-popup .popup-body .invoice-cell[data-v-226c1b28]{padding:%?30?% 0;border-top:1px solid #f5f5f5;margin:0 %?48?%}.invoice-popup .popup-body .invoice-cell[data-v-226c1b28]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-226c1b28]{font-size:%?28?%}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-226c1b28]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-226c1b28]{height:%?54?%;line-height:%?54?%;display:inline-block;font-size:%?22?%;padding:0 %?36?%;background:#eee;border:1px solid #eee;border-radius:50px;margin:0 %?20?% %?20?% 0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(1), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(2), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(3){margin-bottom:0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.active[data-v-226c1b28]{background:#ff4544;color:#fff}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.disabled[data-v-226c1b28]{color:#aaa}.invoice-popup .popup-body .invoice-cell .invoice-form-group uni-input[data-v-226c1b28]{background:#f1f1f1;border-radius:%?10?%;height:%?66?%;margin-top:%?22?%;padding:0 %?32?%}.invoice-popup .popup-body .invoice-tops[data-v-226c1b28]{font-size:%?20?%;margin:0 %?48?%}.coupon-popup[data-v-226c1b28]{height:65vh}.coupon-popup .popup-body[data-v-226c1b28]{background:#f5f5f5}.coupon-popup .coupon-item[data-v-226c1b28]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.coupon-popup .coupon-item > .iconfont[data-v-226c1b28]{font-size:%?40?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.coupon-popup .coupon-item > .iconyuan_checkbox[data-v-226c1b28]{color:#898989}.coupon-popup .coupon-item .circular[data-v-226c1b28]{position:absolute;top:50%;left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background:#f5f5f5;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item .coupon-info[data-v-226c1b28]{padding-right:%?60?%;height:%?140?%;display:-webkit-box;display:-webkit-flex;display:flex;width:100%}.coupon-popup .coupon-item .coupon-info .coupon-money[data-v-226c1b28]{width:%?160?%;height:%?140?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-right:%?20?%}.coupon-popup .coupon-item .coupon-info .coupon-money uni-text[data-v-226c1b28]{font-size:%?50?%}.coupon-popup .coupon-item .coupon-info .info[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;max-width:calc(100% - %?240?%)}.coupon-popup .coupon-item .coupon-info .info uni-view[data-v-226c1b28]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-popup .coupon-item .coupon-info .info .ns-text-color-gray[data-v-226c1b28]{line-height:1.5}.promotion-popup[data-v-226c1b28]{height:65vh}.promotion-popup .order-cell[data-v-226c1b28]{margin:%?20?% %?30?%}.promotion-popup .order-cell .promotion-mark[data-v-226c1b28]{padding:%?4?% %?10?%;line-height:1;border-radius:%?32?%;color:#fff;font-size:%?24?%;margin-right:%?10?%}.delivery-popup[data-v-226c1b28]{height:80vh;box-sizing:border-box}.delivery-popup .delivery-cell[data-v-226c1b28]{padding:%?30?% 0;box-sizing:border-box}.delivery-popup .delivery-cell .tit[data-v-226c1b28]{font-size:%?28?%}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-226c1b28]{display:inline-block;line-height:1;font-size:%?28?%;padding:%?16?% %?40?%;border:1px solid #eee;border-radius:%?32?%;margin:0 %?20?% %?20?% 0}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(1), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(2), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-226c1b28]:nth-of-type(3){margin-bottom:0}.delivery-popup .delivery-cell .option-grpup .option-item.disabled[data-v-226c1b28]{color:#aaa}.delivery-popup .delivery-cont[data-v-226c1b28]{height:100%;overflow-y:scroll}.delivery-popup .delivery-cont .pickup-point[data-v-226c1b28]{padding:%?20?% 0;box-sizing:border-box;border-top:1px solid #f5f5f5;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin:0 %?48?%}.delivery-popup .delivery-cont .pickup-point .color-active uni-view[data-v-226c1b28],\r\n.delivery-popup .delivery-cont .pickup-point .color-active uni-text[data-v-226c1b28]{color:#ff4544}.delivery-popup .delivery-cont .pickup-point .delivery-detail[data-v-226c1b28]{width:90%}.delivery-popup .delivery-cont .pickup-point .name[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex}.delivery-popup .delivery-cont .pickup-point .name uni-text[data-v-226c1b28]{font-size:%?28?%}.delivery-popup .delivery-cont .pickup-point .icon[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1;text-align:right}.delivery-popup .delivery-cont .pickup-point .icon .iconfont[data-v-226c1b28]{line-height:1;font-size:%?32?%}.delivery-popup .delivery-cont .pickup-point[data-v-226c1b28]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-cont .pickup-point .info[data-v-226c1b28]{line-height:1.2}.delivery-popup .delivery-cont .pickup-point .info uni-view[data-v-226c1b28]{font-size:%?22?%}.pay-password[data-v-226c1b28]{width:80vw;background:#fff;box-sizing:border-box;border-radius:%?10?%;overflow:hidden;padding:%?60?% %?40?%;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.pay-password .title[data-v-226c1b28]{font-size:%?28?%;text-align:center}.pay-password .tips[data-v-226c1b28]{font-size:%?24?%;color:#999;text-align:center}.pay-password .btn[data-v-226c1b28]{width:60%;margin:0 auto;margin-top:%?30?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;color:#fff;text-align:center;border:1px solid #fff}.pay-password .btn.white[data-v-226c1b28]{margin-top:%?20?%;background-color:#fff!important}.pay-password .password-wrap[data-v-226c1b28]{padding-top:%?20?%;width:90%;margin:0 auto}.pay-password .password-wrap .forget-password[data-v-226c1b28]{margin-top:%?20?%;display:inline-block}.head-nav[data-v-226c1b28]{width:100%;height:0}.head-nav.active[data-v-226c1b28]{padding-top:%?40?%}.head-return[data-v-226c1b28]{height:%?90?%;line-height:%?90?%;color:#fff;font-weight:600;font-size:%?32?%;width:100%;text-align:center;position:relative}.head-return uni-text[data-v-226c1b28]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?20?%;display:inline-block;margin-right:%?10?%;font-size:%?32?%}.payment-top[data-v-226c1b28]{width:100%;height:%?180?%;overflow:hidden}\r\n/* 2020/6/1 新增样式 */.big-tit[data-v-226c1b28]{font-size:%?32?%}.balance-switch[data-v-226c1b28]{-webkit-transform:scale(.8);transform:scale(.8)}.margin-top[data-v-226c1b28]{margin-top:%?-76?%}.store-wrap[data-v-226c1b28]{background-color:#fff;margin:%?-140?% %?20?% %?20?%;border-radius:4px}.store-wrap .delivery-box[data-v-226c1b28]{padding:%?20?%;border-radius:4px}.store-wrap .store-info[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.store-wrap .store-info .icon[data-v-226c1b28]{width:%?60?%;height:%?60?%;border-radius:50%;position:relative;margin-right:%?26?%;-webkit-align-self:flex-start;align-self:flex-start;margin-top:%?12?%}.store-wrap .store-info .icon.image-icon[data-v-226c1b28]{background-color:unset}.store-wrap .store-info .icon.image-icon uni-image[data-v-226c1b28]{width:100%;height:100%}.store-wrap .store-info .icon .iconfont[data-v-226c1b28]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);font-size:%?32?%;color:#fff}.store-wrap .store-info .store-info-detail[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1}.store-wrap .store-info .store-info-detail .store-detail uni-view[data-v-226c1b28]{word-break:break-word;font-size:%?24?%}.store-wrap .store-info .cell-more[data-v-226c1b28]{margin-left:%?50?%}.store-wrap .pick-block[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-top:%?20?%;padding-top:%?20?%;border-top:%?2?% solid #f1f1f1}.store-wrap .pick-block > uni-view[data-v-226c1b28]{-webkit-box-flex:1;-webkit-flex:1;flex:1}.store-wrap .pick-block .time-picker[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end}.store-wrap .pick-block .time-picker uni-view[data-v-226c1b28]{text-align:center;color:#898989}.tabs[data-v-226c1b28]{border-radius:%?23?%;background:hsla(0,0%,100%,.3);display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:%?70?%;position:relative;margin-bottom:%?20?%}.tabs > uni-view[data-v-226c1b28]{color:#fff;text-align:center;font-size:%?22?%;-webkit-box-flex:1;-webkit-flex:1;flex:1;height:100%;line-height:%?70?%;opacity:.7;position:relative}.tabs > uni-view.active[data-v-226c1b28]{background-color:#fff!important;opacity:1;-webkit-transform:scaleY(1.2);transform:scaleY(1.2);z-index:2;transform-origin:0 100%;-webkit-transform-origin:0 100%;border-top-left-radius:4px;border-top-right-radius:4px}.tabs > uni-view.active .content[data-v-226c1b28]{color:#333;-webkit-transform:scaleY(.83) translate3d(-50%,-50%,0);transform:scaleY(.83) translate3d(-50%,-50%,0)}.tabs > uni-view .content[data-v-226c1b28]{position:absolute;top:50%;left:50%;-webkit-transform:translate3d(-50%,-50%,0);transform:translate3d(-50%,-50%,0);color:#fff}.tabs > uni-view:not(:first-of-type):not(:last-of-type).active[data-v-226c1b28]:before{content:"";position:absolute;top:%?1?%;width:0;height:0;border-bottom:%?70?% solid #fff;z-index:2;left:calc(100% - %?1?%);border-right:16px solid transparent}.tabs > uni-view:not(:first-of-type):not(:last-of-type).active[data-v-226c1b28]:after{content:"";position:absolute;top:%?1?%;width:0;height:0;border-bottom:%?70?% solid #fff;z-index:2;right:calc(100% - %?1?%);border-left:16px solid transparent}.tabs > uni-view:first-of-type.active[data-v-226c1b28]:before{content:"";position:absolute;top:%?1?%;width:0;height:0;border-bottom:%?70?% solid #fff;z-index:2;left:calc(100% - %?1?%);border-right:16px solid transparent}.tabs > uni-view:last-of-type.active[data-v-226c1b28]:after{content:"";position:absolute;top:%?1?%;width:0;height:0;border-bottom:%?70?% solid #fff;z-index:2;right:calc(100% - %?1?%);border-left:16px solid transparent}.text-right[data-v-226c1b28]{text-align:right}.sku[data-v-226c1b28]{display:-webkit-box;display:-webkit-flex;display:flex;line-height:1;margin-top:%?10?%;margin-bottom:%?10?%}.goods-spec[data-v-226c1b28]{color:#838383;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;-webkit-box-flex:1;-webkit-flex:1;flex:1}.address-empty[data-v-226c1b28]{line-height:%?100?%;text-align:center}body.?%PAGE?%[data-v-226c1b28]{background-color:#f7f7f7}',""]),e.exports=t},cbd7:function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,"[data-v-226c1b28] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none;max-height:unset!important;overflow-y:hidden!important}[data-v-226c1b28] .uni-popup__wrapper{border-radius:%?20?% %?20?% 0 0}[data-v-226c1b28] .uni-popup{z-index:8}",""]),e.exports=t},d276:function(e,t,a){"use strict";a.r(t);var i=a("55ee"),o=a("7c82");for(var r in o)"default"!==r&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("2d82"),a("62ff");var n,s=a("f0c5"),d=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"226c1b28",null,!1,i["a"],n);t["default"]=d.exports},d806:function(e,t,a){"use strict";a.r(t);var i=a("2126"),o=a("30ef");for(var r in o)"default"!==r&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("f673");var n,s=a("f0c5"),d=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"6d66adde",null,!1,i["a"],n);t["default"]=d.exports},e8b1:function(e,t,a){var i=a("8efb");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("4f06").default;o("8981c87e",i,!0,{sourceMap:!1,shadowMode:!1})},f029:function(e,t,a){"use strict";a("4160"),a("c975"),a("b6802"),a("b64b"),a("d3b7"),a("acd8"),a("e25e"),a("25f0"),a("159b"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isIphoneX:!1,orderCreateData:{is_balance:0,pay_password:"",is_invoice:0,invoice_type:1,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:""},orderPaymentData:{shop_goods_list:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]}},topic_info:{},member_account:{balance:0,is_pay_password:0},local_config:{info:{start_time:0,end_time:0,time_week:[]}}},isSub:!1,tempData:null,storeInfo:{storeList:[],currStore:{}},member_address:{mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTimeBar:!1},canLocalDelicery:!0,isFocus:!1}},methods:{openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){this.$util.redirectTo("/otherpages/member/address/address",{back:"/promotionpages/topics/payment/payment"})},getOrderPaymentData:function(){var e=this;this.orderCreateData=uni.getStorageSync("topicOrderCreateData");var t=uni.getStorageSync("location");t&&(this.orderCreateData=Object.assign(this.orderCreateData,t));var a=uni.getStorageSync("store");a&&(this.orderCreateData.default_store_id=a.store_id),this.orderCreateData?this.$api.sendRequest({url:"/topic/api/ordercreate/payment",data:this.orderCreateData,success:function(t){t.code>=0?(e.orderPaymentData=t.data,e.handlePaymentData(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){e.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}),1500)}})},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}}):this.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){e.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}),1500)}})},handlePaymentData:function(){var e=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.buyer_message="",this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="",this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="";var t=this.orderPaymentData;if(void 0!=t.shop_goods_list.express_type&&void 0!=t.shop_goods_list.express_type[0]){var a=t.shop_goods_list.express_type;this.orderCreateData.delivery.store_id=0;var i=uni.getStorageSync("delivery");if(i){var o=i.name,r=i.title;"store"==o&&t.shop_goods_list.express_type.forEach((function(t){"store"==t.name&&e.storeSelected(t)}))}else o=a[0].name,r=a[0].title;this.orderCreateData.delivery.delivery_type=o,this.orderCreateData.delivery.delivery_type_name=r,"store"==a[0].name&&this.storeSelected(a[0])}if(void 0!=t.shop_goods_list.coupon_list&&void 0!=t.shop_goods_list.coupon_list[0]){var n=t.shop_goods_list.coupon_list;this.orderCreateData.coupon.coupon_id=n[0].coupon_id,this.orderCreateData.coupon.coupon_money=n[0].money}if(this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={mobile:""}),this.orderPaymentData.shop_goods_list.invoice){var s=this.orderPaymentData.shop_goods_list.invoice.invoice_content_array;s.length&&(this.orderCreateData.invoice_content=s[0])}if(0==this.orderPaymentData.is_virtual&&this.orderPaymentData.shop_goods_list.local_config.info&&1==this.orderPaymentData.shop_goods_list.local_config.info.time_is_open){this.timeInfo.showTimeBar=!0,0==this.orderPaymentData.shop_goods_list.local_config.info.time_week.length||7==this.orderPaymentData.shop_goods_list.local_config.info.time_week.length||this.orderPaymentData.shop_goods_list.local_config.info.time_week.indexOf(this.timeInfo.week)>-1?this.canLocalDelicery=!0:this.canLocalDelicery=!1;var d=(new Date).getHours().toString(),c=(new Date).getMinutes().toString();1==d.length&&(d="0"+d),1==c.length&&(c="0"+c),this.orderCreateData.buyer_ask_delivery_time=d+":"+c;var l=this.orderPaymentData.shop_goods_list.local_config.info.start_time;this.timeInfo.start_time=this.getTimeStr(l);var b=this.orderPaymentData.shop_goods_list.local_config.info.end_time;this.timeInfo.end_time=this.getTimeStr(b)}Object.assign(this.orderPaymentData,this.orderCreateData),this.orderPaymentData.shop_goods_list.goods_list.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]})),this.orderCalculate()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),a=parseInt(e%3600/60).toString();return 1==a.length&&(a="0"+a),1==t.length&&(t="0"+t),t+":"+a},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/topic/api/ordercreate/calculate",data:t,success:function(t){t.code>=0?(e.orderPaymentData.delivery_money=t.data.delivery_money,e.orderPaymentData.coupon_money=t.data.coupon_money,e.orderPaymentData.invoice_money=t.data.invoice_money,e.orderPaymentData.invoice_delivery_money=t.data.shop_goods_list.invoice_delivery_money,e.orderPaymentData.promotion_money=t.data.promotion_money,e.orderPaymentData.order_money=t.data.order_money,e.orderPaymentData.balance_money=t.data.balance_money,e.orderPaymentData.pay_money=t.data.pay_money,e.orderPaymentData.goods_money=t.data.goods_money,e.$forceUpdate()):e.$util.showToast({title:t.message})}})},orderCreate:function(){var e=this;if(this.verify()){if(this.isSub)return;this.isSub=!0;var t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/topic/api/ordercreate/create",data:t,success:function(t){t.code>=0?uni.removeStorage({key:"topicOrderCreateData",success:function(){0==e.orderPaymentData.pay_money?e.$util.redirectTo("/pages/pay/result/result",{code:t.data},"redirectTo"):e.$util.redirectTo("/pages/pay/index/index",{code:t.data},"redirectTo")}}):(e.isSub=!1,uni.hideLoading(),e.$refs.payPassword&&e.$refs.payPassword.close(),10==t.data.error_code||12==t.data.error_code?uni.showModal({title:"订单未创建",content:t.message,confirmText:"去设置",success:function(t){t.confirm&&e.selectAddress()}}):e.$util.showToast({title:t.message}))}})}},verify:function(){var e=this;if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;var t=/^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;if(!t.test(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if("store"!=this.orderCreateData.delivery.delivery_type&&!this.orderPaymentData.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("{}"==JSON.stringify(this.orderCreateData.delivery))return this.$util.showToast({title:"店铺未设置配送方式"}),!1;if("store"==this.orderCreateData.delivery.delivery_type&&0==this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"店铺没有可提货的门店,请选择其他配送方式"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;t=/^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;if(!t.test(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1}}return!(1==this.orderCreateData.is_invoice&&!this.invoiceVerify())&&(1!=this.orderCreateData.is_balance||""!=this.orderCreateData.pay_password||(this.$refs.input&&setTimeout((function(){e.$refs.input.clear()}),0),this.openPasswordPopup(),!1))},openSitePromotion:function(){this.$refs.sitePromotionPopup.open()},openSiteDelivery:function(){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.$refs.deliveryPopup.open()},selectDeliveryType:function(e){uni.setStorageSync("delivery",{title:e.title,name:e.name}),this.orderCreateData.delivery.delivery_type=e.name,this.orderCreateData.delivery.delivery_type_name=e.title,"store"==e.name&&this.storeSelected(e),Object.assign(this.orderPaymentData,this.orderCreateData),this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){if(this.storeInfo.storeList=e.store_list,!(this.orderCreateData.delivery.store_id>0)){var t=uni.getStorageSync("store");t&&e.store_id==t.store_id?(this.storeInfo.currStore=t,this.orderCreateData.delivery.store_id=this.storeInfo.currStore.store_id):void 0!=e.store_list[0]?(this.storeInfo.currStore=e.store_list[0],this.orderCreateData.delivery.store_id=e.store_list[0].store_id):this.storeInfo.currStore=null}},selectPickupPoint:function(e){this.orderCreateData.delivery.store_id=e.store_id,this.storeInfo.currStore=e,Object.assign(this.orderPaymentData,this.orderCreateData),this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},openSiteCoupon:function(){this.tempData={coupon:this.$util.deepClone(this.orderPaymentData.coupon)},this.$refs.couponPopup.open()},selectCoupon:function(e){this.orderCreateData.coupon.coupon_id!=e.coupon_id?(this.orderCreateData.coupon.coupon_id=e.coupon_id,this.orderCreateData.coupon.coupon_money=e.money):(this.orderCreateData.coupon.coupon_id=0,this.orderCreateData.coupon.coupon_money="0.00"),Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},popupConfirm:function(e){this.$refs[e].close(),this.orderCalculate(),this.$forceUpdate(),this.tempData=null},useBalance:function(){this.orderCreateData.is_balance?this.orderCreateData.is_balance=0:this.orderCreateData.is_balance=1,this.orderCalculate(),this.$forceUpdate()},setPayPassword:function(){this.$util.redirectTo("/otherpages/member/pay_password/pay_password",{back:"/promotionpages/topics/payment/payment"})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(e){var t=this;6==e.length&&(uni.showLoading({title:"支付中...",mask:!0}),this.$api.sendRequest({url:"/api/member/checkpaypassword",data:{pay_password:e},success:function(a){a.code>=0?(t.orderCreateData.pay_password=e,t.orderCreate()):(uni.hideLoading(),t.$util.showToast({title:a.message}))},fail:function(e){uni.hideLoading()}}))},imageError:function(e){this.orderPaymentData.shop_goods_list.goods_list[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},navigateBack:function(){this.$util.goBack()},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?this.orderCreateData.is_invoice=1:this.orderCreateData.is_invoice=0,this.orderCalculate(),this.$forceUpdate()},changeInvoiceType:function(e){this.orderCreateData.invoice_type=e,this.orderCalculate(),this.$forceUpdate()},changeInvoiceTitleType:function(e){this.orderCreateData.invoice_title_type=e,this.orderCalculate(),this.$forceUpdate()},changeIsTaxInvoice:function(){0==this.orderCreateData.is_tax_invoice?this.orderCreateData.is_tax_invoice=1:this.orderCreateData.is_tax_invoice=0,this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票抬头"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写纳税人识别号"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票邮寄地址"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写邮箱"}),!1;if(2==this.orderCreateData.invoice_type){var e=/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;if(!e.test(this.orderCreateData.invoice_email))return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写正确的邮箱"}),!1}return!!this.orderCreateData.invoice_content||(this.$refs.invoicePopup.open(),this.$util.showToast({title:"请选择发票内容"}),!1)},saveInvoice:function(){1==this.orderCreateData.is_invoice?this.invoiceVerify()&&this.closePopup("invoicePopup"):this.closePopup("invoicePopup")},bindTimeChange:function(e){var t=e.detail.value;this.orderCreateData.buyer_ask_delivery_time=t,this.orderCalculate(),this.$forceUpdate()},getTime:function(){var e=["0","1","2","3","4","5","6"],t=(new Date).getDay();this.timeInfo.week=e[t]},closeInvoicePopup:function(){this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="",this.orderCreateData.taxpayer_number="",this.orderCreateData.invoice_content="",this.orderCreateData.invoice_full_address="",this.orderCreateData.invoice_email="",this.orderCalculate(),this.$forceUpdate(),this.$refs.invoicePopup.close()},openPasswordPopup:function(){var e=this;this.$refs.payPassword.open(),setTimeout((function(){e.isFocus=!0}),500)},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail/detail",{sku_id:e})}},onShow:function(){this.$langConfig.refresh(),uni.getStorageSync("token")?this.getOrderPaymentData():this.$util.redirectTo("/pages/login/login/login"),this.getTime(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},computed:{balanceDeduct:function(){return this.orderPaymentData.member_account.balance_total<=parseFloat(this.orderPaymentData.order_money).toFixed(2)?parseFloat(this.orderPaymentData.member_account.balance_total).toFixed(2):parseFloat(this.orderPaymentData.order_money).toFixed(2)}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)},promotion:function(e){var t="";return e&&Object.keys(e).forEach((function(a){t+=e[a].content+"　"})),t}}};t.default=i},f673:function(e,t,a){"use strict";var i=a("e8b1"),o=a.n(i);o.a},f7d1:function(e,t,a){"use strict";var i,o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"code-box"},[a("v-uni-view",{staticClass:"flex-box"},[a("v-uni-input",{staticClass:"hide-input",attrs:{value:e.inputValue,type:"number",focus:e.autoFocus,maxlength:e.maxlength},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.getVal.apply(void 0,arguments)}}}),e._l(e.ranges,(function(t,i){return[a("v-uni-view",{key:i+"_0",class:["item",{active:e.codeIndex===t,middle:"middle"===e.type,bottom:"bottom"===e.type,box:"box"===e.type}]},["middle"!==e.type?a("v-uni-view",{staticClass:"line"}):e._e(),"middle"===e.type&&e.codeIndex<=t?a("v-uni-view",{staticClass:"bottom-line"}):e._e(),e.isPwd&&e.codeArr.length>=t?[a("v-uni-text",{staticClass:"dot"},[e._v("●")])]:[a("v-uni-text",{staticClass:"number"},[e._v(e._s(e.codeArr[i]?e.codeArr[i]:""))])]],2)]}))],2)],1)},r=[];a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}))}}]);